import pool from '../config/db.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function runMigration() {
  console.log('🚀 Starting Entry Activity Tracking Migration...\n');

  const client = await pool.connect();

  try {
    await client.query('BEGIN');

    // Step 1: Run basic schema changes
    console.log('📝 Step 1: Adding is_active column and indexes...');
    const schema = fs.readFileSync(
      path.join(__dirname, 'migrations', 'add_entry_activity_tracking_fixed.sql'),
      'utf8'
    );
    await client.query(schema);
    console.log('✅ Schema changes completed');

    // Step 2: Create function and trigger
    console.log('\n📝 Step 2: Creating status tracking function and trigger...');
    const functionSql = fs.readFileSync(
      path.join(__dirname, 'migrations', 'minimal_function.sql'),
      'utf8'
    );
    await client.query(functionSql);
    console.log('✅ Function and trigger created');

    // Step 3: Create views
    console.log('\n📝 Step 3: Creating active/retired entry views...');
    const viewsSql = fs.readFileSync(
      path.join(__dirname, 'migrations', 'add_entry_activity_views.sql'),
      'utf8'
    );
    await client.query(viewsSql);
    console.log('✅ Views created');

    // Step 4: Verify the migration
    console.log('\n🔍 Step 4: Verifying migration...');

    // Check if column exists
    const columnCheck = await client.query(`
      SELECT column_name
      FROM information_schema.columns
      WHERE table_name = 'results' AND column_name = 'is_active'
    `);

    if (columnCheck.rows.length === 0) {
      throw new Error('is_active column was not created');
    }

    // Check if table exists
    const tableCheck = await client.query(`
      SELECT table_name
      FROM information_schema.tables
      WHERE table_name = 'entry_status_history'
    `);

    if (tableCheck.rows.length === 0) {
      throw new Error('entry_status_history table was not created');
    }

    // Check if views exist
    const viewsCheck = await client.query(`
      SELECT table_name
      FROM information_schema.views
      WHERE table_name IN ('active_entries', 'retired_entries')
    `);

    if (viewsCheck.rows.length < 2) {
      throw new Error('Views were not created properly');
    }

    // Check if trigger exists
    const triggerCheck = await client.query(`
      SELECT trigger_name
      FROM information_schema.triggers
      WHERE trigger_name = 'trigger_track_entry_status_change'
    `);

    if (triggerCheck.rows.length === 0) {
      throw new Error('Trigger was not created');
    }

    await client.query('COMMIT');
    console.log('✅ Migration verification passed');

    // Step 5: Show summary
    console.log('\n📊 Migration Summary:');

    const entriesCount = await pool.query('SELECT COUNT(*) as count FROM entries');
    console.log(`   Total entries: ${entriesCount.rows[0].count}`);

    const resultsCount = await pool.query(`
      SELECT
        COUNT(*) as total_results,
        COUNT(CASE WHEN is_active = true THEN 1 END) as active_results,
        COUNT(CASE WHEN is_active = false THEN 1 END) as inactive_results
      FROM results
    `);
    console.log(`   Total results: ${resultsCount.rows[0].total_results}`);
    console.log(`   Active results: ${resultsCount.rows[0].active_results}`);
    console.log(`   Inactive results: ${resultsCount.rows[0].inactive_results}`);

    console.log('\n🎉 Entry Activity Tracking Migration completed successfully!');
    console.log('\n📋 What was added:');
    console.log('   ✅ is_active column in results table');
    console.log('   ✅ entry_status_history table for tracking changes');
    console.log('   ✅ Automatic trigger for status change tracking');
    console.log('   ✅ active_entries and retired_entries views');
    console.log('   ✅ Performance indexes');

  } catch (error) {
    await client.query('ROLLBACK');
    console.error('❌ Migration failed:', error.message);
    console.error('Full error:', error);
    throw error;
  } finally {
    client.release();
  }
}

// Run the migration
runMigration()
  .then(() => {
    console.log('\n✨ Migration script completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n💥 Migration script failed:', error.message);
    process.exit(1);
  });
