-- Setup Rally Stereas Elladas 2025 championship class filtering
-- This will ensure entries only appear in appropriate championships

-- First, let's see what we're working with
SELECT 'Current Championships for Rally Stereas Elladas 2025:' as info;
SELECT 
    c.name as championship,
    c.id as championship_id
FROM championships c
JOIN championship_events ce ON c.id = ce.championship_id
JOIN rallies r ON ce.rally_id = r.id
WHERE r.name = 'Rally Stereas Elladas 2025';

SELECT 'Current Entry Classes in Rally Stereas Elladas 2025:' as info;
SELECT DISTINCT 
    e.class,
    COUNT(*) as entry_count
FROM entries e
JOIN rallies r ON e.rally_id = r.id
WHERE r.name = 'Rally Stereas Elladas 2025'
GROUP BY e.class
ORDER BY entry_count DESC;

-- Now set up class filtering for each championship
-- You'll need to replace these championship IDs with your actual ones

-- Example setup (adjust championship IDs and classes based on your data):

-- 1. WRC Championship - only top-level WRC cars
DELETE FROM championship_classes WHERE championship_id = 'wrc-2025';
INSERT INTO championship_classes (championship_id, class_name) VALUES
('wrc-2025', 'WRC'),
('wrc-2025', 'Rally1');

-- 2. WRC2/R5 Championship - R5 and Rally2 cars
DELETE FROM championship_classes WHERE championship_id = 'wrc2-2025';
INSERT INTO championship_classes (championship_id, class_name) VALUES
('wrc2-2025', 'WRC2'),
('wrc2-2025', 'R5'),
('wrc2-2025', 'Rally2');

-- 3. Greek National Championship - most classes but not historic
DELETE FROM championship_classes WHERE championship_id = 'greek-national-2025';
INSERT INTO championship_classes (championship_id, class_name) VALUES
('greek-national-2025', 'WRC'),
('greek-national-2025', 'WRC2'),
('greek-national-2025', 'R5'),
('greek-national-2025', 'Rally2'),
('greek-national-2025', 'N4'),
('greek-national-2025', 'R2'),
('greek-national-2025', 'R3'),
('greek-national-2025', 'A6'),
('greek-national-2025', 'A7'),
('greek-national-2025', 'A8');

-- 4. Historic Championship - only historic classes
DELETE FROM championship_classes WHERE championship_id = 'historic-2025';
INSERT INTO championship_classes (championship_id, class_name) VALUES
('historic-2025', 'Historic'),
('historic-2025', 'H1'),
('historic-2025', 'H2'),
('historic-2025', 'H3'),
('historic-2025', 'H4');

-- Verify the setup
SELECT 'Championship Class Mappings:' as info;
SELECT 
    c.name as championship,
    cc.class_name,
    COUNT(e.id) as matching_entries
FROM championship_classes cc
JOIN championships c ON cc.championship_id = c.id
LEFT JOIN championship_events ce ON c.id = ce.championship_id
LEFT JOIN rallies r ON ce.rally_id = r.id AND r.name = 'Rally Stereas Elladas 2025'
LEFT JOIN entries e ON r.id = e.rally_id AND e.class LIKE '%' || cc.class_name || '%'
GROUP BY c.name, cc.class_name
ORDER BY c.name, cc.class_name;

-- Test the filtered results
SELECT 'Entries per Championship after filtering:' as info;
SELECT 
    championship_name,
    COUNT(*) as entry_count,
    STRING_AGG(DISTINCT class, ', ') as classes_included
FROM championship_overall_classification coc
JOIN rallies r ON coc.rally_id = r.id
WHERE r.name = 'Rally Stereas Elladas 2025'
GROUP BY championship_id, championship_name
ORDER BY championship_name;
