import express from 'express';
import cors from 'cors';
import cookieParser from 'cookie-parser';
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';
import authRoutes from './routes/auth.js';
import rallyRoutes from './routes/rallies.js';
import newsRoutes from './routes/news.js';
import driverRoutes from './routes/drivers.js';
import driverStatsRoutes from './routes/driverStats.js';
import penaltyRoutes from './routes/penalties.js';
import stageRoutes from './routes/stages.js';
import stageResultsRoutes from './routes/stageResults.js';
import entryRoutes from './routes/entries.js';
import teamsRoutes from './routes/teams.js';
import championshipsRoutes from './routes/championships.js';
import powerStagePointsRoutes from './routes/powerStagePoints.js';
import splitsRoutes from './routes/splits.js';
import codriversRoutes from './routes/codrivers.js';
import personsRoutes from './routes/persons.js';
import shakedownsRoutes from './routes/shakedowns.js';
import itineraryRoutes from './routes/itinerary.js';
import rallyChampionshipsRoutes from './routes/rallyChampionships.js';
import entryClassesRoutes from './routes/entryClasses.js';
import entryStatusHistoryRoutes from './routes/entryStatusHistory.js';
import championshipResultsRoutes from './routes/championshipResults.js';
import { verifyToken } from './middleware/auth.js';
import pool from './config/db.js';

dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();

app.use(cors({
  origin: process.env.CLIENT_URL || 'http://localhost:5173',
  credentials: true
}));
app.use(express.json());
app.use(cookieParser());

// Routes
app.use('/api/auth', authRoutes);
app.use('/api/rallies', rallyRoutes);
app.use('/api/news', newsRoutes);
app.use('/api/drivers', driverRoutes);
app.use('/api/driverStats', driverStatsRoutes);
app.use('/api/penalties', penaltyRoutes);
app.use('/api/stages', stageRoutes);
app.use('/api/stageResults', stageResultsRoutes);
app.use('/api/entries', entryRoutes);
app.use('/api/teams', teamsRoutes);
app.use('/api/championships', championshipsRoutes);
app.use('/api/powerStagePoints', powerStagePointsRoutes);
app.use('/api/splits', splitsRoutes);
app.use('/api/codrivers', codriversRoutes);
app.use('/api/persons', personsRoutes);
app.use('/api/shakedowns', shakedownsRoutes);
app.use('/api/itinerary', itineraryRoutes);
app.use('/api/rally-championships', rallyChampionshipsRoutes);
app.use('/api/entry-classes', entryClassesRoutes);
app.use('/api/entry-status-history', entryStatusHistoryRoutes);
app.use('/api/championship-results', championshipResultsRoutes);

app.get('/api/db-health', async (req, res) => {
  try {
    await pool.query('SELECT 1');
    res.json({ connected: true });
  } catch (err) {
    res.status(500).json({ connected: false, error: err.message });
  }
});

// Serve static files from the React app build directory
app.use(express.static(path.join(__dirname, '../dist')));

// Catch-all handler: send back React's index.html file for any non-API routes
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, '../dist/index.html'));
});

const PORT = process.env.PORT || 5000;

app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});