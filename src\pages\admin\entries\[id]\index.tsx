import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ClipboardList } from 'lucide-react';
import EntryForm from '../../../../components/admin/entry-form';

interface Entry {
  id: string;
  rally_id: string;
  driver_id: string;
  codriver_id: string;
  car: string;
  team_id: string | null;
  number: number;
  class: string;
  status: string;
  driver_first_name?: string;
  driver_last_name?: string;
  codriver_first_name?: string;
  codriver_last_name?: string;
  team_name?: string;
}

const EditEntryPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [entry, setEntry] = useState<Entry | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const navigate = useNavigate();

  useEffect(() => {
    if (id) {
      fetchEntry();
    }
  }, [id]);

  const fetchEntry = async () => {
    try {
      const res = await fetch(`/api/entries/${id}`, { credentials: 'include' });
      if (!res.ok) throw new Error('Failed to fetch entry');
      const data = await res.json();
      setEntry(data);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (formData: any) => {
    try {
      console.log('Updating entry with data:', formData);

      // Validate that all required fields are present
      if (!formData.rally_id || !formData.driver_id || !formData.codriver_id || !formData.car || !formData.number || !formData.class || !formData.status) {
        console.error('Missing required fields in form data:', formData);
        throw new Error('Please fill in all required fields');
      }

      // Ensure number is a valid integer
      if (isNaN(parseInt(formData.number))) {
        console.error('Invalid number value:', formData.number);
        throw new Error('Entry number must be a valid number');
      }

      const response = await fetch(`/api/entries/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify(formData)
      });

      // Log the response for debugging
      console.log('Response status:', response.status);

      try {
        const data = await response.json();
        console.log('Response data:', data);

        if (!response.ok) {
          console.error('Error response from server:', data);
          throw new Error(data.message || data.error || 'Failed to update entry');
        }

        navigate('/admin/entries');
      } catch (jsonError) {
        console.error('Error parsing JSON response:', jsonError);
        if (!response.ok) {
          throw new Error(`Failed to update entry. Server returned status ${response.status}`);
        }
      }
    } catch (error: any) {
      console.error('Error in handleSubmit:', error);
      setError(error.message || 'An unexpected error occurred');
      throw error; // Re-throw to be caught by the form's error handler
    }
  };

  const handleDelete = async () => {
    const response = await fetch(`/api/entries/${id}`, {
      method: 'DELETE',
      credentials: 'include',
    });

    if (!response.ok) {
      const data = await response.json();
      throw new Error(data.message || data.error || 'Failed to delete entry');
    }

    navigate('/admin/entries');
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8 text-center">
        <div className="inline-block animate-spin rounded-full h-8 w-8 border-4 border-gray-300 border-t-red-600"></div>
        <p className="mt-2 text-gray-600 dark:text-gray-400">Loading entry...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="p-4 bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-200 rounded">
          {error}
        </div>
      </div>
    );
  }

  if (!entry) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="p-4 bg-yellow-100 dark:bg-yellow-900 text-yellow-700 dark:text-yellow-200 rounded">
          Entry not found
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center mb-6">
        <ClipboardList className="w-6 h-6 text-red-600 mr-2" />
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          Edit Entry: #{entry.number} - {entry.driver_first_name} {entry.driver_last_name}
        </h1>
      </div>

      <EntryForm
        initialData={entry}
        onSubmit={handleSubmit}
        onDelete={handleDelete}
      />
    </div>
  );
};

export default EditEntryPage;
