import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { AlertTriangle } from 'lucide-react';
import PenaltyForm from '../../../../components/admin/penalty-form';

const AddPenaltyPage: React.FC = () => {
  const [error, setError] = useState('');
  const navigate = useNavigate();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const entryId = queryParams.get('entry_id');
  const stageId = queryParams.get('stage_id');

  const handleSubmit = async (formData: any) => {
    console.log('Submitting penalty with data:', formData);
    
    const response = await fetch('/api/penalties', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      credentials: 'include',
      body: JSON.stringify(formData)
    });

    // Log the response for debugging
    console.log('Response status:', response.status);
    
    if (!response.ok) {
      const data = await response.json();
      console.error('Error response:', data);
      throw new Error(data.message || data.error || 'Failed to add penalty');
    }

    // If we came from an entry page, go back there
    if (entryId) {
      navigate(`/admin/entries/${entryId}`);
    } else if (stageId) {
      // If we have a stage ID, go back to the stage page
      navigate(`/admin/stages/${stageId}`);
    } else {
      navigate('/admin/penalties');
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center mb-6">
        <AlertTriangle className="w-6 h-6 text-red-600 mr-2" />
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Add Penalty</h1>
      </div>

      {error && (
        <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          {error}
        </div>
      )}

      <PenaltyForm 
        onSubmit={handleSubmit} 
        preselectedEntryId={entryId || undefined}
        preselectedStageId={stageId || undefined}
      />
    </div>
  );
};

export default AddPenaltyPage;
