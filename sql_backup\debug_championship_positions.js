import pool from './server/config/db.js';

async function debugChampionshipPositions() {
  try {
    console.log('=== Debugging Championship Position Calculation ===');
    
    // Get Rally Stereas Elladas 2025 ID
    const rally = await pool.query(
      "SELECT id FROM rallies WHERE name LIKE '%Stereas Elladas 2025%'"
    );
    const rallyId = rally.rows[0].id;
    
    // Get Greece championship ID
    const championship = await pool.query(
      "SELECT id FROM championships WHERE name = 'Greece'"
    );
    const championshipId = championship.rows[0].id;
    
    console.log('Rally ID:', rallyId);
    console.log('Greece Championship ID:', championshipId);
    
    // Check entries registered for Greece championship
    const greecEntries = await pool.query(`
      SELECT 
        e.number,
        CONCAT(pd.first_name, ' ', pd.last_name) as driver,
        e.class,
        e.car,
        oc.position as overall_position,
        oc.total_time
      FROM entry_championships ec
      JOIN entries e ON ec.entry_id = e.id
      JOIN persons pd ON e.driver_id = pd.id
      JOIN overall_classification oc ON e.id = oc.entry_id
      WHERE e.rally_id = $1 AND ec.championship_id = $2
      ORDER BY oc.position
    `, [rallyId, championshipId]);
    
    console.log(`\nEntries registered for Greece Championship (${greecEntries.rows.length} total):`);
    greecEntries.rows.forEach((entry, index) => {
      const championshipPosition = index + 1;
      const points = championshipPosition <= 10 ? 
        [25, 18, 15, 12, 10, 8, 6, 4, 2, 1][championshipPosition - 1] : 0;
      console.log(`  ${championshipPosition}. #${entry.number} ${entry.driver} (Overall: ${entry.overall_position}) - ${entry.class} - ${points} pts`);
    });
    
    // Check what the current view is showing
    const currentView = await pool.query(`
      SELECT 
        driver,
        position,
        championship_points,
        total_time
      FROM championship_overall_classification_new
      WHERE rally_id = $1 AND championship_id = $2
      ORDER BY position
      LIMIT 10
    `, [rallyId, championshipId]);
    
    console.log('\nCurrent championship view results:');
    currentView.rows.forEach(row => {
      console.log(`  ${row.position}. ${row.driver} - ${row.championship_points} pts`);
    });
    
    // Find Papadimitriou specifically
    const papadimitriou = greecEntries.rows.find(entry => entry.driver.includes('Papadimitriou'));
    if (papadimitriou) {
      const correctPosition = greecEntries.rows.findIndex(entry => entry.driver.includes('Papadimitriou')) + 1;
      const correctPoints = correctPosition <= 10 ? 
        [25, 18, 15, 12, 10, 8, 6, 4, 2, 1][correctPosition - 1] : 0;
      
      console.log(`\n🔍 Papadimitriou Analysis:`);
      console.log(`  Overall rally position: ${papadimitriou.overall_position}`);
      console.log(`  Correct Greece championship position: ${correctPosition}`);
      console.log(`  Should get: ${correctPoints} points`);
      console.log(`  Currently getting: 0 points (WRONG!)`);
    }
    
    process.exit(0);
  } catch (error) {
    console.error('Error debugging championship positions:', error);
    process.exit(1);
  }
}

debugChampionshipPositions();
