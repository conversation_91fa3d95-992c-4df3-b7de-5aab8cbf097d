import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { getCountryCode } from '../../utils/countryUtils';
import { useAuth } from '../../context/AuthContext';

interface PersonFormProps {
  initialData?: any;
  onSubmit?: (formData: any) => Promise<void>;
  onDelete?: () => Promise<void>;
}

const PersonForm: React.FC<PersonFormProps> = ({
  initialData,
  onSubmit,
  onDelete
}) => {
  const [form, setForm] = useState({
    first_name: '',
    last_name: '',
    nationality: '',
    date_of_birth: '',
    photo_url: '',
    bio: ''
  });
  const [flagCode, setFlagCode] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const navigate = useNavigate();
  const { user, isAdmin } = useAuth();

  useEffect(() => {
    if (initialData) {
      setForm({
        first_name: initialData.first_name || '',
        last_name: initialData.last_name || '',
        nationality: initialData.nationality || '',
        date_of_birth: initialData.date_of_birth ? initialData.date_of_birth.slice(0, 10) : '',
        photo_url: initialData.photo_url || '',
        bio: initialData.bio || ''
      });
    }
  }, [initialData]);

  // Update flag code when nationality changes
  useEffect(() => {
    setFlagCode(getCountryCode(form.nationality));
  }, [form.nationality]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const validateForm = () => {
    const errors: Record<string, string> = {};

    if (!form.first_name.trim()) errors.first_name = 'First name is required';
    if (!form.last_name.trim()) errors.last_name = 'Last name is required';
    if (!form.nationality.trim()) errors.nationality = 'Nationality is required';

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    // Check authentication before submitting
    if (!user || !isAdmin) {
      setError('Authentication required. Please log in as an admin.');
      window.scrollTo(0, 0);
      return;
    }

    setLoading(true);
    setError('');

    try {
      // Format date to ISO string if present
      const formattedData = {
        ...form,
        date_of_birth: form.date_of_birth ? new Date(form.date_of_birth).toISOString().split('T')[0] : null
      };

      if (onSubmit) {
        await onSubmit(formattedData);
      } else {
        // Default submission logic if no onSubmit provided
        const url = initialData ? `/api/persons/${initialData.id}` : '/api/persons';
        const method = initialData ? 'PUT' : 'POST';

        // Log the form data being sent
        console.log('Submitting person form data:', formattedData);

        try {
          const response = await fetch(url, {
            method,
            headers: {
              'Content-Type': 'application/json'
            },
            credentials: 'include',
            body: JSON.stringify(formattedData)
          });

          // Log the response
          console.log('Response status:', response.status);

          // Try to parse the response as JSON
          let responseData;
          const contentType = response.headers.get('content-type');
          if (contentType && contentType.includes('application/json')) {
            responseData = await response.json();
            console.log('Response data:', responseData);
          } else {
            const text = await response.text();
            console.log('Response text:', text);
            responseData = { message: text };
          }

          if (!response.ok) {
            throw new Error(responseData.message || responseData.error || `Failed to ${initialData ? 'update' : 'add'} person`);
          }

          navigate('/admin/persons');
        } catch (fetchError: any) {
          console.error('Fetch error:', fetchError);
          throw new Error(fetchError.message || `Network error while trying to ${initialData ? 'update' : 'add'} person`);
        }
      }
    } catch (err: any) {
      console.error('Error in form submission:', err);
      setError(err.message || `Failed to ${initialData ? 'update' : 'add'} person`);
      window.scrollTo(0, 0); // Scroll to top to show error
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!initialData || !onDelete) return;

    if (!window.confirm('Are you sure you want to delete this person?')) {
      return;
    }

    try {
      await onDelete();
    } catch (err: any) {
      setError(err.message);
    }
  };

  const handleCancel = () => {
    navigate('/admin/persons');
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      {error && (
        <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              First Name
            </label>
            <input
              name="first_name"
              value={form.first_name}
              onChange={handleChange}
              required
              placeholder="First Name"
              className={`w-full px-3 py-2 rounded border ${
                validationErrors.first_name ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
              } dark:bg-gray-700 dark:text-white`}
            />
            {validationErrors.first_name && (
              <p className="mt-1 text-sm text-red-500">{validationErrors.first_name}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Last Name
            </label>
            <input
              name="last_name"
              value={form.last_name}
              onChange={handleChange}
              required
              placeholder="Last Name"
              className={`w-full px-3 py-2 rounded border ${
                validationErrors.last_name ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
              } dark:bg-gray-700 dark:text-white`}
            />
            {validationErrors.last_name && (
              <p className="mt-1 text-sm text-red-500">{validationErrors.last_name}</p>
            )}
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Nationality
          </label>
          <div className="relative">
            <input
              name="nationality"
              value={form.nationality}
              onChange={handleChange}
              required
              placeholder="Nationality"
              className={`w-full px-3 py-2 rounded border ${
                validationErrors.nationality ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
              } dark:bg-gray-700 dark:text-white ${flagCode ? 'pl-10' : ''}`}
            />
            {flagCode && (
              <span
                className={`fi fi-${flagCode} absolute left-3 top-1/2 transform -translate-y-1/2`}
                style={{ width: '20px', height: '15px' }}
              ></span>
            )}
            {validationErrors.nationality && (
              <p className="mt-1 text-sm text-red-500">{validationErrors.nationality}</p>
            )}
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Date of Birth
          </label>
          <input
            name="date_of_birth"
            value={form.date_of_birth}
            onChange={handleChange}
            type="date"
            className="w-full px-3 py-2 rounded border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Photo URL
          </label>
          <input
            name="photo_url"
            value={form.photo_url}
            onChange={handleChange}
            placeholder="Photo URL"
            className="w-full px-3 py-2 rounded border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Biography
          </label>
          <textarea
            name="bio"
            value={form.bio}
            onChange={handleChange}
            placeholder="Biography"
            rows={4}
            className="w-full px-3 py-2 rounded border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
          />
        </div>

        <div className="flex justify-between pt-4">
          <div>
            {initialData && onDelete && (
              <button
                type="button"
                onClick={handleDelete}
                className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
              >
                Delete
              </button>
            )}
          </div>
          <div className="flex space-x-3">
            <button
              type="button"
              onClick={handleCancel}
              className="px-4 py-2 border border-gray-300 rounded text-gray-700 bg-white hover:bg-gray-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:hover:bg-gray-600"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors disabled:opacity-50"
            >
              {loading ? (initialData ? 'Saving...' : 'Adding...') : (initialData ? 'Save Changes' : 'Add Person')}
            </button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default PersonForm;
