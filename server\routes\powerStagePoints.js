import express from 'express';
import pool from '../config/db.js';
import { verifyToken, verifyAdmin } from '../middleware/auth.js';

const router = express.Router();

// Create power stage points
router.post('/', verifyToken, verifyAdmin, async (req, res) => {
  try {
    const { rally_id, stage_id, entry_id, points } = req.body;
    const result = await pool.query(
      `INSERT INTO power_stage_points (rally_id, stage_id, entry_id, points) VALUES ($1, $2, $3, $4) RETURNING *`,
      [rally_id, stage_id, entry_id, points]
    );
    res.status(201).json(result.rows[0]);
  } catch (error) {
    res.status(500).json({ message: 'Failed to add power stage points' });
  }
});

// Get all power stage points (with entry, stage, and rally info) - PUBLIC ROUTE
router.get('/', async (req, res) => {
  try {
    const result = await pool.query(`
      SELECT psp.*, e.number AS entry_number, s.name AS stage_name, r.name AS rally_name
      FROM power_stage_points psp
      JOIN entries e ON psp.entry_id = e.id
      JOIN stages s ON psp.stage_id = s.id
      JOIN rallies r ON psp.rally_id = r.id
      ORDER BY psp.rally_id, psp.stage_id, psp.points DESC
    `);
    res.json(result.rows);
  } catch (error) {
    res.status(500).json({ message: 'Failed to fetch power stage points' });
  }
});

// Get single power stage points (with entry, stage, and rally info) - PUBLIC ROUTE
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const result = await pool.query(`
      SELECT psp.*, e.number AS entry_number, s.name AS stage_name, r.name AS rally_name
      FROM power_stage_points psp
      JOIN entries e ON psp.entry_id = e.id
      JOIN stages s ON psp.stage_id = s.id
      JOIN rallies r ON psp.rally_id = r.id
      WHERE psp.id = $1
    `, [id]);
    if (result.rows.length === 0) return res.status(404).json({ message: 'Power stage points not found' });
    res.json(result.rows[0]);
  } catch (error) {
    res.status(500).json({ message: 'Failed to fetch power stage points' });
  }
});

// Update power stage points
router.put('/:id', verifyToken, verifyAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { rally_id, stage_id, entry_id, points } = req.body;
    const result = await pool.query(
      `UPDATE power_stage_points SET rally_id=$1, stage_id=$2, entry_id=$3, points=$4, updated_at=NOW() WHERE id=$5 RETURNING *`,
      [rally_id, stage_id, entry_id, points, id]
    );
    if (result.rows.length === 0) return res.status(404).json({ message: 'Power stage points not found' });
    res.json(result.rows[0]);
  } catch (error) {
    res.status(500).json({ message: 'Failed to update power stage points' });
  }
});

// Delete power stage points
router.delete('/:id', verifyToken, verifyAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const result = await pool.query('DELETE FROM power_stage_points WHERE id = $1 RETURNING *', [id]);
    if (result.rows.length === 0) return res.status(404).json({ message: 'Power stage points not found' });
    res.json({ message: 'Power stage points deleted', powerStagePoints: result.rows[0] });
  } catch (error) {
    res.status(500).json({ message: 'Failed to delete power stage points' });
  }
});

export default router;
