import React from 'react';
import { Trophy, Award, Target } from 'lucide-react';
import { ChampionshipStandings, ChampionshipClassStandings } from '../../types';
import { getCountryCode } from '../../utils/countryUtils';

interface ChampionshipStandingsTableProps {
  standings: ChampionshipStandings[] | ChampionshipClassStandings[];
  isClassStandings?: boolean;
  championshipName?: string;
  className?: string;
}

const ChampionshipStandingsTable: React.FC<ChampionshipStandingsTableProps> = ({
  standings,
  isClassStandings = false,
  championshipName,
  className = ''
}) => {
  if (standings.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500 dark:text-gray-400">
        No standings available
      </div>
    );
  }

  const getPositionIcon = (position: number) => {
    if (position === 1) return <Trophy className="w-5 h-5 text-yellow-500" />;
    if (position === 2) return <Trophy className="w-5 h-5 text-gray-400" />;
    if (position === 3) return <Trophy className="w-5 h-5 text-amber-700" />;
    return null;
  };

  const getPositionClass = (position: number) => {
    if (position === 1) return 'text-yellow-600 font-bold';
    if (position === 2) return 'text-gray-500 font-semibold';
    if (position === 3) return 'text-amber-700 font-semibold';
    return 'text-gray-900 dark:text-white';
  };

  const getOrdinalSuffix = (position: number): string => {
    const j = position % 10;
    const k = position % 100;

    if (j === 1 && k !== 11) return 'st';
    if (j === 2 && k !== 12) return 'nd';
    if (j === 3 && k !== 13) return 'rd';
    return 'th';
  };

  return (
    <div className={`overflow-x-auto ${className}`}>
      {championshipName && (
        <div className="mb-4">
          <h3 className="text-xl font-bold text-gray-900 dark:text-white">
            {championshipName} Standings
          </h3>
        </div>
      )}

      <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
        <thead className="bg-gray-50 dark:bg-gray-800">
          <tr>
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              Position
            </th>
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              Driver
            </th>
            {isClassStandings && (
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Class
              </th>
            )}
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              Rallies
            </th>
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              Rally Points
            </th>
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              Power Stage
            </th>
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              Total Points
            </th>
          </tr>
        </thead>
        <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
          {standings.map((standing) => {
            const position = isClassStandings
              ? (standing as ChampionshipClassStandings).class_position
              : (standing as ChampionshipStandings).championship_position || (standing as ChampionshipStandings).position || 0;

            const rallyPoints = isClassStandings
              ? (standing as ChampionshipClassStandings).total_points || (standing as ChampionshipClassStandings).class_points || 0
              : (standing as ChampionshipStandings).total_points;

            return (
              <tr key={`${standing.driver}-${isClassStandings ? (standing as ChampionshipClassStandings).class : ''}`}
                  className="hover:bg-gray-50 dark:hover:bg-gray-800">
                <td className="px-4 py-4 whitespace-nowrap">
                  <div className={`flex items-center space-x-2 ${getPositionClass(position)}`}>
                    {getPositionIcon(position)}
                    <span className="text-lg font-bold">
                      {position}{getOrdinalSuffix(position)}
                    </span>
                  </div>
                </td>

                <td className="px-4 py-4">
                  <div className="flex items-center space-x-3">
                    {standing.driver_nationality && (
                      <span
                        className={`fi fi-${getCountryCode(standing.driver_nationality)}`}
                        style={{ width: '20px', height: '15px' }}
                      ></span>
                    )}
                    <span className="text-sm font-medium text-gray-900 dark:text-white">
                      {standing.driver}
                    </span>
                  </div>
                </td>

                {isClassStandings && (
                  <td className="px-4 py-4 whitespace-nowrap">
                    <span className="inline-flex px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 rounded-full">
                      {(standing as ChampionshipClassStandings).class}
                    </span>
                  </td>
                )}

                <td className="px-4 py-4 whitespace-nowrap">
                  <div className="flex items-center space-x-1">
                    <Target className="w-4 h-4 text-gray-400" />
                    <span className="text-sm text-gray-900 dark:text-white">
                      {standing.rallies_participated || 0}
                    </span>
                  </div>
                </td>

                <td className="px-4 py-4 whitespace-nowrap">
                  <span className="text-sm font-semibold text-blue-600 dark:text-blue-400">
                    {standing.total_points || 0}
                  </span>
                </td>

                <td className="px-4 py-4 whitespace-nowrap">
                  <div className="flex items-center space-x-1">
                    <Award className="w-4 h-4 text-purple-500" />
                    <span className="text-sm text-purple-600 dark:text-purple-400">
                      {standing.total_power_stage_points || 0}
                    </span>
                  </div>
                </td>

                <td className="px-4 py-4 whitespace-nowrap">
                  <span className="text-lg font-bold text-green-600 dark:text-green-400">
                    {standing.grand_total_points || rallyPoints}
                  </span>
                </td>
              </tr>
            );
          })}
        </tbody>
      </table>
    </div>
  );
};

export default ChampionshipStandingsTable;
