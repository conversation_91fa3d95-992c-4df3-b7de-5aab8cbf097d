import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { ClipboardList, Edit, Search, RefreshCw, Upload } from 'lucide-react';

interface Entry {
  id: string;
  rally_id: string;
  driver_id: string;
  codriver_id: string;
  car: string;
  team_id: string | null;
  number: number;
  class: string;
  status: string;
  driver_first_name?: string;
  driver_last_name?: string;
  codriver_first_name?: string;
  codriver_last_name?: string;
  team_name?: string;
}

interface Rally {
  id: string;
  name: string;
}

const EntriesManagementPage: React.FC = () => {
  const [entries, setEntries] = useState<Entry[]>([]);
  const [rallies, setRallies] = useState<Rally[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedRally, setSelectedRally] = useState<string>('');

  useEffect(() => {
    fetchEntries();
    fetchRallies();
  }, []);

  const fetchRallies = async () => {
    try {
      const res = await fetch('/api/rallies', { credentials: 'include' });
      if (!res.ok) throw new Error('Failed to fetch rallies');
      const data = await res.json();
      setRallies(Array.isArray(data) ? data : data.rallies || []);
    } catch (err: any) {
      setError(err.message);
    }
  };

  const fetchEntries = async () => {
    setLoading(true);
    try {
      const res = await fetch('/api/entries', { credentials: 'include' });
      if (!res.ok) throw new Error('Failed to fetch entries');
      const data = await res.json();
      setEntries(Array.isArray(data) ? data : data.entries || []);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: string) => {
    if (!window.confirm('Delete this entry?')) return;
    try {
      const res = await fetch(`/api/entries/${id}`, {
        method: 'DELETE',
        credentials: 'include',
      });
      if (!res.ok) throw new Error('Failed to delete');
      setEntries(entries.filter(e => e.id !== id));
    } catch (err: any) {
      setError(err.message || 'Delete failed');
    }
  };

  // Filter entries based on search term and selected rally
  const filteredEntries = entries.filter(entry => {
    // Filter by rally if selected
    if (selectedRally && entry.rally_id !== selectedRally) {
      return false;
    }

    // Filter by search term
    const searchLower = searchTerm.toLowerCase();
    return (
      (entry.driver_first_name && entry.driver_first_name.toLowerCase().includes(searchLower)) ||
      (entry.driver_last_name && entry.driver_last_name.toLowerCase().includes(searchLower)) ||
      (entry.codriver_first_name && entry.codriver_first_name.toLowerCase().includes(searchLower)) ||
      (entry.codriver_last_name && entry.codriver_last_name.toLowerCase().includes(searchLower)) ||
      entry.car.toLowerCase().includes(searchLower) ||
      String(entry.number).includes(searchLower) ||
      entry.class.toLowerCase().includes(searchLower) ||
      entry.status.toLowerCase().includes(searchLower) ||
      (entry.team_name && entry.team_name.toLowerCase().includes(searchLower))
    );
  });

  // Helper function to get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Active':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'Retired':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'Excluded':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'DNS':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Entries Management</h1>
        <div className="flex space-x-4">
          <Link
            to="/admin/entries/import"
            className="bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center hover:bg-blue-700 transition-colors"
          >
            <Upload className="w-4 h-4 mr-2" />
            Import CSV
          </Link>
          <Link
            to="/admin/entries/add"
            className="bg-red-600 text-white px-4 py-2 rounded-lg flex items-center hover:bg-red-700 transition-colors"
          >
            <ClipboardList className="w-4 h-4 mr-2" />
            Add Entry
          </Link>
        </div>
      </div>

      <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
        <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex flex-col md:flex-row justify-between items-center space-y-2 md:space-y-0">
          <div className="relative w-full md:w-64">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search entries..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 w-full rounded border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
            />
          </div>
          
          <div className="flex items-center space-x-2">
            <select
              value={selectedRally}
              onChange={(e) => setSelectedRally(e.target.value)}
              className="px-3 py-2 rounded border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
            >
              <option value="">All Rallies</option>
              {rallies.map(rally => (
                <option key={rally.id} value={rally.id}>{rally.name}</option>
              ))}
            </select>

            <button
              onClick={() => {
                fetchEntries();
                fetchRallies();
              }}
              className="flex items-center text-gray-700 dark:text-gray-300 hover:text-red-600 dark:hover:text-red-400"
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Refresh
            </button>
          </div>
        </div>

        {error && (
          <div className="p-4 bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-200">
            {error}
          </div>
        )}

        {loading ? (
          <div className="p-8 text-center">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-4 border-gray-300 border-t-red-600"></div>
            <p className="mt-2 text-gray-600 dark:text-gray-400">Loading entries...</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Rally
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Driver
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Codriver
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Car
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    #
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Status
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {filteredEntries.length === 0 ? (
                  <tr>
                    <td colSpan={7} className="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                      No entries found
                    </td>
                  </tr>
                ) : (
                  filteredEntries.map((entry) => {
                    const rally = rallies.find(r => r.id === entry.rally_id);
                    
                    return (
                      <tr key={entry.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                          {rally ? rally.name : entry.rally_id}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                          {entry.driver_first_name ? `${entry.driver_first_name} ${entry.driver_last_name}` : entry.driver_id}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                          {entry.codriver_first_name ? `${entry.codriver_first_name} ${entry.codriver_last_name}` : entry.codriver_id}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                          {entry.car}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                          {entry.number}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                          <span className={`px-2 py-1 rounded text-xs ${getStatusColor(entry.status)}`}>
                            {entry.status}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <div className="flex justify-end space-x-3">
                            <Link
                              to={`/admin/entries/${entry.id}`}
                              className="text-blue-600 hover:text-blue-900 dark:hover:text-blue-400 flex items-center"
                            >
                              <Edit className="w-4 h-4 mr-1" />
                              Edit
                            </Link>
                            <button
                              onClick={() => handleDelete(entry.id)}
                              className="text-red-600 hover:text-red-900 dark:hover:text-red-400 flex items-center"
                            >
                              Delete
                            </button>
                          </div>
                        </td>
                      </tr>
                    );
                  })
                )}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
};

export default EntriesManagementPage;

