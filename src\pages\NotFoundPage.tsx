import React from 'react';
import { Link } from 'react-router-dom';
import { Home, Flag, AlertTriangle } from 'lucide-react';

const NotFoundPage: React.FC = () => {
  return (
    <div className="min-h-screen flex items-center justify-center pt-16 px-4 bg-gray-50 dark:bg-gray-900">
      <div className="text-center max-w-md">
        <div className="mb-6">
          <AlertTriangle className="w-20 h-20 text-red-600 mx-auto" />
        </div>
        <h1 className="text-4xl font-bold mb-4 text-gray-900 dark:text-white">404</h1>
        <h2 className="text-2xl font-semibold mb-2 text-gray-800 dark:text-gray-100">Page Not Found</h2>
        <p className="text-gray-600 dark:text-gray-400 mb-8">
          The page you are looking for might have been removed, had its name changed, or is temporarily unavailable.
        </p>
        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          <Link 
            to="/" 
            className="bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded-lg flex items-center justify-center"
          >
            <Home className="w-4 h-4 mr-2" />
            Go Home
          </Link>
          <Link 
            to="/rallies" 
            className="bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-800 dark:text-gray-200 px-6 py-2 rounded-lg flex items-center justify-center"
          >
            <Flag className="w-4 h-4 mr-2" />
            Explore Rallies
          </Link>
        </div>
      </div>
    </div>
  );
};

export default NotFoundPage;