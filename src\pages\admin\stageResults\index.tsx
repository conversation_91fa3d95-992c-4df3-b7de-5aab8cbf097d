import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Clock, Edit, Search, RefreshCw, Upload } from 'lucide-react';
import { formatTime, formatPenalty, calculateTotalTime } from '../../../utils/timeUtils.js';

interface Rally {
  id: string;
  name: string;
}

interface Stage {
  id: string;
  rally_id: string;
  name: string;
  number: number;
}

interface Entry {
  id: string;
  rally_id: string;
  number: number;
  driver_first_name?: string;
  driver_last_name?: string;
  codriver_first_name?: string;
  codriver_last_name?: string;
}

interface StageResult {
  id: string;
  rally_id: string;
  stage_id: string;
  entry_id: string;
  time: number;
  nominal_time?: boolean;
  super_rally?: boolean;
  penalty_time?: number;
  penalty_reason?: string;
  rally_name?: string;
  stage_name?: string;
  entry_number?: number;
  driver_name?: string;
  codriver_name?: string;
}

const StageResultsManagementPage: React.FC = () => {
  const [results, setResults] = useState<StageResult[]>([]);
  const [rallies, setRallies] = useState<Rally[]>([]);
  const [stages, setStages] = useState<Stage[]>([]);
  const [entries, setEntries] = useState<Entry[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedRally, setSelectedRally] = useState<string>('');
  const [selectedStage, setSelectedStage] = useState<string>('');

  useEffect(() => {
    fetchResults();
    fetchRallies();
    fetchStages();
    fetchEntries();
  }, []);

  const fetchRallies = async () => {
    try {
      const res = await fetch('/api/rallies', { credentials: 'include' });
      if (!res.ok) throw new Error('Failed to fetch rallies');
      const data = await res.json();
      setRallies(Array.isArray(data) ? data : data.rallies || []);
    } catch (err: any) {
      console.error('Failed to fetch rallies:', err.message);
    }
  };

  const fetchStages = async () => {
    try {
      const res = await fetch('/api/stages', { credentials: 'include' });
      if (!res.ok) throw new Error('Failed to fetch stages');
      const data = await res.json();
      setStages(Array.isArray(data) ? data : data.stages || []);
    } catch (err: any) {
      console.error('Failed to fetch stages:', err.message);
    }
  };

  const fetchEntries = async () => {
    try {
      const res = await fetch('/api/entries', { credentials: 'include' });
      if (!res.ok) throw new Error('Failed to fetch entries');
      const data = await res.json();
      setEntries(Array.isArray(data) ? data : data.entries || []);
    } catch (err: any) {
      console.error('Failed to fetch entries:', err.message);
    }
  };

  const fetchResults = async () => {
    setLoading(true);
    try {
      const res = await fetch('/api/stageResults', { credentials: 'include' });
      if (!res.ok) throw new Error('Failed to fetch stage results');
      const data = await res.json();
      setResults(Array.isArray(data) ? data : data.results || []);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch stage results');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: string) => {
    if (!window.confirm('Delete this stage result?')) return;
    try {
      const res = await fetch(`/api/stageResults/${id}`, {
        method: 'DELETE',
        credentials: 'include',
      });
      if (!res.ok) throw new Error('Failed to delete');
      setResults(results.filter(r => r.id !== id));
    } catch (err: any) {
      setError(err.message || 'Delete failed');
    }
  };

  // Filter results based on search term and selected rally/stage
  const filteredResults = results.filter(result => {
    // Get related objects
    const rally = rallies.find(r => r.id === result.rally_id);
    const stage = stages.find(s => s.id === result.stage_id);
    const entry = entries.find(e => e.id === result.entry_id);

    // Search term matching
    const searchLower = searchTerm.toLowerCase();
    const matchesSearch = searchTerm === '' ||
      (rally && rally.name.toLowerCase().includes(searchLower)) ||
      (stage && stage.name.toLowerCase().includes(searchLower)) ||
      (entry && entry.number.toString().includes(searchLower)) ||
      (entry && entry.driver_first_name && entry.driver_first_name.toLowerCase().includes(searchLower)) ||
      (entry && entry.driver_last_name && entry.driver_last_name.toLowerCase().includes(searchLower)) ||
      (entry && entry.codriver_first_name && entry.codriver_first_name.toLowerCase().includes(searchLower)) ||
      (entry && entry.codriver_last_name && entry.codriver_last_name.toLowerCase().includes(searchLower));

    // Filter by selected rally
    const matchesRally = !selectedRally || result.rally_id === selectedRally;

    // Filter by selected stage
    const matchesStage = !selectedStage || result.stage_id === selectedStage;

    return matchesSearch && matchesRally && matchesStage;
  });



  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Stage Results Management</h1>
        <div className="flex space-x-2">
          <Link
            to="/admin/stageResults/import"
            className="bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center hover:bg-blue-700 transition-colors"
          >
            <Upload className="w-4 h-4 mr-2" />
            Import CSV
          </Link>
          <Link
            to="/admin/stageResults/add"
            className="bg-red-600 text-white px-4 py-2 rounded-lg flex items-center hover:bg-red-700 transition-colors"
          >
            <Clock className="w-4 h-4 mr-2" />
            Add Stage Result
          </Link>
        </div>
      </div>

      <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
        <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex flex-col md:flex-row justify-between items-center space-y-2 md:space-y-0">
          <div className="relative w-full md:w-64">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search stage results..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 w-full rounded border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
            />
          </div>

          <div className="flex items-center space-x-2">
            <select
              value={selectedRally}
              onChange={(e) => {
                setSelectedRally(e.target.value);
                // Reset stage selection if rally changes
                if (selectedStage) {
                  const stageStillValid = stages.some(
                    s => s.id === selectedStage && s.rally_id === e.target.value
                  );
                  if (!stageStillValid) {
                    setSelectedStage('');
                  }
                }
              }}
              className="px-3 py-2 rounded border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
            >
              <option value="">All Rallies</option>
              {rallies.map(rally => (
                <option key={rally.id} value={rally.id}>{rally.name}</option>
              ))}
            </select>

            <select
              value={selectedStage}
              onChange={(e) => setSelectedStage(e.target.value)}
              className="px-3 py-2 rounded border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
            >
              <option value="">All Stages</option>
              {stages
                .filter(stage => !selectedRally || stage.rally_id === selectedRally)
                .map(stage => (
                  <option key={stage.id} value={stage.id}>SS{stage.number} - {stage.name}</option>
                ))
              }
            </select>

            <button
              onClick={() => {
                fetchResults();
                fetchRallies();
                fetchStages();
                fetchEntries();
              }}
              className="flex items-center text-gray-700 dark:text-gray-300 hover:text-red-600 dark:hover:text-red-400"
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Refresh
            </button>
          </div>
        </div>

        {error && (
          <div className="p-4 bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-200">
            {error}
          </div>
        )}

        {loading ? (
          <div className="p-8 text-center">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-4 border-gray-300 border-t-red-600"></div>
            <p className="mt-2 text-gray-600 dark:text-gray-400">Loading stage results...</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Rally
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Stage
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Entry
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Driver
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Time
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {filteredResults.length === 0 ? (
                  <tr>
                    <td colSpan={6} className="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                      No stage results found
                    </td>
                  </tr>
                ) : (
                  filteredResults.map((result) => {
                    const rally = rallies.find(r => r.id === result.rally_id);
                    const stage = stages.find(s => s.id === result.stage_id);
                    const entry = entries.find(e => e.id === result.entry_id);

                    return (
                      <tr key={result.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                          {rally ? rally.name : result.rally_name || result.rally_id}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                          {stage ? `SS${stage.number} - ${stage.name}` : result.stage_name || result.stage_id}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                          #{entry ? entry.number : result.entry_number || '?'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                          {entry ? `${entry.driver_first_name} ${entry.driver_last_name}` :
                            (result.driver_name || 'Unknown Driver')}
                          {result.super_rally && (
                            <span className="ml-1 text-xs text-blue-600 dark:text-blue-400 font-medium">[SR]</span>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-500 dark:text-gray-400">
                          <div className="flex flex-col">
                            <span>
                              {(() => {
                                const penaltyTime = result.penalty_time ? parseFloat(result.penalty_time) : 0;
                                const stageTime = parseFloat(result.time);
                                const hasPenalty = penaltyTime > 0 && !isNaN(penaltyTime);
                                const totalTime = hasPenalty ? calculateTotalTime(stageTime, penaltyTime) : stageTime;
                                return formatTime(totalTime);
                              })()}
                              {result.nominal_time && (
                                <span className="ml-1 text-xs text-orange-600 dark:text-orange-400 font-medium">[N]</span>
                              )}
                            </span>
                            {(() => {
                              const penaltyTime = result.penalty_time ? parseFloat(result.penalty_time) : 0;
                              const stageTime = parseFloat(result.time);
                              const hasPenalty = penaltyTime > 0 && !isNaN(penaltyTime);

                              if (hasPenalty) {
                                return (
                                  <div className="text-xs text-red-600 dark:text-red-400">
                                    {formatTime(stageTime)} {formatPenalty(penaltyTime)}
                                    {result.penalty_reason && (
                                      <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                        {result.penalty_reason}
                                      </div>
                                    )}
                                  </div>
                                );
                              }
                              return null;
                            })()}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <div className="flex justify-end space-x-3">
                            <Link
                              to={`/admin/stageResults/${result.id}`}
                              className="text-blue-600 hover:text-blue-900 dark:hover:text-blue-400 flex items-center"
                            >
                              <Edit className="w-4 h-4 mr-1" />
                              Edit
                            </Link>
                            <button
                              onClick={() => handleDelete(result.id)}
                              className="text-red-600 hover:text-red-900 dark:hover:text-red-400 flex items-center"
                            >
                              Delete
                            </button>
                          </div>
                        </td>
                      </tr>
                    );
                  })
                )}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
};

export default StageResultsManagementPage;
