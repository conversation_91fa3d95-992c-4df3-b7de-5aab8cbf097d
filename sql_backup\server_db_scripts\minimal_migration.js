import pool from '../config/db.js';

async function minimalMigration() {
  console.log('🚀 Minimal Entry Activity Tracking Migration...\n');

  try {
    // Just add the essential column and update existing data
    console.log('📝 Adding is_active column to results table...');
    
    await pool.query('ALTER TABLE results ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT TRUE');
    console.log('✅ Column added successfully');

    console.log('\n📝 Updating existing results...');
    const updateResult = await pool.query(`
      UPDATE results 
      SET is_active = (
        SELECT CASE 
          WHEN e.status IN ('retired', 'dns', 'dnf', 'dsq') THEN FALSE
          ELSE TRUE
        END
        FROM entries e 
        WHERE e.id = results.entry_id
      )
    `);
    
    console.log(`✅ Updated ${updateResult.rowCount} results`);

    console.log('\n📝 Adding performance index...');
    await pool.query('CREATE INDEX IF NOT EXISTS idx_results_is_active ON results(is_active)');
    console.log('✅ Index added successfully');

    // Verification
    console.log('\n🔍 Verification:');
    const stats = await pool.query(`
      SELECT 
        COUNT(*) as total_results,
        COUNT(CASE WHEN is_active = true THEN 1 END) as active_results,
        COUNT(CASE WHEN is_active = false THEN 1 END) as inactive_results
      FROM results
    `);
    
    console.log('📊 Results:');
    console.log(`   Total: ${stats.rows[0].total_results}`);
    console.log(`   Active: ${stats.rows[0].active_results}`);
    console.log(`   Inactive: ${stats.rows[0].inactive_results}`);

    console.log('\n🎉 Minimal migration completed successfully!');
    console.log('\n📋 What was added:');
    console.log('   ✅ is_active column in results table');
    console.log('   ✅ Updated existing data based on entry status');
    console.log('   ✅ Performance index for queries');
    console.log('\n💡 Note: You can add the history table and triggers later if needed');

  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    console.error('Full error:', error);
    throw error;
  }
}

// Run the migration
minimalMigration()
  .then(() => {
    console.log('\n✨ Migration script completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n💥 Migration script failed:', error.message);
    process.exit(1);
  });
