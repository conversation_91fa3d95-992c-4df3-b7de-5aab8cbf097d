{"name": "stagetime-gr", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "server": "node server/index.js", "update-views": "node server/db/update_views.js", "start": "npm run build && node server/index.js"}, "dependencies": {"axios": "^1.6.7", "bcryptjs": "^2.4.3", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "csv-parse": "^5.6.0", "dotenv": "^16.4.5", "express": "^4.18.3", "flag-icons": "^7.3.2", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.344.0", "multer": "^1.4.5-lts.1", "pg": "^8.11.3", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.23.1", "vite": "^5.4.2", "@vitejs/plugin-react": "^4.3.1"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0"}}