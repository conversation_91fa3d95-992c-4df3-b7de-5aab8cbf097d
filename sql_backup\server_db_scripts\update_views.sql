-- Drop existing views that depend on overall_classification
DROP VIEW IF EXISTS driver_stats;
DROP VIEW IF EXISTS championship_standings;

-- Drop the overall_classification view
DROP VIEW IF EXISTS overall_classification;

-- Recreate the overall_classification view with status filter
CREATE VIEW overall_classification AS
SELECT
  e.rally_id,
  e.id AS entry_id,
  e.number,
  e.status,
  pd.first_name || ' ' || pd.last_name AS driver,
  pcd.first_name || ' ' || pcd.last_name AS codriver,
  SUM(r.time + COALESCE(p.time, 0)) AS total_time,
  RANK() OVER (PARTITION BY e.rally_id ORDER BY SUM(r.time + COALESCE(p.time, 0))) AS position,
  SUM(r.time + COALESCE(p.time, 0)) - MIN(SUM(r.time + COALESCE(p.time, 0))) OVER (PARTITION BY e.rally_id) AS time_diff
FROM entries e
JOIN results r ON r.entry_id = e.id
JOIN drivers d ON d.id = e.driver_id
JOIN persons pd ON pd.id = d.id
JOIN codrivers cd ON cd.id = e.codriver_id
JOIN persons pcd ON pcd.id = cd.id
LEFT JOIN penalties p ON p.entry_id = e.id AND p.stage_id = r.stage_id
-- Filter out entries with invalid statuses
WHERE e.status NOT IN ('retired', 'dns', 'dnf', 'dsq')
GROUP BY e.rally_id, e.id, e.status, pd.first_name, pd.last_name, pcd.first_name, pcd.last_name, e.number;

-- Recreate the driver_stats view
CREATE VIEW driver_stats AS
SELECT
  d.id AS driver_id,
  pd.first_name || ' ' || pd.last_name AS driver,
  COUNT(DISTINCT e.rally_id) AS rallies_participated,
  COUNT(CASE WHEN oc.position = 1 THEN 1 END) AS wins,
  COUNT(CASE WHEN oc.position <= 3 THEN 1 END) AS podiums
FROM drivers d
JOIN persons pd ON pd.id = d.id
JOIN entries e ON e.driver_id = d.id
LEFT JOIN overall_classification oc ON oc.entry_id = e.id
GROUP BY d.id, pd.first_name, pd.last_name;

-- Recreate the championship_standings view
CREATE VIEW championship_standings AS
SELECT
  ce.championship_id,
  e.driver_id,
  pd.first_name || ' ' || pd.last_name AS driver,
  SUM(COALESCE(ps.points, 0)) AS total_points,
  RANK() OVER (PARTITION BY ce.championship_id ORDER BY SUM(COALESCE(ps.points, 0)) DESC) AS position
FROM championship_events ce
JOIN rallies r ON r.id = ce.rally_id
JOIN power_stage_points ps ON ps.rally_id = r.id
JOIN entries e ON e.id = ps.entry_id
JOIN drivers d ON d.id = e.driver_id
JOIN persons pd ON pd.id = d.id
GROUP BY ce.championship_id, e.driver_id, pd.first_name, pd.last_name;
