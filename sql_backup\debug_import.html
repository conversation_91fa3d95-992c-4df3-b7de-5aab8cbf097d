<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EWRC Import Debug</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        .result { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 3px; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
        input, select { padding: 5px; margin: 5px; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>EWRC Import Debug Tool</h1>
    
    <div class="section">
        <h2>1. Test Championship Lookup</h2>
        <p>Test if championships exist in the database:</p>
        <button onclick="testChampionships()">Test Championships from JSON</button>
        <div id="championshipResults"></div>
    </div>
    
    <div class="section">
        <h2>2. Test Stage Date/Time Parsing</h2>
        <p>Test the stage date and time parsing logic:</p>
        <input type="text" id="stageDate" placeholder="Stage Date (e.g., 25.5.)" value="25.5.">
        <input type="text" id="stageTime" placeholder="Stage Time (e.g., 09:23)" value="09:23">
        <input type="number" id="rallyYear" placeholder="Rally Year" value="2025">
        <button onclick="testStageParsing()">Test Stage Parsing</button>
        <div id="stageResults"></div>
    </div>
    
    <div class="section">
        <h2>3. Create Missing Championships</h2>
        <p>Create the championships needed for the JSON import:</p>
        <button onclick="createChampionships()">Create Championships</button>
        <div id="createResults"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3001/api';
        
        // You'll need to get a valid token - replace this with actual token
        const TOKEN = 'your-admin-token-here';
        
        async function apiCall(endpoint, method = 'GET', data = null) {
            const options = {
                method,
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${TOKEN}`
                }
            };
            
            if (data) {
                options.body = JSON.stringify(data);
            }
            
            try {
                const response = await fetch(`${API_BASE}${endpoint}`, options);
                const result = await response.json();
                return { success: response.ok, data: result };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }
        
        async function testChampionships() {
            const championshipNames = [
                "Greece",
                "Historic (GR)",
                "Rally3 (GR)",
                "Historic Gravel Cup (GR)"
            ];
            
            const result = await apiCall('/rallies/debug-championships', 'POST', {
                championshipNames: championshipNames
            });
            
            const resultsDiv = document.getElementById('championshipResults');
            
            if (result.success) {
                let html = '<div class="result success"><h3>Championship Test Results:</h3>';
                
                result.data.test_results.forEach(test => {
                    const status = test.found ? '✓ FOUND' : '✗ NOT FOUND';
                    const color = test.found ? 'green' : 'red';
                    html += `<p style="color: ${color}"><strong>${test.name}</strong>: ${status}`;
                    if (test.found) {
                        html += ` (ID: ${test.id}, Name: "${test.actual_name}")`;
                    }
                    html += '</p>';
                });
                
                html += '<h4>Available Championships:</h4><ul>';
                result.data.available_championships.forEach(champ => {
                    html += `<li>${champ.name} (${champ.year}) - ${champ.type}</li>`;
                });
                html += '</ul></div>';
                
                resultsDiv.innerHTML = html;
            } else {
                resultsDiv.innerHTML = `<div class="result error">Error: ${result.error || result.data?.message}</div>`;
            }
        }
        
        async function testStageParsing() {
            const stageDate = document.getElementById('stageDate').value;
            const stageTime = document.getElementById('stageTime').value;
            const rallyYear = parseInt(document.getElementById('rallyYear').value);
            
            const result = await apiCall('/rallies/debug-stage-parsing', 'POST', {
                stageDate, stageTime, rallyYear
            });
            
            const resultsDiv = document.getElementById('stageResults');
            
            if (result.success) {
                let html = '<div class="result success"><h3>Stage Parsing Results:</h3>';
                html += `<p><strong>Input:</strong> ${stageDate} ${stageTime} (${rallyYear})</p>`;
                
                if (result.data.result.success) {
                    html += `<p><strong>✓ Parsed Successfully:</strong></p>`;
                    html += `<p><strong>ISO Date:</strong> ${result.data.result.parsed_date}</p>`;
                    html += `<p><strong>Local Date:</strong> ${result.data.result.local_date}</p>`;
                    html += `<p><strong>Components:</strong> ${JSON.stringify(result.data.result.components)}</p>`;
                } else {
                    html += `<p style="color: red"><strong>✗ Parsing Failed:</strong> ${result.data.result.error}</p>`;
                }
                
                html += '</div>';
                resultsDiv.innerHTML = html;
            } else {
                resultsDiv.innerHTML = `<div class="result error">Error: ${result.error || result.data?.message}</div>`;
            }
        }
        
        async function createChampionships() {
            const championships = [
                { name: "Greece", year: 2025, type: "national", description: "Greek National Rally Championship 2025" },
                { name: "Historic (GR)", year: 2025, type: "national", description: "Greek Historic Rally Championship 2025" },
                { name: "Rally3 (GR)", year: 2025, type: "national", description: "Greek Rally3 Championship 2025" },
                { name: "Historic Gravel Cup (GR)", year: 2025, type: "local", description: "Greek Historic Gravel Cup 2025" }
            ];
            
            const resultsDiv = document.getElementById('createResults');
            let html = '<div class="result"><h3>Creating Championships:</h3>';
            
            for (const champ of championships) {
                const result = await apiCall('/championships', 'POST', champ);
                
                if (result.success) {
                    html += `<p style="color: green">✓ Created: ${champ.name}</p>`;
                } else {
                    if (result.data?.message?.includes('duplicate') || result.data?.message?.includes('already exists')) {
                        html += `<p style="color: orange">⚠ Already exists: ${champ.name}</p>`;
                    } else {
                        html += `<p style="color: red">✗ Failed to create ${champ.name}: ${result.data?.message || result.error}</p>`;
                    }
                }
            }
            
            html += '</div>';
            resultsDiv.innerHTML = html;
            
            // Refresh championship test after creation
            setTimeout(testChampionships, 1000);
        }
        
        // Show instructions for getting token
        if (TOKEN === 'your-admin-token-here') {
            document.body.insertAdjacentHTML('afterbegin', 
                '<div style="background: #fff3cd; padding: 15px; margin-bottom: 20px; border-radius: 5px;">' +
                '<strong>Setup Required:</strong> You need to replace "your-admin-token-here" in the script with a valid admin token. ' +
                'Login to the admin panel and check the browser developer tools for the Authorization header.' +
                '</div>'
            );
        }
    </script>
</body>
</html>
