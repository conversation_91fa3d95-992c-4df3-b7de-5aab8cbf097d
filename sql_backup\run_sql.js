import pool from './server/config/db.js';
import fs from 'fs';

async function runSQL() {
  try {
    const sql = fs.readFileSync('./create_entry_based_championship_views.sql', 'utf8');
    console.log('Running SQL:', sql);

    const result = await pool.query(sql);
    console.log('SQL executed successfully:', result);

    process.exit(0);
  } catch (error) {
    console.error('Error running SQL:', error);
    process.exit(1);
  }
}

runSQL();
