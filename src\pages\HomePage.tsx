import React from 'react';
import { Link } from 'react-router-dom';
import { ChevronRight, Flag, Trophy, Clock } from 'lucide-react';
import { useRallyContext } from '../context/RallyContext';
import RallyCard from '../components/rallies/RallyCard';
import DriverCard from '../components/drivers/DriverCard';
import StandingsTable from '../components/championships/StandingsTable';
import LoadingSpinner from '../components/ui/LoadingSpinner';

const HomePage: React.FC = () => {
  const { rallies, drivers, championships, loading, error } = useRallyContext();

  // Show loading spinner if still loading
  if (loading) {
    return <LoadingSpinner fullScreen message="Loading rally data..." />;
  }

  // Show error message if there's an error
  if (error) {
    return (
      <div className="pt-16 min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center p-8">
          <div className="text-red-500 text-6xl mb-4">⚠️</div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
            Unable to load data
          </h1>
          <p className="text-gray-600 dark:text-gray-300 mb-4">
            {error}
          </p>
          <button
            onClick={() => window.location.reload()}
            className="bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  const liveRallies = Array.isArray(rallies) ? rallies.filter(rally => rally.status === 'live') : [];
  const upcomingRallies = Array.isArray(rallies) ? rallies.filter(rally => rally.status === 'upcoming') : [];

  // Get the top 4 drivers by points
  const topDrivers = Array.isArray(drivers)
    ? [...drivers].sort((a, b) => (b.careerPoints || 0) - (a.careerPoints || 0)).slice(0, 4)
    : [];

  // Get the first championship for standings display
  const featuredChampionship = Array.isArray(championships) && championships.length > 0 ? championships[0] : null;

  return (
    <div className="pt-16">
      {/* Hero Section */}
      <section className="relative h-[70vh] bg-gray-900 text-white overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-b from-transparent to-gray-900 z-10"></div>
        <div
          className="absolute inset-0 bg-cover bg-center z-0"
          style={{
            backgroundImage: `url('https://gr.ml-vehicle.com/Content/uploads/2023931296/202308162340533b5a06956a5841dbb33f6d2b05086599.jpg')`
          }}
        ></div>

        <div className="container mx-auto px-4 h-full flex items-center relative z-20">
          <div className="max-w-3xl">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">
              Follow Every Stage, Every Second
            </h1>
            <p className="text-xl mb-8">
              Your comprehensive rally tracking platform with live timing, results, and statistics for all rally events.
            </p>
            <div className="flex flex-wrap gap-4">
              {liveRallies.length > 0 ? (
                <Link
                  to={`/rallies/${liveRallies[0].id}`}
                  className="bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-lg font-semibold flex items-center transition-colors"
                >
                  <Clock className="w-5 h-5 mr-2" />
                  Watch Live Now
                </Link>
              ) : (
                <Link
                  to="/rallies"
                  className="bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-lg font-semibold flex items-center transition-colors"
                >
                  <Flag className="w-5 h-5 mr-2" />
                  Explore Rallies
                </Link>
              )}
              <Link
                to="/championships"
                className="bg-transparent border border-white hover:bg-white hover:text-gray-900 text-white px-6 py-3 rounded-lg font-semibold flex items-center transition-colors"
              >
                <Trophy className="w-5 h-5 mr-2" />
                Championship Standings
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Live & Upcoming Rallies */}
      <section className="py-12 bg-white dark:bg-gray-800">
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center mb-8">
            <h2 className="text-2xl font-bold">Live & Upcoming Rallies</h2>
            <Link
              to="/rallies"
              className="text-red-600 hover:text-red-700 flex items-center font-medium"
            >
              View All <ChevronRight className="w-4 h-4 ml-1" />
            </Link>
          </div>

          {[...liveRallies, ...upcomingRallies].length === 0 ? (
            <div className="text-center py-12">
              <p className="text-gray-600 dark:text-gray-300">No rallies available at the moment.</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...liveRallies, ...upcomingRallies].slice(0, 3).map(rally => (
                <RallyCard key={rally.id} rally={rally} />
              ))}
            </div>
          )}
        </div>
      </section>

      {/* Championship Standings & Top Drivers */}
      <section className="py-12 bg-gray-100 dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-5 gap-8">
            {/* Championship Standings */}
            <div className="lg:col-span-3">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-2xl font-bold">Championship Standings</h2>
                <Link
                  to="/championships"
                  className="text-red-600 hover:text-red-700 flex items-center font-medium"
                >
                  View All <ChevronRight className="w-4 h-4 ml-1" />
                </Link>
              </div>

              {featuredChampionship && Array.isArray((featuredChampionship as any).standings) && (featuredChampionship as any).standings.length > 0 ? (
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center">
                      <Trophy className="w-5 h-5 text-yellow-500 mr-2" />
                      <h3 className="text-xl font-semibold">{featuredChampionship.name} {featuredChampionship.year}</h3>
                    </div>
                  </div>
                  <StandingsTable standings={(featuredChampionship as any).standings} drivers={drivers} />
                </div>
              ) : (
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 text-center py-12">
                  <p className="text-gray-600 dark:text-gray-300">No championship standings available</p>
                </div>
              )}
            </div>

            {/* Top Drivers */}
            <div className="lg:col-span-2">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-2xl font-bold">Top Drivers</h2>
                <Link
                  to="/drivers"
                  className="text-red-600 hover:text-red-700 flex items-center font-medium"
                >
                  View All <ChevronRight className="w-4 h-4 ml-1" />
                </Link>
              </div>

              {topDrivers.length === 0 ? (
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 text-center py-12">
                  <p className="text-gray-600 dark:text-gray-300">No drivers available</p>
                </div>
              ) : (
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md">
                  <div className="divide-y divide-gray-200 dark:divide-gray-700">
                    {topDrivers.map(driver => (
                      <DriverCard key={driver.id} driver={driver} compact />
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </section>

      {/* Latest News & Updates */}
      <section className="py-12 bg-white dark:bg-gray-800">
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center mb-8">
            <h2 className="text-2xl font-bold">Rally Updates</h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-gray-100 dark:bg-gray-700 rounded-lg overflow-hidden shadow-md">
              <img
                src="https://www.wrc.com/images/redaktion/Season-2023-NEWS/WRC/6_Rally_Poland/Sunday/ogier7_bcdb0_f_1400x788.jpg"
                alt="Rally Update"
                className="w-full h-48 object-cover"
              />
              <div className="p-4">
                <span className="inline-block bg-red-600 text-white text-xs px-2 py-1 rounded mb-2">Breaking News</span>
                <h3 className="text-lg font-semibold mb-2">Ogier Secures Dominant Poland Victory</h3>
                <p className="text-gray-600 dark:text-gray-300 text-sm mb-3">
                  After an intense battle throughout the weekend, Sébastien Ogier claims his fourth win of the season.
                </p>
                <a href="#" className="text-red-600 hover:text-red-700 font-medium text-sm">Read More</a>
              </div>
            </div>

            <div className="bg-gray-100 dark:bg-gray-700 rounded-lg overflow-hidden shadow-md">
              <img
                src="https://www.wrc.com/images/redaktion/Season-2023-NEWS/WRC/9_Rally_Chile/Sunday/tanak_6dd9a_f_1400x788.jpg"
                alt="Rally Update"
                className="w-full h-48 object-cover"
              />
              <div className="p-4">
                <span className="inline-block bg-blue-600 text-white text-xs px-2 py-1 rounded mb-2">Team News</span>
                <h3 className="text-lg font-semibold mb-2">Tänak Confirms Move for 2024 Season</h3>
                <p className="text-gray-600 dark:text-gray-300 text-sm mb-3">
                  Estonian star Ott Tänak has confirmed his plans for the upcoming 2024 WRC season with a surprise team change.
                </p>
                <a href="#" className="text-red-600 hover:text-red-700 font-medium text-sm">Read More</a>
              </div>
            </div>

            <div className="bg-gray-100 dark:bg-gray-700 rounded-lg overflow-hidden shadow-md">
              <img
                src="https://www.wrc.com/images/redaktion/Season-2023-NEWS/WRC/2_Rally_Sweden/Sunday/rovanpera_71c20_f_1400x788.jpg"
                alt="Rally Update"
                className="w-full h-48 object-cover"
              />
              <div className="p-4">
                <span className="inline-block bg-green-600 text-white text-xs px-2 py-1 rounded mb-2">Technical</span>
                <h3 className="text-lg font-semibold mb-2">Next-Gen Rally Cars Coming in 2025</h3>
                <p className="text-gray-600 dark:text-gray-300 text-sm mb-3">
                  The FIA has announced new technical regulations for the 2025 season, focusing on sustainability.
                </p>
                <a href="#" className="text-red-600 hover:text-red-700 font-medium text-sm">Read More</a>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-16 bg-gray-900 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-4">Stay Updated with Live Rally Coverage</h2>
          <p className="text-xl mb-8 max-w-3xl mx-auto">
            Get real-time stage results, live timing, and in-depth analysis for all rally events around the world.
          </p>
          <Link
            to="/rallies"
            className="bg-red-600 hover:bg-red-700 text-white px-8 py-3 rounded-lg font-semibold inline-flex items-center transition-colors"
          >
            <Flag className="w-5 h-5 mr-2" />
            Browse All Rallies
          </Link>
        </div>
      </section>
    </div>
  );
};

export default HomePage;