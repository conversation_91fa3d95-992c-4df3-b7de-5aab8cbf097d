import express from 'express';
import pool from '../config/db.js';

const router = express.Router();

// Get all driver statistics - PUBLIC ROUTE
router.get('/', async (req, res) => {
  try {
    const result = await pool.query(`
      SELECT 
        d.id AS driver_id,
        COUNT(DISTINCT e.rally_id) AS rallies_participated,
        COUNT(CASE WHEN oc.position = 1 THEN 1 END) AS wins,
        COUNT(CASE WHEN oc.position <= 3 THEN 1 END) AS podiums
      FROM drivers d
      LEFT JOIN entries e ON e.driver_id = d.id
      LEFT JOIN overall_classification oc ON oc.entry_id = e.id
      GROUP BY d.id
    `);
    res.json(result.rows);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Failed to fetch driver statistics' });
  }
});

// Get statistics for a specific driver - PUBLIC ROUTE
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const result = await pool.query(`
      SELECT 
        d.id AS driver_id,
        COUNT(DISTINCT e.rally_id) AS rallies_participated,
        COUNT(CASE WHEN oc.position = 1 THEN 1 END) AS wins,
        COUNT(CASE WHEN oc.position <= 3 THEN 1 END) AS podiums
      FROM drivers d
      LEFT JOIN entries e ON e.driver_id = d.id
      LEFT JOIN overall_classification oc ON oc.entry_id = e.id
      WHERE d.id = $1
      GROUP BY d.id
    `, [id]);
    
    if (result.rows.length === 0) {
      // If no results found, return default values
      return res.json({
        driver_id: id,
        rallies_participated: 0,
        wins: 0,
        podiums: 0
      });
    }
    
    res.json(result.rows[0]);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Failed to fetch driver statistics' });
  }
});

export default router;
