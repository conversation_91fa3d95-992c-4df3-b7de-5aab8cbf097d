import React, { useState, useEffect } from 'react';
import { Clock, Award, Flag, ChevronDown, ChevronUp } from 'lucide-react';
import { Shakedown, ShakedownRun, ShakedownBestTime, Entry } from '../../types';
import { formatTime } from '../../utils/timeUtils.js';

interface ShakedownResultsProps {
  rallyId: string;
}

export const ShakedownResults: React.FC<ShakedownResultsProps> = ({ rallyId }) => {
  const [shakedown, setShakedown] = useState<Shakedown | null>(null);
  const [runs, setRuns] = useState<ShakedownRun[]>([]);
  const [bestTimes, setBestTimes] = useState<ShakedownBestTime[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [expandedEntry, setExpandedEntry] = useState<string | null>(null);

  useEffect(() => {
    const fetchShakedownData = async () => {
      try {
        setLoading(true);

        // Fetch shakedown info
        const shakedownRes = await fetch(`/api/shakedowns/rally/${rallyId}`);
        if (!shakedownRes.ok) throw new Error('Failed to fetch shakedown');
        const shakedownData = await shakedownRes.json();

        if (!shakedownData) {
          setShakedown(null);
          setLoading(false);
          return;
        }

        setShakedown(shakedownData);

        // Fetch runs
        const runsRes = await fetch(`/api/shakedowns/${shakedownData.id}/runs`);
        if (!runsRes.ok) throw new Error('Failed to fetch shakedown runs');
        const runsData = await runsRes.json();
        setRuns(runsData);

        // Fetch best times
        const bestTimesRes = await fetch(`/api/shakedowns/${shakedownData.id}/best-times`);
        if (!bestTimesRes.ok) throw new Error('Failed to fetch best times');
        const bestTimesData = await bestTimesRes.json();
        setBestTimes(bestTimesData);

        setLoading(false);
      } catch (err) {
        console.error('Error fetching shakedown data:', err);
        setError(err instanceof Error ? err.message : 'An unknown error occurred');
        setLoading(false);
      }
    };

    if (rallyId) {
      fetchShakedownData();
    }
  }, [rallyId]);

  const toggleEntryExpansion = (entryId: string) => {
    if (expandedEntry === entryId) {
      setExpandedEntry(null);
    } else {
      setExpandedEntry(entryId);
    }
  };

  const getRunsForEntry = (entryId: string) => {
    return runs.filter(run => run.entry_id === entryId)
      .sort((a, b) => a.run_number - b.run_number);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg">
        <p className="text-red-600 dark:text-red-400">Error: {error}</p>
      </div>
    );
  }

  if (!shakedown) {
    return (
      <div className="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg text-center">
        <p className="text-gray-600 dark:text-gray-400">No shakedown data available for this rally.</p>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
      <div className="px-6 py-4 bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600">
        <h2 className="font-medium text-lg text-gray-900 dark:text-white flex items-center">
          <Flag className="w-5 h-5 mr-2 text-orange-500" />
          {shakedown.name}
        </h2>
        <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
          {new Date(shakedown.date).toLocaleDateString('en-GB', {
            weekday: 'long',
            day: 'numeric',
            month: 'long',
            year: 'numeric'
          })}
        </div>
        <div className="mt-2 flex flex-wrap gap-3">
          <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded-full text-xs">
            {shakedown.length} km
          </span>
          <span className="bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 px-2 py-1 rounded-full text-xs">
            {shakedown.location}
          </span>
          <span className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-2 py-1 rounded-full text-xs">
            Max {shakedown.max_runs} runs
          </span>
        </div>
      </div>

      <div className="p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
          <Award className="w-5 h-5 mr-2 text-yellow-500" />
          Best Times
        </h3>

        {bestTimes.length === 0 ? (
          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg text-center">
            <p className="text-gray-600 dark:text-gray-400">No times recorded yet.</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Pos</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Car</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Driver</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Best Time</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Details</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                {bestTimes.map((entry, index) => (
                  <tr key={entry.entry_id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">{index + 1}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400">#{entry.car_number}</td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900 dark:text-white">{entry.driver_first_name} {entry.driver_last_name}</div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">{entry.car}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                      {formatTime(entry.best_time)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      <button
                        onClick={() => toggleEntryExpansion(entry.entry_id)}
                        className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 flex items-center"
                      >
                        {expandedEntry === entry.entry_id ? (
                          <>
                            <ChevronUp className="w-4 h-4 mr-1" />
                            Hide runs
                          </>
                        ) : (
                          <>
                            <ChevronDown className="w-4 h-4 mr-1" />
                            Show runs
                          </>
                        )}
                      </button>

                      {expandedEntry === entry.entry_id && (
                        <div className="mt-3 bg-gray-50 dark:bg-gray-700 rounded p-3">
                          <h4 className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">All Runs</h4>
                          <div className="space-y-2">
                            {getRunsForEntry(entry.entry_id).map(run => (
                              <div key={run.id} className="flex justify-between text-xs">
                                <span>Run {run.run_number}</span>
                                <span className="font-medium">{run.time ? formatTime(run.time) : 'No time'}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
};