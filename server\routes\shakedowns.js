import express from 'express';
import pool from '../config/db.js';
import { verifyToken, verifyAdmin } from '../middleware/auth.js';

const router = express.Router();

// Get all shakedowns - ADMIN ROUTE
router.get('/', verifyToken, verifyAdmin, async (req, res) => {
  try {
    const result = await pool.query(`
      SELECT s.*, r.name as rally_name
      FROM shakedowns s
      JOIN rallies r ON s.rally_id = r.id
      ORDER BY s.date DESC
    `);

    res.json(result.rows);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Failed to fetch shakedowns' });
  }
});

// Create shakedown
router.post('/', verifyToken, verifyAdmin, async (req, res) => {
  try {
    const {
      rally_id,
      name,
      location,
      length,
      date,
      start_time,
      end_time,
      max_runs
    } = req.body;

    const result = await pool.query(
      `INSERT INTO shakedowns (
        rally_id, name, location, length, date, start_time, end_time, max_runs
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8) RETURNING *`,
      [rally_id, name, location, length, date, start_time, end_time, max_runs || 3]
    );

    res.status(201).json(result.rows[0]);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Failed to create shakedown' });
  }
});

// Get shakedown for a rally
router.get('/rally/:rallyId', async (req, res) => {
  try {
    const { rallyId } = req.params;
    const result = await pool.query(
      `SELECT * FROM shakedowns WHERE rally_id = $1`,
      [rallyId]
    );

    if (result.rows.length > 0) {
      res.json(result.rows[0]);
    } else {
      res.json(null);
    }
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Failed to fetch shakedown' });
  }
});

// Add a shakedown run
router.post('/runs', verifyToken, verifyAdmin, async (req, res) => {
  try {
    const { shakedown_id, entry_id, run_number, time } = req.body;

    const result = await pool.query(
      `INSERT INTO shakedown_runs (
        shakedown_id, entry_id, run_number, time, timestamp
      ) VALUES ($1, $2, $3, $4, NOW())
      ON CONFLICT (shakedown_id, entry_id, run_number)
      DO UPDATE SET time = $4, timestamp = NOW()
      RETURNING *`,
      [shakedown_id, entry_id, run_number, time]
    );

    res.status(201).json(result.rows[0]);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Failed to add shakedown run' });
  }
});

// Get all runs for a shakedown
router.get('/:shakedownId/runs', async (req, res) => {
  try {
    const { shakedownId } = req.params;

    const result = await pool.query(`
      SELECT sr.*,
        e.number as car_number,
        e.car,
        p_driver.first_name as driver_first_name,
        p_driver.last_name as driver_last_name,
        p_codriver.first_name as codriver_first_name,
        p_codriver.last_name as codriver_last_name
      FROM shakedown_runs sr
      JOIN entries e ON sr.entry_id = e.id
      JOIN persons p_driver ON e.driver_id = p_driver.id
      JOIN persons p_codriver ON e.codriver_id = p_codriver.id
      WHERE sr.shakedown_id = $1
      ORDER BY sr.run_number, sr.time NULLS LAST
    `, [shakedownId]);

    res.json(result.rows);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Failed to fetch shakedown runs' });
  }
});

// Get best times for a shakedown
router.get('/:shakedownId/best-times', async (req, res) => {
  try {
    const { shakedownId } = req.params;

    const result = await pool.query(`
      WITH best_times AS (
        SELECT
          entry_id,
          MIN(time) as best_time
        FROM shakedown_runs
        WHERE shakedown_id = $1 AND time IS NOT NULL
        GROUP BY entry_id
      )
      SELECT
        bt.entry_id,
        bt.best_time,
        e.number as car_number,
        e.car,
        p_driver.first_name as driver_first_name,
        p_driver.last_name as driver_last_name,
        p_codriver.first_name as codriver_first_name,
        p_codriver.last_name as codriver_last_name
      FROM best_times bt
      JOIN entries e ON bt.entry_id = e.id
      JOIN persons p_driver ON e.driver_id = p_driver.id
      JOIN persons p_codriver ON e.codriver_id = p_codriver.id
      ORDER BY bt.best_time
    `, [shakedownId]);

    res.json(result.rows);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Failed to fetch best times' });
  }
});

// Get a specific shakedown by ID - ADMIN ROUTE
router.get('/:id', verifyToken, verifyAdmin, async (req, res) => {
  try {
    const { id } = req.params;

    const result = await pool.query(`
      SELECT s.*, r.name as rally_name
      FROM shakedowns s
      JOIN rallies r ON s.rally_id = r.id
      WHERE s.id = $1
    `, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({ message: 'Shakedown not found' });
    }

    res.json(result.rows[0]);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Failed to fetch shakedown' });
  }
});

// Update a shakedown - ADMIN ROUTE
router.put('/:id', verifyToken, verifyAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const {
      rally_id,
      name,
      location,
      length,
      date,
      start_time,
      end_time,
      max_runs
    } = req.body;

    const result = await pool.query(
      `UPDATE shakedowns SET
        rally_id = $1,
        name = $2,
        location = $3,
        length = $4,
        date = $5,
        start_time = $6,
        end_time = $7,
        max_runs = $8,
        updated_at = NOW()
      WHERE id = $9
      RETURNING *`,
      [rally_id, name, location, length, date, start_time, end_time, max_runs, id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ message: 'Shakedown not found' });
    }

    res.json(result.rows[0]);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Failed to update shakedown' });
  }
});

// Delete a shakedown - ADMIN ROUTE
router.delete('/:id', verifyToken, verifyAdmin, async (req, res) => {
  try {
    const { id } = req.params;

    const result = await pool.query(
      'DELETE FROM shakedowns WHERE id = $1 RETURNING *',
      [id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ message: 'Shakedown not found' });
    }

    res.json({ message: 'Shakedown deleted successfully' });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Failed to delete shakedown' });
  }
});

// Delete a shakedown run - ADMIN ROUTE
router.delete('/runs/:id', verifyToken, verifyAdmin, async (req, res) => {
  try {
    const { id } = req.params;

    const result = await pool.query(
      'DELETE FROM shakedown_runs WHERE id = $1 RETURNING *',
      [id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ message: 'Shakedown run not found' });
    }

    res.json({ message: 'Shakedown run deleted successfully' });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Failed to delete shakedown run' });
  }
});

export default router;