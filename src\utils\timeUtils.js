/**
 * Format time in seconds to a readable format (MM:SS.ss)
 * @param {number} seconds - Time in seconds
 * @returns {string} - Formatted time string
 */
export const formatTime = (seconds) => {
  if (seconds === null || seconds === undefined || isNaN(seconds)) return '-';

  // Convert to number if it's a string
  const numSeconds = typeof seconds === 'string' ? parseFloat(seconds) : seconds;

  if (isNaN(numSeconds) || numSeconds < 0) return '-';

  // Handle hours when time exceeds 60 minutes
  const hours = Math.floor(numSeconds / 3600);
  const minutes = Math.floor((numSeconds % 3600) / 60);
  const remainingSeconds = (numSeconds % 60).toFixed(2);

  // Ensure the seconds part has leading zeros
  const formattedSeconds = remainingSeconds.padStart(5, '0');

  // Format with hours if needed
  if (hours > 0) {
    // Ensure minutes has leading zero when hours are displayed
    const formattedMinutes = minutes.toString().padStart(2, '0');
    return `${hours}:${formattedMinutes}:${formattedSeconds}`;
  }

  return `${minutes}:${formattedSeconds}`;
};

/**
 * Format time difference (gap) between two times
 * @param {number} seconds - Time difference in seconds
 * @returns {string} - Formatted time difference
 */
export const formatTimeDiff = (seconds) => {
  if (seconds === null || seconds === undefined) return '-';
  if (seconds === 0) return 'Leader';

  return '+' + formatTime(seconds);
};

/**
 * Format penalty time for display
 * @param {number} penaltyInSeconds - Penalty time in seconds
 * @returns {string} - Formatted penalty string
 */
export const formatPenalty = (penaltyInSeconds) => {
  // Convert to number and validate
  const penalty = typeof penaltyInSeconds === 'string' ? parseFloat(penaltyInSeconds) : penaltyInSeconds;

  if (!penalty || penalty <= 0 || isNaN(penalty)) return '';

  const minutes = Math.floor(penalty / 60);
  const seconds = penalty % 60;

  if (minutes > 0) {
    return `+${minutes}:${seconds.toFixed(0).padStart(2, '0')}`;
  } else {
    return `+${seconds.toFixed(0)}s`;
  }
};

/**
 * Calculate total time including penalties
 * @param {number} stageTime - Stage time in seconds
 * @param {number} penaltyTime - Penalty time in seconds
 * @returns {number} - Total time in seconds
 */
export const calculateTotalTime = (stageTime, penaltyTime) => {
  // Convert to numbers and validate
  const stage = typeof stageTime === 'string' ? parseFloat(stageTime) : stageTime;
  const penalty = typeof penaltyTime === 'string' ? parseFloat(penaltyTime) : (penaltyTime || 0);

  // Return stage time if either value is invalid
  if (isNaN(stage)) return 0;
  if (isNaN(penalty)) return stage;

  return stage + penalty;
};

