// ENUMS
export enum RallyStatus {
  Upcoming = 'upcoming',
  Running = 'running',
  Finished = 'finished',
}

export type Surface = 'gravel' | 'tarmac' | 'snow' | 'mixed';

export enum StageStatus {
  Upcoming = 'upcoming',
  Running = 'running',
  Finished = 'finished',
  Canceled = 'canceled',
}

export enum EntryStatus {
  Entered = 'entered',
  Running = 'running',
  Finished = 'finished',
  Retired = 'retired',
  DNS = 'dns',
  DNF = 'dnf',
  DSQ = 'dsq',
}

export enum ChampionshipType {
  International = 'international',
  National = 'national',
  Local = 'local',
}

export interface Person {
  id: string;
  first_name: string;
  last_name: string;
  nationality: string;
  date_of_birth?: string;
  photo_url?: string;
  bio?: string;
  created_at?: string;
  updated_at?: string;
}

export interface Driver extends Person {
  // Additional properties for frontend display
  name?: string;
  image?: string;
  flagCode?: string;
  flagImage?: string;
  totalWins?: number;
  totalRallies?: number;
  totalPodiums?: number;
  careerPoints?: number;
}
export interface Codriver extends Person {}

export interface Team {
  id: string;
  name: string;
  country: string;
  logo_url?: string;
  created_at?: string;
  updated_at?: string;
}

export interface Championship {
  id: string;
  name: string;
  year: number;
  type: ChampionshipType;
  description?: string;
  created_at?: string;
  updated_at?: string;
}

export interface RallyChampionship {
  id: string;
  name: string;
  year: number;
  coefficient: number;
}

export interface ChampionshipEvent {
  id: string;
  championship_id: string;
  rally_id: string;
  coefficient: number;
  created_at?: string;
  updated_at?: string;
}

export interface Rally {
  id: string;
  name: string;
  country: string;
  start_date: string;
  end_date: string;
  status: RallyStatus;
  surface: Surface;
  banner_url?: string;
  logo_url?: string;
  views?: number;
  current_stage?: string;
  current_stage_start?: string;
  created_at?: string;
  updated_at?: string;
  championship_names?: string; // joined field - comma separated names
  championships?: RallyChampionship[]; // array of championship objects
}

export interface Stage {
  id: string;
  rally_id: string;
  name: string;
  number: number;
  length: number;
  surface: Surface;
  start_time: string;
  status: StageStatus;
  is_power_stage: boolean;
  is_super_special: boolean;
  leg_number?: number; // Add leg number
  day_number?: number; // Add day number
  created_at?: string;
  updated_at?: string;
}

export interface Entry {
  id: string;
  rally_id: string;
  driver_id: string;
  codriver_id: string;
  team_id?: string;
  car: string;
  number: number;
  class: string;
  status: EntryStatus;
  created_at?: string;
  updated_at?: string;
  // joined fields
  driver_first_name?: string;
  driver_last_name?: string;
  driver_nationality?: string;
  codriver_first_name?: string;
  codriver_last_name?: string;
  codriver_nationality?: string;
  team_name?: string;
}

export interface Result {
  id: string;
  rally_id: string;
  stage_id: string;
  entry_id: string;
  time: number;
  nominal_time?: boolean;
  super_rally?: boolean;
  is_active?: boolean;
  penalty_time?: number;
  penalty_reason?: string;
  created_at?: string;
  updated_at?: string;
}

export interface Penalty {
  id: string;
  rally_id: string;
  stage_id?: string;
  stage_number?: number;
  entry_id: string;
  time: number;
  reason: string;
  created_at?: string;
  updated_at?: string;
}

export interface PowerStagePoints {
  id: string;
  rally_id: string;
  stage_id: string;
  entry_id: string;
  points: number;
  created_at?: string;
  updated_at?: string;
}

export interface News {
  id: string;
  title: string;
  content: string;
  excerpt: string;
  author_id: string;
  image_url?: string;
  created_at?: string;
  updated_at?: string;
  author_name?: string; // joined field
}

export interface Split {
  id: string;
  stage_id: string;
  entry_id: string;
  split_number: number;
  time: number;
  created_at?: string;
  updated_at?: string;
}

export interface Standings {
  driverId: string;
  position: number;
  points: number;
  wins: number;
}

export interface Shakedown {
  id: string;
  rally_id: string;
  name: string;
  location: string;
  length: number;
  date: string;
  start_time: string;
  end_time: string;
  max_runs: number;
  created_at?: string;
  updated_at?: string;
}

export interface ShakedownRun {
  id: string;
  shakedown_id: string;
  entry_id: string;
  run_number: number;
  time: number | null;
  timestamp: string;
  created_at?: string;

  // Joined fields
  car_number?: number;
  car?: string;
  driver_first_name?: string;
  driver_last_name?: string;
  codriver_first_name?: string;
  codriver_last_name?: string;
}

export interface ShakedownBestTime {
  entry_id: string;
  best_time: number;
  car_number: number;
  car: string;
  driver_first_name: string;
  driver_last_name: string;
  codriver_first_name: string;
  codriver_last_name: string;
}

export type ItineraryItemType =
  | 'shakedown'
  | 'start'
  | 'service'
  | 'regroup'
  | 'tyre_fitting'
  | 'remote_service'
  | 'refuel'
  | 'finish'
  | 'podium'
  | 'stage'
  | 'parc_ferme';

export interface ItineraryItem {
  id: string;
  rally_id: string;
  type: ItineraryItemType;
  name: string;
  location?: string;
  start_time: string;
  duration?: number;
  leg_number: number;
  day_number: number;
  order_in_day: number;
  related_id?: string;
  additional_info?: any;
  created_at?: string;
  updated_at?: string;
}

export interface ItinerarySummary {
  legs: {
    leg_number: number;
    day_number: number;
  }[];
  stageTotals: {
    leg_number: number;
    stage_count: number;
    stage_distance: number;
  }[];
  overallTotals: {
    total_stages: number;
    total_stage_distance: number;
  };
}

// Championship-specific types
export interface ChampionshipRally {
  championship_id: string;
  championship_name: string;
  championship_type: ChampionshipType;
  year: number;
  coefficient: number;
  created_at?: string;
}

export interface ChampionshipOverallClassification {
  championship_id: string;
  championship_name: string;
  rally_id: string;
  rally_name: string;
  entry_id: string;
  number: number;
  driver: string;
  codriver: string;
  class: string;
  car: string;
  total_time: number;
  championship_position: number;
  time_diff: number;
  coefficient: number;
  driver_nationality?: string;
  codriver_nationality?: string;
}

export interface ChampionshipPoints {
  championship_id: string;
  championship_name: string;
  rally_id: string;
  rally_name: string;
  driver_id: string;
  driver: string;
  entry_id: string;
  number: number;
  class: string;
  championship_position: number;
  total_time: number;
  rally_points: number;
  coefficient: number;
  start_date?: string;
  end_date?: string;
  rally_status?: RallyStatus;
}

export interface ChampionshipStandings {
  championship_id: string;
  championship_name: string;
  championship_year: number;
  driver: string;
  driver_nationality?: string;
  rallies_participated: number;
  total_points: number;
  average_points: number;
  best_result_points: number;
  best_position: number;
  wins: number;
  podiums: number;
  points_finishes: number;
  championship_position: number;
  // Legacy fields for compatibility
  position?: number;
  total_power_stage_points?: number;
  grand_total_points?: number;
}

export interface ChampionshipClassStandings {
  championship_id: string;
  championship_name: string;
  class: string;
  driver: string;
  driver_nationality?: string;
  rallies_participated: number;
  total_points: number;
  class_position: number;
  // Legacy fields for compatibility
  class_points?: number;
  total_power_stage_points?: number;
  grand_total_points?: number;
}

export interface ChampionshipClass {
  class_name: string;
  driver_count: number;
}

export interface ChampionshipCalendar extends Rally {
  coefficient: number;
  entry_count: number;
}
