import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { Flag, Edit, Search, RefreshCw, Upload } from 'lucide-react';
import { getCountryCode } from '../../../utils/countryUtils';

interface Rally {
  id: string;
  name: string;
  country: string;
  start_date: string;
  end_date: string;
  status: string;
  surface: string;
  logo_url?: string;
}

const RalliesManagementPage: React.FC = () => {
  const [rallies, setRallies] = useState<Rally[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    fetchRallies();
  }, []);

  const fetchRallies = async () => {
    setLoading(true);
    try {
      const res = await fetch('/api/rallies', { credentials: 'include' });
      if (!res.ok) throw new Error('Failed to fetch rallies');
      const data = await res.json();
      setRallies(Array.isArray(data) ? data : data.rallies || []);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: string) => {
    if (!window.confirm('Delete this rally?')) return;
    try {
      const res = await fetch(`/api/rallies/${id}`, {
        method: 'DELETE',
        credentials: 'include',
      });
      if (!res.ok) throw new Error('Failed to delete');
      setRallies(rallies.filter(r => r.id !== id));
    } catch (err: any) {
      setError(err.message || 'Delete failed');
    }
  };

  const filteredRallies = rallies.filter(rally => {
    const searchLower = searchTerm.toLowerCase();
    return (
      rally.name.toLowerCase().includes(searchLower) ||
      rally.country.toLowerCase().includes(searchLower) ||
      rally.status.toLowerCase().includes(searchLower) ||
      rally.surface.toLowerCase().includes(searchLower)
    );
  });

  // Helper function to format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Rallies Management</h1>
        <div className="flex gap-3">
          <Link
            to="/admin/rallies/import"
            className="bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center hover:bg-blue-700 transition-colors"
          >
            <Upload className="w-4 h-4 mr-2" />
            Import EWRC
          </Link>
          <Link
            to="/admin/rallies/add"
            className="bg-red-600 text-white px-4 py-2 rounded-lg flex items-center hover:bg-red-700 transition-colors"
          >
            <Flag className="w-4 h-4 mr-2" />
            Add Rally
          </Link>
        </div>
      </div>

      <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
        <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex flex-col md:flex-row justify-between items-center space-y-2 md:space-y-0">
          <div className="relative w-full md:w-64">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search rallies..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 w-full rounded border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
            />
          </div>
          <button
            onClick={fetchRallies}
            className="flex items-center text-gray-700 dark:text-gray-300 hover:text-red-600 dark:hover:text-red-400"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </button>
        </div>

        {error && (
          <div className="p-4 bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-200">
            {error}
          </div>
        )}

        {loading ? (
          <div className="p-8 text-center">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-4 border-gray-300 border-t-red-600"></div>
            <p className="mt-2 text-gray-600 dark:text-gray-400">Loading rallies...</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Rally
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Country
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Start Date
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    End Date
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Status
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Surface
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {filteredRallies.length === 0 ? (
                  <tr>
                    <td colSpan={7} className="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                      No rallies found
                    </td>
                  </tr>
                ) : (
                  filteredRallies.map((rally) => {
                    const flagCode = getCountryCode(rally.country);
                    return (
                      <tr key={rally.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            {rally.logo_url ? (
                              <img
                                src={rally.logo_url}
                                alt={rally.name}
                                className="h-10 w-10 rounded-full mr-3 object-cover"
                              />
                            ) : (
                              <div className="h-10 w-10 rounded-full bg-gray-200 dark:bg-gray-600 mr-3 flex items-center justify-center">
                                <Flag className="h-5 w-5 text-gray-500 dark:text-gray-400" />
                              </div>
                            )}
                            <div className="text-sm font-medium text-gray-900 dark:text-white">
                              {rally.name}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                          <div className="flex items-center">
                            {flagCode && (
                              <span
                                className={`fi fi-${flagCode} mr-2`}
                                style={{ width: '20px', height: '15px' }}
                              ></span>
                            )}
                            {rally.country}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                          {formatDate(rally.start_date)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                          {formatDate(rally.end_date)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                          {rally.status}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                          {rally.surface}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <div className="flex justify-end space-x-3">
                            <Link
                              to={`/admin/rallies/${rally.id}`}
                              className="text-blue-600 hover:text-blue-900 dark:hover:text-blue-400 flex items-center"
                            >
                              <Edit className="w-4 h-4 mr-1" />
                              Edit
                            </Link>
                            <button
                              onClick={() => handleDelete(rally.id)}
                              className="text-red-600 hover:text-red-900 dark:hover:text-red-400 flex items-center"
                            >
                              Delete
                            </button>
                          </div>
                        </td>
                      </tr>
                    );
                  })
                )}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
};

export default RalliesManagementPage;
