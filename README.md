# Stagetime Rally Management System

A comprehensive rally management system built with React, Node.js, and PostgreSQL.

## 🏁 Overview

Stagetime is a modern web application for managing rally events, tracking results, and maintaining championship standings. It provides a complete solution for rally organizers, drivers, and fans.

## ✨ Features

### Rally Management
- **Event Creation & Management** - Create and manage rally events with stages, entries, and schedules
- **Stage Results** - Real-time stage timing and results tracking
- **Entry Management** - Driver/codriver registration and status tracking
- **Itinerary Management** - Complete rally schedule with service parks, regrouping, etc.

### Championship System
- **Multiple Championships** - Support for various championship types (national, international, local)
- **Class-Based Competition** - Flexible class system with multi-class entries
- **Points Calculation** - Automatic championship points calculation with coefficients
- **Standings & Statistics** - Real-time championship standings and driver statistics

### Data Management
- **EWRC Integration** - Import rally data from EWRC JSON format
- **Person Management** - Centralized driver/codriver database with nationality support
- **Activity Tracking** - Track entry status changes and stage activity
- **Penalty System** - Time penalties with stage-specific tracking

### User Interface
- **Responsive Design** - Mobile-friendly interface with modern UI
- **Admin Panel** - Comprehensive administration interface
- **Real-time Updates** - Live results and standings updates
- **Flag Support** - Country flags using flag-icons library

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ 
- PostgreSQL 14+
- npm or yarn

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd stagetime3v2
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up the database**
   ```bash
   # Create PostgreSQL database
   createdb stagetime_db
   
   # Run the schema
   psql stagetime_db -f StageTimeSchema.sql
   ```

4. **Configure environment**
   ```bash
   # Copy environment template
   cp .env.example .env
   
   # Edit .env with your database credentials
   ```

5. **Start the application**
   ```bash
   # Development mode
   npm run dev
   
   # Production mode
   npm run build
   npm start
   ```

## 📁 Project Structure

```
stagetime3v2/
├── src/                    # Frontend React application
│   ├── components/         # React components
│   ├── pages/             # Page components
│   ├── hooks/             # Custom React hooks
│   ├── utils/             # Utility functions
│   └── types/             # TypeScript type definitions
├── server/                # Backend Node.js application
│   ├── routes/            # API route handlers
│   ├── middleware/        # Express middleware
│   ├── config/            # Configuration files
│   └── utils/             # Server utilities
├── public/                # Static assets
├── docs/                  # Documentation files
└── StageTimeSchema.sql    # Database schema
```

## 📚 Documentation

### Core Documentation
- **[Database Schema](DATABASE_SCHEMA_README.md)** - Complete database structure and relationships
- **[Server Changes](server-changes.md)** - Server-side implementation details
- **[Migration Guide](migration-guide.md)** - Next.js migration information

### Development Guides
- **[Championship Implementation](docs/CHAMPIONSHIP_GUIDE.md)** - Championship system implementation
- **[API Documentation](docs/API.md)** - REST API endpoints and usage
- **[Frontend Components](docs/COMPONENTS.md)** - React component documentation

## 🗄️ Database

The application uses PostgreSQL with a comprehensive schema supporting:

- **21 Tables** - Complete rally management data model
- **13 Views** - Optimized queries for standings and statistics  
- **43 Indexes** - Performance-optimized database structure
- **4 ENUM Types** - Type-safe status and classification system

### Key Tables
- `rallies` - Rally events and metadata
- `stages` - Individual rally stages
- `entries` - Rally participants (driver + codriver + car)
- `results` - Stage timing results
- `championships` - Championship definitions
- `persons` - Centralized people database

## 🏆 Championship System

### Features
- **Multi-Championship Support** - Multiple championships per rally
- **Class-Based Eligibility** - Flexible class patterns for championship eligibility
- **Points System** - Configurable points with rally coefficients
- **Entry Tracking** - Explicit championship participation tracking

### Championship Types
- **Greece Championship** - National championship with C1-C6 classes
- **Historic Championships** - Historic vehicle categories
- **Rally3 Championship** - Specific Rally3 vehicle class
- **Regional Cups** - Local and regional competitions

## 🔧 Development

### Available Scripts
- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint
- `npm test` - Run tests

### Database Management
- `server/db/test_remote_connection.js` - Test database connectivity
- `server/db/check_schema.sql` - Verify database schema
- `check_database_vs_schema.sql` - Compare live DB with schema

## 📊 Data Import

### EWRC Integration
The system supports importing rally data from EWRC (European Rally Website) JSON format:

- **Rally Information** - Event details, stages, and metadata
- **Entry Lists** - Driver/codriver information with cars
- **Results** - Stage times and overall classifications
- **Championships** - Championship associations per entry

### CSV Import
- **Stage Results** - Bulk import of stage timing data
- **Person Data** - Import driver/codriver information

## 🛡️ Security

- **JWT Authentication** - Secure API access
- **Role-Based Access** - Admin and editor roles
- **Input Validation** - Comprehensive data validation
- **SQL Injection Protection** - Parameterized queries

## 🌍 Internationalization

- **Multi-Language Support** - Ready for localization
- **Country Flags** - Flag-icons library integration
- **Nationality Tracking** - Driver/codriver nationality support

## 📈 Performance

- **Optimized Queries** - Database views for complex operations
- **Indexed Tables** - Performance-critical indexes
- **Lazy Loading** - Component-level code splitting
- **Caching** - Strategic data caching

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Check the documentation in the `docs/` folder
- Review the database schema documentation
- Check existing issues in the repository

---

**Built with ❤️ for the rally community**
