-- Fix Championship Positions
-- The ranking is wrong - let's debug and fix it

-- 1. Check the current times and positions for Greece Championship
SELECT 'Current Greece Championship times and positions:' as debug;
SELECT 
    driver,
    class,
    total_time,
    championship_position,
    RANK() OVER (ORDER BY total_time) as correct_rank_by_time
FROM championship_overall_classification
WHERE championship_name = 'Greece' 
AND rally_name = 'Rally Stereas Elladas 2025'
ORDER BY total_time
LIMIT 15;

-- 2. Check if there are NULL times or other issues
SELECT 'Checking for data issues:' as debug;
SELECT 
    driver,
    class,
    total_time,
    championship_position,
    CASE 
        WHEN total_time IS NULL THEN 'NULL TIME'
        WHEN total_time <= 0 THEN 'ZERO/NEGATIVE TIME'
        ELSE 'OK'
    END as time_status
FROM championship_overall_classification
WHERE championship_name = 'Greece' 
AND rally_name = 'Rally Stereas Elladas 2025'
AND (total_time IS NULL OR total_time <= 0 OR championship_position != RANK() OVER (ORDER BY total_time))
ORDER BY total_time;

-- 3. The problem might be in the view logic - let's recreate it with simpler logic
DROP VIEW IF EXISTS championship_overall_classification CASCADE;

CREATE VIEW championship_overall_classification AS
WITH championship_times AS (
  SELECT
    ce.championship_id,
    c.name AS championship_name,
    e.rally_id,
    r.name AS rally_name,
    e.id AS entry_id,
    e.number,
    pd.first_name || ' ' || pd.last_name AS driver,
    pcd.first_name || ' ' || pcd.last_name AS codriver,
    e.class,
    e.car,
    SUM(res.time + COALESCE(p.time, 0)) AS total_time,
    ce.coefficient
  FROM championship_events ce
  JOIN championships c ON c.id = ce.championship_id
  JOIN rallies r ON r.id = ce.rally_id
  JOIN entries e ON e.rally_id = ce.rally_id
  JOIN results res ON res.entry_id = e.id
  JOIN persons pd ON pd.id = e.driver_id
  JOIN persons pcd ON pcd.id = e.codriver_id
  LEFT JOIN penalties p ON p.entry_id = e.id AND p.stage_id = res.stage_id
  WHERE e.status NOT IN ('retired', 'dnf', 'dsq')
  -- CHAMPIONSHIP ELIGIBILITY FILTER
  AND EXISTS (
      SELECT 1 FROM championship_eligibility elig 
      WHERE elig.championship_id = ce.championship_id 
      AND (
          -- For Historic championships (numeric classes): EXACT match only
          (c.name LIKE '%Historic%' AND e.class = elig.class_pattern) OR
          -- For other championships: CONTAINS match
          (c.name NOT LIKE '%Historic%' AND (
              e.class = elig.class_pattern OR 
              e.class LIKE '%' || elig.class_pattern || '%'
          ))
      )
  )
  GROUP BY ce.championship_id, c.name, e.rally_id, r.name, e.id, e.number,
           pd.first_name, pd.last_name, pcd.first_name, pcd.last_name, e.class, e.car, ce.coefficient
)
SELECT 
  championship_id,
  championship_name,
  rally_id,
  rally_name,
  entry_id,
  number,
  driver,
  codriver,
  class,
  car,
  total_time,
  -- FIXED: Proper ranking by total time (fastest = position 1)
  RANK() OVER (
    PARTITION BY championship_id, rally_id
    ORDER BY total_time ASC
  ) AS championship_position,
  -- Time difference from leader
  total_time - MIN(total_time) OVER (
    PARTITION BY championship_id, rally_id
  ) AS time_diff,
  coefficient
FROM championship_times;

-- 4. Recreate dependent views
CREATE VIEW championship_points AS
SELECT
  ce.championship_id,
  c.name AS championship_name,
  e.rally_id,
  r.name AS rally_name,
  e.driver_id,
  pd.first_name || ' ' || pd.last_name AS driver,
  e.id AS entry_id,
  e.number,
  e.class,
  coc.championship_position,
  coc.total_time,
  CASE
    WHEN coc.championship_position = 1 THEN 25 * ce.coefficient
    WHEN coc.championship_position = 2 THEN 18 * ce.coefficient
    WHEN coc.championship_position = 3 THEN 15 * ce.coefficient
    WHEN coc.championship_position = 4 THEN 12 * ce.coefficient
    WHEN coc.championship_position = 5 THEN 10 * ce.coefficient
    WHEN coc.championship_position = 6 THEN 8 * ce.coefficient
    WHEN coc.championship_position = 7 THEN 6 * ce.coefficient
    WHEN coc.championship_position = 8 THEN 4 * ce.coefficient
    WHEN coc.championship_position = 9 THEN 2 * ce.coefficient
    WHEN coc.championship_position = 10 THEN 1 * ce.coefficient
    ELSE 0
  END AS rally_points,
  ce.coefficient
FROM championship_events ce
JOIN championships c ON c.id = ce.championship_id
JOIN rallies r ON r.id = ce.rally_id
JOIN championship_overall_classification coc ON coc.championship_id = ce.championship_id
  AND coc.rally_id = ce.rally_id
JOIN entries e ON e.id = coc.entry_id
JOIN persons pd ON pd.id = e.driver_id;

CREATE VIEW championship_standings AS
SELECT
  cp.championship_id,
  cp.championship_name,
  cp.driver_id,
  cp.driver,
  COUNT(DISTINCT cp.rally_id) AS rallies_completed,
  SUM(cp.rally_points) AS total_points,
  RANK() OVER (PARTITION BY cp.championship_id ORDER BY SUM(cp.rally_points) DESC) AS position,
  COALESCE(SUM(psp_total.power_stage_points), 0) AS total_power_stage_points,
  SUM(cp.rally_points) + COALESCE(SUM(psp_total.power_stage_points), 0) AS grand_total_points
FROM championship_points cp
LEFT JOIN (
  SELECT
    ce.championship_id,
    e.driver_id,
    SUM(psp.points) AS power_stage_points
  FROM championship_events ce
  JOIN power_stage_points psp ON psp.rally_id = ce.rally_id
  JOIN entries e ON e.id = psp.entry_id
  GROUP BY ce.championship_id, e.driver_id
) psp_total ON psp_total.championship_id = cp.championship_id AND psp_total.driver_id = cp.driver_id
GROUP BY cp.championship_id, cp.championship_name, cp.driver_id, cp.driver;

-- 5. Test the fix
SELECT 'FIXED - Greece Championship Results (should show fastest driver as position 1):' as test;
SELECT 
    championship_position,
    driver,
    class,
    total_time,
    total_time - MIN(total_time) OVER () as time_behind_leader
FROM championship_overall_classification
WHERE championship_name = 'Greece' 
AND rally_name = 'Rally Stereas Elladas 2025'
ORDER BY championship_position
LIMIT 10;

SELECT 'FIXED - Historic Championship Results:' as test;
SELECT 
    championship_position,
    driver,
    class,
    total_time
FROM championship_overall_classification
WHERE championship_name = 'Historic' 
AND rally_name = 'Rally Stereas Elladas 2025'
ORDER BY championship_position
LIMIT 10;

SELECT 'Championship positions fixed!' as result;
