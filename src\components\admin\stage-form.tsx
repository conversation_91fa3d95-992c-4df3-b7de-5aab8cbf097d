import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

interface Rally {
  id: string;
  name: string;
}

interface StageFormProps {
  initialData?: any;
  onSubmit?: (formData: any) => Promise<void>;
  onDelete?: () => Promise<void>;
  preselectedRallyId?: string;
}

const StageForm: React.FC<StageFormProps> = ({
  initialData,
  onSubmit,
  onDelete,
  preselectedRallyId
}) => {
  const [form, setForm] = useState({
    rally_id: '',
    number: '',
    name: '',
    length: '',
    surface: '',
    start_time: '',
    is_super_special: false,
    is_power_stage: false,
    status: '',
    leg_number: '1',
    day_number: '1'
  });
  const [rallies, setRallies] = useState<Rally[]>([]);
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const navigate = useNavigate();

  // Surface options - use lowercase to match database expectations
  const surfaceOptions = [
    { value: 'tarmac', label: 'Tarmac' },
    { value: 'gravel', label: 'Gravel' },
    { value: 'snow', label: 'Snow' },
    { value: 'mixed', label: 'Mixed' },
  ];

  // Status options must match the stage_status ENUM in the database
  // CREATE TYPE stage_status AS ENUM ('upcoming', 'running', 'finished', 'canceled');
  const statusOptions = [
    { value: 'upcoming', label: 'Upcoming' },
    { value: 'running', label: 'Running' },
    { value: 'finished', label: 'Finished' },
    { value: 'canceled', label: 'Canceled' },
  ];

  useEffect(() => {
    fetchRallies();
  }, []);

  useEffect(() => {
    if (initialData) {
      setForm({
        rally_id: initialData.rally_id || '',
        number: initialData.number !== undefined && initialData.number !== null ? String(initialData.number) : '',
        name: initialData.name || '',
        length: initialData.length !== undefined && initialData.length !== null ? String(initialData.length) : '',
        surface: initialData.surface || '',
        start_time: initialData.start_time ? initialData.start_time.slice(0, 16) : '',
        is_super_special: !!initialData.is_super_special,
        is_power_stage: !!initialData.is_power_stage,
        status: initialData.status || '',
        leg_number: initialData.leg_number ? String(initialData.leg_number) : '1',
        day_number: initialData.day_number ? String(initialData.day_number) : '1'
      });
    } else if (preselectedRallyId) {
      setForm(prev => ({ ...prev, rally_id: preselectedRallyId }));
    }
  }, [initialData, preselectedRallyId]);

  const fetchRallies = async () => {
    try {
      const res = await fetch('/api/rallies', { credentials: 'include' });
      if (!res.ok) throw new Error('Failed to fetch rallies');
      const data = await res.json();
      setRallies(Array.isArray(data) ? data : data.rallies || []);
    } catch (err: any) {
      setError(err.message);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;

    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setForm({ ...form, [name]: checked });
    } else {
      setForm({ ...form, [name]: value });
    }
  };

  const validateForm = () => {
    const errors: Record<string, string> = {};

    if (!form.rally_id) errors.rally_id = 'Rally is required';
    if (!form.name.trim()) errors.name = 'Stage name is required';
    if (!form.number.trim()) errors.number = 'Stage number is required';
    if (isNaN(Number(form.number))) errors.number = 'Stage number must be a number';
    if (!form.length.trim()) errors.length = 'Stage length is required';
    if (isNaN(Number(form.length))) errors.length = 'Stage length must be a number';
    if (!form.surface) errors.surface = 'Surface is required';
    if (!form.status) errors.status = 'Status is required';
    if (!form.start_time) errors.start_time = 'Start time is required';
    if (!form.leg_number.trim()) errors.leg_number = 'Leg number is required';
    if (isNaN(Number(form.leg_number))) errors.leg_number = 'Leg number must be a number';
    if (!form.day_number.trim()) errors.day_number = 'Day number is required';
    if (isNaN(Number(form.day_number))) errors.day_number = 'Day number must be a number';

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    setLoading(true);
    setError('');

    try {
      // Format the form data for submission
      const formattedData = {
        ...form,
        number: parseInt(form.number),
        length: parseFloat(form.length),
        leg_number: parseInt(form.leg_number),
        day_number: parseInt(form.day_number),
        start_time: form.start_time ? new Date(form.start_time).toISOString() : null
      };

      if (onSubmit) {
        await onSubmit(formattedData);
      } else {
        // Default submission logic if no onSubmit provided
        const url = initialData ? `/api/stages/${initialData.id}` : '/api/stages';
        const method = initialData ? 'PUT' : 'POST';

        // Log the form data being sent
        console.log('Submitting stage form data:', formattedData);

        const response = await fetch(url, {
          method,
          headers: { 'Content-Type': 'application/json' },
          credentials: 'include',
          body: JSON.stringify(formattedData)
        });

        // Log the response
        console.log('Response status:', response.status);
        const responseData = await response.json();
        console.log('Response data:', responseData);

        if (!response.ok) {
          throw new Error(responseData.message || responseData.error || `Failed to ${initialData ? 'update' : 'add'} stage`);
        }

        navigate('/admin/stages');
      }
    } catch (err: any) {
      setError(err.message);
      window.scrollTo(0, 0); // Scroll to top to show error
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!initialData || !onDelete) return;

    if (!window.confirm('Are you sure you want to delete this stage?')) {
      return;
    }

    try {
      await onDelete();
    } catch (err: any) {
      setError(err.message);
    }
  };

  const handleCancel = () => {
    navigate('/admin/stages');
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      {error && (
        <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Rally
          </label>
          <select
            name="rally_id"
            value={form.rally_id}
            onChange={handleChange}
            required
            className={`w-full px-3 py-2 rounded border ${
              validationErrors.rally_id ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
            } dark:bg-gray-700 dark:text-white`}
          >
            <option value="">Select Rally</option>
            {rallies.map(rally => (
              <option key={rally.id} value={rally.id}>{rally.name}</option>
            ))}
          </select>
          {validationErrors.rally_id && (
            <p className="mt-1 text-sm text-red-500">{validationErrors.rally_id}</p>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Stage Number
            </label>
            <input
              name="number"
              value={form.number}
              onChange={handleChange}
              required
              type="number"
              min="1"
              placeholder="Stage Number"
              className={`w-full px-3 py-2 rounded border ${
                validationErrors.number ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
              } dark:bg-gray-700 dark:text-white`}
            />
            {validationErrors.number && (
              <p className="mt-1 text-sm text-red-500">{validationErrors.number}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Stage Name
            </label>
            <input
              name="name"
              value={form.name}
              onChange={handleChange}
              required
              placeholder="Stage Name"
              className={`w-full px-3 py-2 rounded border ${
                validationErrors.name ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
              } dark:bg-gray-700 dark:text-white`}
            />
            {validationErrors.name && (
              <p className="mt-1 text-sm text-red-500">{validationErrors.name}</p>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Length (km)
            </label>
            <input
              name="length"
              value={form.length}
              onChange={handleChange}
              required
              type="number"
              step="0.01"
              min="0.01"
              placeholder="Length in km"
              className={`w-full px-3 py-2 rounded border ${
                validationErrors.length ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
              } dark:bg-gray-700 dark:text-white`}
            />
            {validationErrors.length && (
              <p className="mt-1 text-sm text-red-500">{validationErrors.length}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Surface
            </label>
            <select
              name="surface"
              value={form.surface}
              onChange={handleChange}
              required
              className={`w-full px-3 py-2 rounded border ${
                validationErrors.surface ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
              } dark:bg-gray-700 dark:text-white`}
            >
              <option value="">Select Surface</option>
              {surfaceOptions.map(option => (
                <option key={option.value} value={option.value}>{option.label}</option>
              ))}
            </select>
            {validationErrors.surface && (
              <p className="mt-1 text-sm text-red-500">{validationErrors.surface}</p>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Start Time
            </label>
            <input
              name="start_time"
              value={form.start_time}
              onChange={handleChange}
              required
              type="datetime-local"
              className={`w-full px-3 py-2 rounded border ${
                validationErrors.start_time ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
              } dark:bg-gray-700 dark:text-white`}
            />
            {validationErrors.start_time && (
              <p className="mt-1 text-sm text-red-500">{validationErrors.start_time}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Status
            </label>
            <select
              name="status"
              value={form.status}
              onChange={handleChange}
              required
              className={`w-full px-3 py-2 rounded border ${
                validationErrors.status ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
              } dark:bg-gray-700 dark:text-white`}
            >
              <option value="">Select Status</option>
              {statusOptions.map(option => (
                <option key={option.value} value={option.value}>{option.label}</option>
              ))}
            </select>
            {validationErrors.status && (
              <p className="mt-1 text-sm text-red-500">{validationErrors.status}</p>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Leg Number
            </label>
            <input
              name="leg_number"
              value={form.leg_number}
              onChange={handleChange}
              required
              type="number"
              min="1"
              placeholder="Leg Number"
              className={`w-full px-3 py-2 rounded border ${
                validationErrors.leg_number ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
              } dark:bg-gray-700 dark:text-white`}
            />
            {validationErrors.leg_number && (
              <p className="mt-1 text-sm text-red-500">{validationErrors.leg_number}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Day Number
            </label>
            <input
              name="day_number"
              value={form.day_number}
              onChange={handleChange}
              required
              type="number"
              min="1"
              placeholder="Day Number"
              className={`w-full px-3 py-2 rounded border ${
                validationErrors.day_number ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
              } dark:bg-gray-700 dark:text-white`}
            />
            {validationErrors.day_number && (
              <p className="mt-1 text-sm text-red-500">{validationErrors.day_number}</p>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="flex items-center">
            <input
              type="checkbox"
              id="is_super_special"
              name="is_super_special"
              checked={form.is_super_special}
              onChange={handleChange}
              className="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded"
            />
            <label htmlFor="is_super_special" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
              Super Special Stage
            </label>
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              id="is_power_stage"
              name="is_power_stage"
              checked={form.is_power_stage}
              onChange={handleChange}
              className="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded"
            />
            <label htmlFor="is_power_stage" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
              Power Stage
            </label>
          </div>
        </div>

        <div className="flex justify-between pt-4">
          <div>
            {initialData && onDelete && (
              <button
                type="button"
                onClick={handleDelete}
                className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
              >
                Delete
              </button>
            )}
          </div>
          <div className="flex space-x-3">
            <button
              type="button"
              onClick={handleCancel}
              className="px-4 py-2 border border-gray-300 rounded text-gray-700 bg-white hover:bg-gray-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:hover:bg-gray-600"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors disabled:opacity-50"
            >
              {loading ? (initialData ? 'Saving...' : 'Adding...') : (initialData ? 'Save Changes' : 'Add Stage')}
            </button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default StageForm;
