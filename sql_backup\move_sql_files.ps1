# Move experimental and debug SQL files to backup folder

$filesToMove = @(
    "cleanup_championship_mess.sql",
    "cleanup_rallies_and_persons.sql", 
    "create_championships.sql",
    "create_championships_simple.sql",
    "create_entry_based_championship_views.sql",
    "create_entry_championships_table.sql",
    "debug_championship_filtering.sql",
    "debug_championship_points.js",
    "debug_championship_positions.js",
    "debug_papadimitriou_position.js",
    "debug_time_sources.sql",
    "drop_and_recreate_all_championship_views.sql",
    "fix_championship_class_filtering.sql",
    "fix_championship_filtering.sql",
    "fix_championship_positions.sql",
    "fix_name_parsing.sql",
    "fix_rally_championships.sql",
    "fix_use_overall_classification.sql",
    "identify_rally3_cars.sql",
    "identify_stereas_championships.sql",
    "minimal_championship_system.sql",
    "populate_championships.sql",
    "populate_specific_championships.sql",
    "proper_championship_solution.sql",
    "rally3_c2_solution.sql",
    "recreate_championship_views.sql",
    "restore_original_championships.sql",
    "restore_original_views.sql",
    "scalable_championship_structure.sql",
    "setup_stereas_elladas_championships.sql",
    "setup_stereas_elladas_exact_championships.sql",
    "setup_universal_championships.sql",
    "simple_championship_api.js",
    "simple_championship_system.sql",
    "simple_championship_tables.sql",
    "simple_debug_positions.sql",
    "simple_rally3_class_update.sql",
    "simple_time_debug.sql",
    "stereas_elladas_championship_setup.sql",
    "test_championship_api.js",
    "test_championship_associations.js",
    "test_championship_import.js",
    "test_championship_integration.sql",
    "test_championships.js",
    "test_historic_championship.js",
    "test_import.js",
    "test_import_api.js",
    "universal_championship_system.sql",
    "verify_championship_entries.sql",
    "verify_correct_names.sql"
)

foreach ($file in $filesToMove) {
    if (Test-Path $file) {
        Write-Host "Moving $file to sql_backup/"
        Move-Item $file sql_backup/
    } else {
        Write-Host "File not found: $file"
    }
}

Write-Host "Cleanup complete!"
