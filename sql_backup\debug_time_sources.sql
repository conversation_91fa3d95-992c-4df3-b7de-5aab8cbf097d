-- Debug Time Sources
-- Let's see where the championship times are coming from

-- 1. Check what's in the results table for Rally Stereas Elladas 2025
SELECT 'Results table data for Rally Stereas Elladas 2025:' as debug;
SELECT 
    pd.first_name || ' ' || pd.last_name as driver,
    e.class,
    s.name as stage_name,
    res.time as stage_time,
    COALESCE(p.time, 0) as penalty_time,
    res.time + COALESCE(p.time, 0) as total_stage_time
FROM results res
JOIN entries e ON res.entry_id = e.id
JOIN rallies r ON e.rally_id = r.id
JOIN persons pd ON e.driver_id = pd.id
JOIN stages s ON res.stage_id = s.id
LEFT JOIN penalties p ON p.entry_id = e.id AND p.stage_id = res.stage_id
WHERE r.name = 'Rally Stereas Elladas 2025'
AND pd.last_name LIKE '%Papadimitriou%'
ORDER BY s.stage_order, res.time;

-- 2. Calculate total time manually for Papadimitriou
SELECT 'Manual total time calculation for Papadimitriou:' as debug;
SELECT 
    pd.first_name || ' ' || pd.last_name as driver,
    e.class,
    COUNT(res.time) as stages_completed,
    SUM(res.time) as total_stage_times,
    SUM(COALESCE(p.time, 0)) as total_penalties,
    SUM(res.time + COALESCE(p.time, 0)) as total_rally_time
FROM results res
JOIN entries e ON res.entry_id = e.id
JOIN rallies r ON e.rally_id = r.id
JOIN persons pd ON e.driver_id = pd.id
LEFT JOIN penalties p ON p.entry_id = e.id AND p.stage_id = res.stage_id
WHERE r.name = 'Rally Stereas Elladas 2025'
AND pd.last_name LIKE '%Papadimitriou%'
GROUP BY e.id, pd.first_name, pd.last_name, e.class;

-- 3. Compare with championship view time
SELECT 'Championship view time for Papadimitriou:' as debug;
SELECT 
    driver,
    class,
    total_time,
    championship_position
FROM championship_overall_classification
WHERE championship_name = 'Greece' 
AND rally_name = 'Rally Stereas Elladas 2025'
AND driver LIKE '%Papadimitriou%';

-- 4. Check if we're using the right view - maybe we should use overall_classification instead
SELECT 'Overall classification view for comparison:' as debug;
SELECT 
    oc.driver,
    oc.position as overall_position,
    oc.total_time as overall_total_time,
    e.class
FROM overall_classification oc
JOIN entries e ON oc.entry_id = e.id
JOIN rallies r ON oc.rally_id = r.id
WHERE r.name = 'Rally Stereas Elladas 2025'
AND oc.driver LIKE '%Papadimitriou%';

-- 5. Compare top 5 drivers from overall_classification vs championship view
SELECT 'Top 5 from overall_classification:' as debug;
SELECT 
    oc.position,
    oc.driver,
    e.class,
    oc.total_time
FROM overall_classification oc
JOIN entries e ON oc.entry_id = e.id
JOIN rallies r ON oc.rally_id = r.id
WHERE r.name = 'Rally Stereas Elladas 2025'
ORDER BY oc.position
LIMIT 5;

-- 6. Check if championship view should use overall_classification times instead of calculating from results
SELECT 'Championship eligibility check for top overall drivers:' as debug;
SELECT 
    oc.position,
    oc.driver,
    e.class,
    oc.total_time,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM championship_eligibility elig 
            WHERE elig.championship_id = '55a003a9-66ff-4a37-b11d-2e14df10bae3'::uuid
            AND (
                e.class = elig.class_pattern OR 
                e.class LIKE '%' || elig.class_pattern || '%'
            )
        ) THEN 'ELIGIBLE FOR GREECE'
        ELSE 'NOT ELIGIBLE'
    END as greece_eligibility
FROM overall_classification oc
JOIN entries e ON oc.entry_id = e.id
JOIN rallies r ON oc.rally_id = r.id
WHERE r.name = 'Rally Stereas Elladas 2025'
ORDER BY oc.position
LIMIT 10;
