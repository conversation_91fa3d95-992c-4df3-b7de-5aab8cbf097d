/**
 * Get the ISO 3166-1 alpha-2 country code from a country name
 * @param countryName - The name of the country
 * @returns The country code or empty string if not found
 */
export function getCountryCode(countryName: string): string;

/**
 * Get the flag CSS class for a country name using flag-icons
 * @param countryName - The name of the country
 * @returns The flag CSS class or empty string if not found
 */
export function getFlagClass(countryName: string): string;

declare const _default: {
  getCountryCode: typeof getCountryCode;
  getFlagClass: typeof getFlagClass;
};

export default _default;
