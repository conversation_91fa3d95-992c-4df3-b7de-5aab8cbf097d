import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import { LayoutDashboard, Flag, Users, Trophy, FileText, LogOut, ClipboardList, Timer, AlertTriangle, Award, Divide, UserPlus, Users as UsersIcon, Clock } from 'lucide-react';

const DashboardPage: React.FC = () => {
  const { user, signOut } = useAuth();
  const navigate = useNavigate();

  const handleSignOut = async () => {
    try {
      await signOut();
      navigate('/admin/login');
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  const [stats, setStats] = React.useState([
    { label: 'Total Rallies', value: '-', icon: <Flag className="h-5 w-5 text-red-600" /> },
    { label: 'Persons', value: '-', icon: <Users className="h-5 w-5 text-red-600" /> },
    { label: 'Entries', value: '-', icon: <ClipboardList className="h-5 w-5 text-red-600" /> },
    { label: 'Stages', value: '-', icon: <Timer className="h-5 w-5 text-red-600" /> },
    { label: 'Stage Results', value: '-', icon: <Trophy className="h-5 w-5 text-red-600" /> },
    { label: 'Penalties', value: '-', icon: <AlertTriangle className="h-5 w-5 text-red-600" /> },
    { label: 'News Posts', value: '-', icon: <FileText className="h-5 w-5 text-red-600" /> },
  ]);

  // Fetch stats for dashboard
  React.useEffect(() => {
    const fetchStats = async () => {
      try {
        const [ralliesRes, personsRes, entriesRes, stagesRes, stageResultsRes, penaltiesRes, newsRes] = await Promise.all([
          fetch('/api/rallies', { credentials: 'include' }),
          fetch('/api/persons', { credentials: 'include' }),
          fetch('/api/entries', { credentials: 'include' }),
          fetch('/api/stages', { credentials: 'include' }),
          fetch('/api/stageResults', { credentials: 'include' }),
          fetch('/api/penalties', { credentials: 'include' }),
          fetch('/api/news', { credentials: 'include' })
        ]);
        const [rallies, persons, entries, stages, stageResults, penalties, news] = await Promise.all([
          ralliesRes.json(),
          personsRes.json(),
          entriesRes.json(),
          stagesRes.json(),
          stageResultsRes.json(),
          penaltiesRes.json(),
          newsRes.json()
        ]);
        setStats([
          { label: 'Total Rallies', value: (Array.isArray(rallies) ? rallies.length : (rallies.rallies?.length || 0)), icon: <Flag className="h-5 w-5 text-red-600" /> },
          { label: 'Persons', value: (Array.isArray(persons) ? persons.length : (persons.persons?.length || 0)), icon: <Users className="h-5 w-5 text-red-600" /> },
          { label: 'Entries', value: (Array.isArray(entries) ? entries.length : (entries.entries?.length || 0)), icon: <ClipboardList className="h-5 w-5 text-red-600" /> },
          { label: 'Stages', value: (Array.isArray(stages) ? stages.length : (stages.stages?.length || 0)), icon: <Timer className="h-5 w-5 text-red-600" /> },
          { label: 'Stage Results', value: (Array.isArray(stageResults) ? stageResults.length : (stageResults.stageResults?.length || 0)), icon: <Trophy className="h-5 w-5 text-red-600" /> },
          { label: 'Penalties', value: (Array.isArray(penalties) ? penalties.length : (penalties.penalties?.length || 0)), icon: <AlertTriangle className="h-5 w-5 text-red-600" /> },
          { label: 'News Posts', value: (Array.isArray(news) ? news.length : (news.news?.length || 0)), icon: <FileText className="h-5 w-5 text-red-600" /> },
        ]);
      } catch {
        setStats([
          { label: 'Total Rallies', value: '-', icon: <Flag className="h-5 w-5 text-red-600" /> },
          { label: 'Persons', value: '-', icon: <Users className="h-5 w-5 text-red-600" /> },
          { label: 'Entries', value: '-', icon: <ClipboardList className="h-5 w-5 text-red-600" /> },
          { label: 'Stages', value: '-', icon: <Timer className="h-5 w-5 text-red-600" /> },
          { label: 'Stage Results', value: '-', icon: <Trophy className="h-5 w-5 text-red-600" /> },
          { label: 'Penalties', value: '-', icon: <AlertTriangle className="h-5 w-5 text-red-600" /> },
          { label: 'News Posts', value: '-', icon: <FileText className="h-5 w-5 text-red-600" /> },
        ]);
      }
    };
    fetchStats();
  }, []);

  // Update quickActions to match DB tables
  const quickActions = [
    {
      to: '/admin/rallies/add',
      icon: <Flag className="mr-2 h-5 w-5" />, label: 'Add Rally', desc: 'Create a new rally', manageTo: '/admin/rallies', manageLabel: 'Manage Rallies',
    },
    {
      to: '/admin/persons/add',
      icon: <UserPlus className="mr-2 h-5 w-5" />, label: 'Add Person', desc: 'Register a new person', manageTo: '/admin/persons', manageLabel: 'Manage Persons',
    },

    {
      to: '/admin/entries/add',
      icon: <ClipboardList className="mr-2 h-5 w-5" />, label: 'Add Entry', desc: 'Register a rally entry', manageTo: '/admin/entries', manageLabel: 'Manage Entries',
    },
    {
      to: '/admin/stages/add',
      icon: <Timer className="mr-2 h-5 w-5" />, label: 'Add Stage', desc: 'Add a stage to a rally', manageTo: '/admin/stages', manageLabel: 'Manage Stages',
    },
    {
      to: '/admin/stageResults/add',
      icon: <Trophy className="mr-2 h-5 w-5" />, label: 'Add Stage Result', desc: 'Enter a stage result', manageTo: '/admin/stageResults', manageLabel: 'Manage Stage Results',
    },
    {
      to: '/admin/penalties/add',
      icon: <AlertTriangle className="mr-2 h-5 w-5" />, label: 'Add Penalty', desc: 'Record a penalty', manageTo: '/admin/penalties', manageLabel: 'Manage Penalties',
    },
    {
      to: '/admin/news/add',
      icon: <FileText className="mr-2 h-5 w-5" />, label: 'Add News', desc: 'Publish a news article', manageTo: '/admin/news', manageLabel: 'Manage News',
    },
    {
      to: '/admin/championships/add',
      icon: <Trophy className="mr-2 h-5 w-5" />, label: 'Add Championship', desc: 'Create a new championship', manageTo: '/admin/championships', manageLabel: 'Manage Championships',
    },
    {
      to: '/admin/powerstagepoints/add',
      icon: <Award className="mr-2 h-5 w-5" />, label: 'Add Power Stage Points', desc: 'Add power stage points', manageTo: '/admin/powerstagepoints', manageLabel: 'Manage Power Stage Points',
    },
    {
      to: '/admin/splits/add',
      icon: <Divide className="mr-2 h-5 w-5" />, label: 'Add Split', desc: 'Add a split time', manageTo: '/admin/splits', manageLabel: 'Manage Splits',
    },

    {
      to: '/admin/teams/add',
      icon: <UsersIcon className="mr-2 h-5 w-5" />, label: 'Add Team', desc: 'Register a new team', manageTo: '/admin/teams', manageLabel: 'Manage Teams',
    },
    {
      to: '/admin/shakedowns/add',
      icon: <Clock className="mr-2 h-5 w-5" />, label: 'Add Shakedown', desc: 'Create a new shakedown', manageTo: '/admin/shakedowns', manageLabel: 'Manage Shakedowns',
    },
  ];

  // Fetch recent rallies (replace with real API call if needed)
  const [recentRallies, setRecentRallies] = React.useState<any[]>([]);
  React.useEffect(() => {
    // Example fetch, replace with your real API endpoint if available
    const fetchRecent = async () => {
      try {
        const res = await fetch('/api/rallies', { credentials: 'include' });
        const data = await res.json();
        let rallies = Array.isArray(data) ? data : data.rallies || [];
        setRecentRallies(rallies.slice(0, 6));
      } catch {
        setRecentRallies([]);
      }
    };
    fetchRecent();
  }, []);

  return (
    <div className="min-h-screen bg-gray-100 dark:bg-gray-900">
      <div className="bg-white dark:bg-gray-800 shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <LayoutDashboard className="h-8 w-8 text-red-600" />
              <span className="ml-2 text-xl font-bold text-gray-900 dark:text-white">
                Admin Dashboard
              </span>
            </div>
            <div className="flex items-center">
              <span className="text-gray-600 dark:text-gray-300 mr-4">{user?.email}</span>
              <button
                onClick={handleSignOut}
                className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors flex items-center"
              >
                <LogOut className="w-4 h-4 mr-2" />
                Sign Out
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Stats Overview */}
      <div className="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {stats.map((stat) => (
            <div key={stat.label} className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 flex flex-col items-start">
              <div className="flex items-center justify-between w-full pb-2 border-b border-gray-200 dark:border-gray-700 mb-2">
                <span className="text-sm font-medium text-red-600 dark:text-red-400">{stat.label}</span>
                {stat.icon}
              </div>
              <div className="text-2xl font-bold text-gray-900 dark:text-white">{stat.value}</div>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">Overview</p>
            </div>
          ))}
        </div>

        {/* Quick Actions & Recent Rallies */}
        <div className="mb-8 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
          {/* Quick Actions */}
          {quickActions.map((action) => (
            <div key={action.to} className="flex flex-col gap-2 bg-white dark:bg-gray-800 rounded-lg shadow p-4 hover:bg-red-50 dark:hover:bg-gray-700 transition-colors">
              <div className="flex items-center mb-2">
                {action.icon}
                <span className="font-medium text-gray-900 dark:text-white">{action.label}</span>
              </div>
              <span className="text-xs text-gray-500 dark:text-gray-400 mb-2">{action.desc}</span>
              <div className="flex gap-2">
                <Link to={action.to} className="flex-1 bg-red-600 text-white text-center py-2 rounded hover:bg-red-700 transition-colors text-sm font-medium">
                  Add
                </Link>
                <Link to={action.manageTo} className="flex-1 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 text-center py-2 rounded hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors text-sm font-medium">
                  {action.manageLabel}
                </Link>
              </div>
            </div>
          ))}
          {/* Recent Rallies */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4 flex flex-col col-span-1 sm:col-span-2 md:col-span-3">
            <h2 className="text-lg font-semibold mb-2 text-red-600 dark:text-red-400">Recent Rallies</h2>
            <div className="divide-y">
              {recentRallies.length === 0 ? (
                <div className="p-2 text-center text-gray-500 dark:text-gray-400">No rallies found</div>
              ) : (
                recentRallies.map((rally) => (
                  <div key={rally.id} className="py-2 flex justify-between items-center">
                    <div>
                      <h3 className="font-medium text-gray-900 dark:text-white">{rally.name}</h3>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        {rally.country} • {new Date(rally.start_date).toLocaleDateString()}
                      </p>
                    </div>
                    <div className="flex gap-2">
                      <Link to={`/admin/rallies/${rally.id}/edit`} className="text-sm text-blue-600 hover:underline">Edit</Link>
                      <Link to={`/admin/rallies/${rally.id}`} className="text-sm text-red-600 hover:underline">Manage</Link>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default DashboardPage;