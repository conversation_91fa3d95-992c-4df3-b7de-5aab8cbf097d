import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { ClipboardList } from 'lucide-react';
import EntryForm from '../../../../components/admin/entry-form';

const AddEntryPage: React.FC = () => {
  const [error, setError] = useState('');
  const navigate = useNavigate();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const rallyId = queryParams.get('rally_id');

  const handleSubmit = async (formData: any) => {
    console.log('Submitting entry with data:', formData);
    
    const response = await fetch('/api/entries', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      credentials: 'include',
      body: JSON.stringify(formData)
    });

    // Log the response for debugging
    console.log('Response status:', response.status);
    
    if (!response.ok) {
      const data = await response.json();
      console.error('Error response:', data);
      throw new Error(data.message || data.error || 'Failed to add entry');
    }

    // If we came from a rally page, go back there
    if (rallyId) {
      navigate(`/admin/rallies/${rallyId}`);
    } else {
      navigate('/admin/entries');
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center mb-6">
        <ClipboardList className="w-6 h-6 text-red-600 mr-2" />
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Add Entry</h1>
      </div>

      {error && (
        <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          {error}
        </div>
      )}

      <EntryForm 
        onSubmit={handleSubmit} 
        preselectedRallyId={rallyId || undefined}
      />
    </div>
  );
};

export default AddEntryPage;
