-- Simple solution: Add Rally3 to existing class column for Rally3 cars
-- This works with your existing comma-separated class structure

-- First, let's see all C2 entries and their current classes
SELECT 'Current C2 entries in Rally Stereas Elladas 2025:' as info;
SELECT 
    e.number,
    CONCAT(p.first_name, ' ', p.last_name) as driver,
    e.car,
    e.class as current_class
FROM entries e
JOIN rallies r ON e.rally_id = r.id
JOIN persons p ON e.driver_id = p.id
WHERE r.name LIKE '%Stereas Elladas%' AND r.name LIKE '%2025%'
AND (e.class = 'C2' OR e.class LIKE '%C2%')
ORDER BY e.number;

-- Method 1: Update specific Rally3 cars by entry number
-- Replace the numbers below with the actual Rally3 entry numbers

-- Example: If entries 5, 12, and 23 are Rally3 cars
UPDATE entries 
SET class = CASE 
    WHEN class LIKE '%Rally3%' THEN class  -- Already has Rally3
    WHEN class IS NULL THEN 'C2, Rally3'
    ELSE class || ', Rally3'  -- Add Rally3 to existing classes
END
WHERE rally_id = (SELECT id FROM rallies WHERE name LIKE '%Stereas Elladas 2025%')
AND number IN ('5', '12', '23');  -- Replace with actual Rally3 entry numbers

-- Method 2: Update Rally3 cars by car model (if identifiable)
-- UPDATE entries 
-- SET class = CASE 
--     WHEN class LIKE '%Rally3%' THEN class
--     WHEN class IS NULL THEN 'C2, Rally3'
--     ELSE class || ', Rally3'
-- END
-- WHERE rally_id = (SELECT id FROM rallies WHERE name LIKE '%Stereas Elladas 2025%')
-- AND (
--     car LIKE '%Rally3%' 
--     OR car LIKE '%Clio Rally3%'
--     OR car LIKE '%208 Rally3%'
-- );

-- Method 3: Update Rally3 cars by driver name (if you know specific drivers)
-- UPDATE entries 
-- SET class = CASE 
--     WHEN class LIKE '%Rally3%' THEN class
--     WHEN class IS NULL THEN 'C2, Rally3'
--     ELSE class || ', Rally3'
-- END
-- WHERE rally_id = (SELECT id FROM rallies WHERE name LIKE '%Stereas Elladas 2025%')
-- AND driver_id IN (
--     SELECT id FROM persons 
--     WHERE last_name IN ('DriverName1', 'DriverName2')  -- Replace with actual Rally3 driver names
-- );

-- Verify the updates
SELECT 'C2 entries after Rally3 classification:' as info;
SELECT 
    e.number,
    CONCAT(p.first_name, ' ', p.last_name) as driver,
    e.car,
    e.class as updated_class,
    CASE 
        WHEN e.class LIKE '%Rally3%' THEN 'Greece + Rally3 Championships'
        WHEN e.class LIKE '%C2%' THEN 'Greece Championship only'
        ELSE 'Other'
    END as championship_eligibility
FROM entries e
JOIN rallies r ON e.rally_id = r.id
JOIN persons p ON e.driver_id = p.id
WHERE r.name LIKE '%Stereas Elladas%' AND r.name LIKE '%2025%'
AND (e.class = 'C2' OR e.class LIKE '%C2%')
ORDER BY e.number;

-- Test championship filtering
SELECT 'Championship entry counts after Rally3 update:' as info;
SELECT 
    'Greece Championship' as championship,
    COUNT(*) as entries
FROM entries e
JOIN rallies r ON e.rally_id = r.id
WHERE r.name LIKE '%Stereas Elladas%' AND r.name LIKE '%2025%'
AND (
    e.class LIKE '%C1%' OR e.class LIKE '%C2%' OR e.class LIKE '%C3%' 
    OR e.class LIKE '%C4%' OR e.class LIKE '%C5%' OR e.class LIKE '%C6%'
)

UNION ALL

SELECT 
    'Rally3 Championship' as championship,
    COUNT(*) as entries
FROM entries e
JOIN rallies r ON e.rally_id = r.id
WHERE r.name LIKE '%Stereas Elladas%' AND r.name LIKE '%2025%'
AND e.class LIKE '%Rally3%'

UNION ALL

SELECT 
    'Historic Championship' as championship,
    COUNT(*) as entries
FROM entries e
JOIN rallies r ON e.rally_id = r.id
WHERE r.name LIKE '%Stereas Elladas%' AND r.name LIKE '%2025%'
AND (e.class LIKE '%2%' OR e.class LIKE '%3%' OR e.class LIKE '%4%')
AND e.class NOT LIKE '%C2%' AND e.class NOT LIKE '%C3%' AND e.class NOT LIKE '%C4%';  -- Exclude C-classes
