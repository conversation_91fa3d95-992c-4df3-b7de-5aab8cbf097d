import React, { useState, useEffect } from 'react';
import { ChampionshipRally } from '../../types';

interface ChampionshipSelectorProps {
  rallyId: string;
  selectedChampionshipId?: string;
  onChampionshipChange: (championshipId: string | null) => void;
  className?: string;
}

const ChampionshipSelector: React.FC<ChampionshipSelectorProps> = ({
  rallyId,
  selectedChampionshipId,
  onChampionshipChange,
  className = ''
}) => {
  const [championships, setChampionships] = useState<ChampionshipRally[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchChampionships = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/championship-results/rallies/${rallyId}/championships`);
        
        if (!response.ok) {
          throw new Error('Failed to fetch championships');
        }
        
        const data = await response.json();
        setChampionships(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch championships');
        console.error('Error fetching championships:', err);
      } finally {
        setLoading(false);
      }
    };

    if (rallyId) {
      fetchChampionships();
    }
  }, [rallyId]);

  const handleChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const value = event.target.value;
    onChampionshipChange(value === '' ? null : value);
  };

  if (loading) {
    return (
      <div className={`animate-pulse ${className}`}>
        <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`text-red-600 dark:text-red-400 text-sm ${className}`}>
        Error loading championships: {error}
      </div>
    );
  }

  if (championships.length === 0) {
    return null; // Don't show selector if no championships
  }

  return (
    <div className={className}>
      <label htmlFor="championship-select" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
        Championship View
      </label>
      <select
        id="championship-select"
        value={selectedChampionshipId || ''}
        onChange={handleChange}
        className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
      >
        <option value="">All Results</option>
        {championships.map((championship) => (
          <option key={championship.championship_id} value={championship.championship_id}>
            {championship.championship_name} ({championship.year})
            {championship.coefficient !== 1.0 && ` - ${championship.coefficient}x points`}
          </option>
        ))}
      </select>
      
      {championships.length > 1 && (
        <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
          This rally is part of {championships.length} championships
        </p>
      )}
    </div>
  );
};

export default ChampionshipSelector;
