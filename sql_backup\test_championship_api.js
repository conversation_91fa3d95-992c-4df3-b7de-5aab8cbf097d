import fetch from 'node-fetch';

async function testChampionshipAPI() {
  try {
    console.log('=== Testing Championship API Endpoints ===');
    
    const baseUrl = 'http://localhost:5174/api/championship-results';
    
    // Test 1: Get all championships
    console.log('\n1. Testing GET /championships');
    const championshipsResponse = await fetch(`${baseUrl}/championships`);
    const championships = await championshipsResponse.json();
    console.log(`Found ${championships.length} championships:`);
    championships.forEach(champ => {
      console.log(`  - ${champ.name} (${champ.year}) - ${champ.rally_count} rallies`);
    });
    
    // Test 2: Get championships for Rally Stereas Elladas 2025
    console.log('\n2. Testing GET /rallies/:rallyId/championships');
    
    // First get the rally ID
    const ralliesResponse = await fetch('http://localhost:5174/api/rallies');
    const rallies = await ralliesResponse.json();
    const strereasRally = rallies.find(r => r.name.includes('Stereas Elladas 2025'));
    
    if (!strereasRally) {
      console.log('Rally Stereas Elladas 2025 not found');
      return;
    }
    
    console.log(`Rally ID: ${strereasRally.id}`);
    
    const rallyChampionshipsResponse = await fetch(`${baseUrl}/rallies/${strereasRally.id}/championships`);
    const rallyChampionships = await rallyChampionshipsResponse.json();
    console.log(`Found ${rallyChampionships.length} championships for this rally:`);
    rallyChampionships.forEach(champ => {
      console.log(`  - ${champ.championship_name} (coefficient: ${champ.coefficient})`);
    });
    
    // Test 3: Get championship results for Greece championship
    if (rallyChampionships.length > 0) {
      const greeceChamionship = rallyChampionships.find(c => c.championship_name === 'Greece');
      if (greeceChamionship) {
        console.log('\n3. Testing GET /rallies/:rallyId/results?championshipId=:id');
        const resultsResponse = await fetch(`${baseUrl}/rallies/${strereasRally.id}/results?championshipId=${greeceChamionship.championship_id}`);
        const results = await resultsResponse.json();
        console.log(`Found ${results.length} results for Greece championship:`);
        results.slice(0, 5).forEach(result => {
          console.log(`  ${result.championship_position}. ${result.driver} (${result.points} pts) - ${result.class}`);
        });
      }
    }
    
    // Test 4: Get championship standings
    if (rallyChampionships.length > 0) {
      const greeceChamionship = rallyChampionships.find(c => c.championship_name === 'Greece');
      if (greeceChamionship) {
        console.log('\n4. Testing GET /championships/:championshipId/standings');
        const standingsResponse = await fetch(`${baseUrl}/championships/${greeceChamionship.championship_id}/standings`);
        const standings = await standingsResponse.json();
        console.log(`Found ${standings.length} drivers in Greece championship standings:`);
        standings.slice(0, 5).forEach(driver => {
          console.log(`  ${driver.position}. ${driver.driver} (${driver.total_points} pts, ${driver.rallies_completed} rallies)`);
        });
      }
    }
    
    // Test 5: Get championship calendar
    if (rallyChampionships.length > 0) {
      const greeceChamionship = rallyChampionships.find(c => c.championship_name === 'Greece');
      if (greeceChamionship) {
        console.log('\n5. Testing GET /championships/:championshipId/calendar');
        const calendarResponse = await fetch(`${baseUrl}/championships/${greeceChamionship.championship_id}/calendar`);
        const calendar = await calendarResponse.json();
        console.log(`Found ${calendar.length} rallies in Greece championship calendar:`);
        calendar.forEach(rally => {
          console.log(`  - ${rally.name} (${rally.championship_entry_count} championship entries)`);
        });
      }
    }
    
    console.log('\n✅ API testing complete!');
    process.exit(0);
  } catch (error) {
    console.error('Error testing championship API:', error);
    process.exit(1);
  }
}

testChampionshipAPI();
