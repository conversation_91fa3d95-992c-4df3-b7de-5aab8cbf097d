-- Comprehensive check of current database structure vs StageTimeSchema.sql
-- This script helps identify what's missing, extra, or different

-- ============================================================================
-- STEP 1: Check all tables in current database
-- ============================================================================
SELECT 'CURRENT DATABASE TABLES' as section;
SELECT
  schemaname,
  tablename,
  tableowner,
  hasindexes,
  hasrules,
  hastriggers
FROM pg_tables
WHERE schemaname = 'public'
ORDER BY tablename;

-- ============================================================================
-- STEP 2: Check all ENUM types
-- ============================================================================
SELECT 'CURRENT ENUM TYPES' as section;
SELECT
  t.typname as enum_name,
  string_agg(e.enumlabel, ', ' ORDER BY e.enumsortorder) as enum_values
FROM pg_type t
JOIN pg_enum e ON t.oid = e.enumtypid
WHERE t.typname IN ('rally_status', 'stage_status', 'entry_status', 'championship_type')
GROUP BY t.typname
ORDER BY t.typname;

-- ============================================================================
-- STEP 3: Check table structures for key tables
-- ============================================================================
SELECT 'PERSONS TABLE STRUCTURE' as section;
SELECT
  column_name,
  data_type,
  is_nullable,
  column_default,
  character_maximum_length
FROM information_schema.columns
WHERE table_name = 'persons'
ORDER BY ordinal_position;

SELECT 'RALLIES TABLE STRUCTURE' as section;
SELECT
  column_name,
  data_type,
  is_nullable,
  column_default
FROM information_schema.columns
WHERE table_name = 'rallies'
ORDER BY ordinal_position;

SELECT 'ENTRIES TABLE STRUCTURE' as section;
SELECT
  column_name,
  data_type,
  is_nullable,
  column_default
FROM information_schema.columns
WHERE table_name = 'entries'
ORDER BY ordinal_position;

SELECT 'RESULTS TABLE STRUCTURE' as section;
SELECT
  column_name,
  data_type,
  is_nullable,
  column_default
FROM information_schema.columns
WHERE table_name = 'results'
ORDER BY ordinal_position;

SELECT 'CHAMPIONSHIPS TABLE STRUCTURE' as section;
SELECT
  column_name,
  data_type,
  is_nullable,
  column_default
FROM information_schema.columns
WHERE table_name = 'championships'
ORDER BY ordinal_position;

-- ============================================================================
-- STEP 4: Check for missing tables from schema
-- ============================================================================
SELECT 'CHECKING FOR MISSING TABLES' as section;
SELECT
  'Missing table: ' || table_name as status
FROM (
  VALUES
    ('users'), ('persons'), ('drivers'), ('codrivers'), ('championships'),
    ('rallies'), ('stages'), ('teams'), ('entries'), ('results'),
    ('penalties'), ('championship_events'), ('power_stage_points'),
    ('news'), ('splits')
) AS schema_tables(table_name)
WHERE table_name NOT IN (
  SELECT tablename FROM pg_tables WHERE schemaname = 'public'
);

-- ============================================================================
-- STEP 5: Check for extra tables not in schema
-- ============================================================================
SELECT 'CHECKING FOR EXTRA TABLES' as section;
SELECT
  'Extra table: ' || tablename as status
FROM pg_tables
WHERE schemaname = 'public'
  AND tablename NOT IN (
    'users', 'persons', 'drivers', 'codrivers', 'championships',
    'rallies', 'stages', 'teams', 'entries', 'results',
    'penalties', 'championship_events', 'power_stage_points',
    'news', 'splits'
  )
  AND tablename NOT LIKE 'backup_%'  -- Ignore backup tables
ORDER BY tablename;

-- ============================================================================
-- STEP 6: Check views
-- ============================================================================
SELECT 'CURRENT VIEWS' as section;
SELECT
  schemaname,
  viewname,
  viewowner
FROM pg_views
WHERE schemaname = 'public'
ORDER BY viewname;

-- ============================================================================
-- STEP 7: Check indexes
-- ============================================================================
SELECT 'CURRENT INDEXES' as section;
SELECT
  schemaname,
  tablename,
  indexname,
  indexdef
FROM pg_indexes
WHERE schemaname = 'public'
  AND indexname NOT LIKE '%_pkey'  -- Exclude primary key indexes
ORDER BY tablename, indexname;

-- ============================================================================
-- STEP 8: Check foreign key constraints
-- ============================================================================
SELECT 'FOREIGN KEY CONSTRAINTS' as section;
SELECT
  tc.table_name,
  tc.constraint_name,
  tc.constraint_type,
  kcu.column_name,
  ccu.table_name AS foreign_table_name,
  ccu.column_name AS foreign_column_name
FROM information_schema.table_constraints AS tc
JOIN information_schema.key_column_usage AS kcu
  ON tc.constraint_name = kcu.constraint_name
  AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
  ON ccu.constraint_name = tc.constraint_name
  AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
  AND tc.table_schema = 'public'
ORDER BY tc.table_name, tc.constraint_name;
