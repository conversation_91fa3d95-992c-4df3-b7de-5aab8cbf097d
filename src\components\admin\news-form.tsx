import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

interface NewsFormProps {
  initialData?: any;
  onSubmit?: (formData: any) => Promise<void>;
  onDelete?: () => Promise<void>;
}

const NewsForm: React.FC<NewsFormProps> = ({ 
  initialData, 
  onSubmit,
  onDelete
}) => {
  const [form, setForm] = useState({
    title: '',
    content: '',
    excerpt: '',
    image_url: ''
  });
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const navigate = useNavigate();

  useEffect(() => {
    if (initialData) {
      setForm({
        title: initialData.title || '',
        content: initialData.content || '',
        excerpt: initialData.excerpt || '',
        image_url: initialData.image_url || ''
      });
    }
  }, [initialData]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const validateForm = () => {
    const errors: Record<string, string> = {};

    if (!form.title.trim()) errors.title = 'Title is required';
    if (!form.content.trim()) errors.content = 'Content is required';
    if (!form.excerpt.trim()) errors.excerpt = 'Excerpt is required';
    if (!form.image_url.trim()) errors.image_url = 'Image URL is required';

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    setLoading(true);
    setError('');
    
    try {
      if (onSubmit) {
        await onSubmit(form);
      } else {
        // Default submission logic if no onSubmit provided
        const url = initialData ? `/api/news/${initialData.id}` : '/api/news';
        const method = initialData ? 'PUT' : 'POST';
        
        // Log the form data being sent
        console.log('Submitting news form data:', form);
        
        const response = await fetch(url, {
          method,
          headers: { 'Content-Type': 'application/json' },
          credentials: 'include',
          body: JSON.stringify(form)
        });

        // Log the response
        console.log('Response status:', response.status);
        const responseData = await response.json();
        console.log('Response data:', responseData);

        if (!response.ok) {
          throw new Error(responseData.message || responseData.error || `Failed to ${initialData ? 'update' : 'add'} news`);
        }

        navigate('/admin/news');
      }
    } catch (err: any) {
      setError(err.message);
      window.scrollTo(0, 0); // Scroll to top to show error
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!initialData || !onDelete) return;
    
    if (!window.confirm('Are you sure you want to delete this news item?')) {
      return;
    }

    try {
      await onDelete();
    } catch (err: any) {
      setError(err.message);
    }
  };

  const handleCancel = () => {
    navigate('/admin/news');
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      {error && (
        <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          {error}
        </div>
      )}
      
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Title
          </label>
          <input
            name="title"
            value={form.title}
            onChange={handleChange}
            required
            placeholder="News Title"
            className={`w-full px-3 py-2 rounded border ${
              validationErrors.title ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
            } dark:bg-gray-700 dark:text-white`}
          />
          {validationErrors.title && (
            <p className="mt-1 text-sm text-red-500">{validationErrors.title}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Excerpt
          </label>
          <textarea
            name="excerpt"
            value={form.excerpt}
            onChange={handleChange}
            required
            rows={2}
            placeholder="Brief summary of the news"
            className={`w-full px-3 py-2 rounded border ${
              validationErrors.excerpt ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
            } dark:bg-gray-700 dark:text-white`}
          />
          {validationErrors.excerpt && (
            <p className="mt-1 text-sm text-red-500">{validationErrors.excerpt}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Content
          </label>
          <textarea
            name="content"
            value={form.content}
            onChange={handleChange}
            required
            rows={8}
            placeholder="Full news content"
            className={`w-full px-3 py-2 rounded border ${
              validationErrors.content ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
            } dark:bg-gray-700 dark:text-white`}
          />
          {validationErrors.content && (
            <p className="mt-1 text-sm text-red-500">{validationErrors.content}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Image URL
          </label>
          <input
            name="image_url"
            value={form.image_url}
            onChange={handleChange}
            required
            placeholder="URL to the news image"
            className={`w-full px-3 py-2 rounded border ${
              validationErrors.image_url ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
            } dark:bg-gray-700 dark:text-white`}
          />
          {validationErrors.image_url && (
            <p className="mt-1 text-sm text-red-500">{validationErrors.image_url}</p>
          )}
          {form.image_url && (
            <div className="mt-2">
              <p className="text-sm text-gray-500 dark:text-gray-400 mb-1">Image Preview:</p>
              <img 
                src={form.image_url} 
                alt="Preview" 
                className="max-h-40 rounded border border-gray-300 dark:border-gray-600"
                onError={(e) => {
                  (e.target as HTMLImageElement).src = 'https://via.placeholder.com/300x200?text=Invalid+Image+URL';
                }}
              />
            </div>
          )}
        </div>

        <div className="flex justify-between pt-4">
          <div>
            {initialData && onDelete && (
              <button
                type="button"
                onClick={handleDelete}
                className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
              >
                Delete
              </button>
            )}
          </div>
          <div className="flex space-x-3">
            <button
              type="button"
              onClick={handleCancel}
              className="px-4 py-2 border border-gray-300 rounded text-gray-700 bg-white hover:bg-gray-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:hover:bg-gray-600"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors disabled:opacity-50"
            >
              {loading ? (initialData ? 'Saving...' : 'Adding...') : (initialData ? 'Save Changes' : 'Add News')}
            </button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default NewsForm;
