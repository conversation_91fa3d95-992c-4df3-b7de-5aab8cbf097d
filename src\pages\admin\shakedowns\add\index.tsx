import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Clock } from 'lucide-react';
import { useAuth } from '../../../../context/AuthContext';
import ShakedownForm from '../../../../components/admin/shakedown-form';

const AddShakedownPage: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();

  const handleSubmit = async (formData: any) => {
    try {
      const response = await fetch('/api/shakedowns', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify(formData)
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to create shakedown');
      }
      
      navigate('/admin/shakedowns');
    } catch (error) {
      console.error('Error creating shakedown:', error);
      throw error;
    }
  };

  return (
    <div className="min-h-screen bg-gray-100 dark:bg-gray-900">
      <div className="bg-white dark:bg-gray-800 shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <Clock className="h-8 w-8 text-red-600" />
              <span className="ml-2 text-xl font-bold text-gray-900 dark:text-white">
                Add Shakedown
              </span>
            </div>
            <div className="flex items-center">
              <span className="text-gray-600 dark:text-gray-300 mr-4">{user?.email}</span>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Add New Shakedown</h1>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
            Create a new shakedown for a rally.
          </p>
        </div>

        <ShakedownForm onSubmit={handleSubmit} />
      </div>
    </div>
  );
};

export default AddShakedownPage;
