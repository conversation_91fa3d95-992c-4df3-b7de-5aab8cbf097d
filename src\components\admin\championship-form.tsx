import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { ChampionshipType } from '../../types';

interface ChampionshipFormProps {
  initialData?: any;
  onSubmit?: (formData: any) => Promise<void>;
  onDelete?: () => Promise<void>;
}

const ChampionshipForm: React.FC<ChampionshipFormProps> = ({ 
  initialData, 
  onSubmit,
  onDelete
}) => {
  const [form, setForm] = useState({
    name: '',
    year: new Date().getFullYear(),
    type: ChampionshipType.International,
    description: ''
  });
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const navigate = useNavigate();

  useEffect(() => {
    if (initialData) {
      setForm({
        name: initialData.name || '',
        year: initialData.year || new Date().getFullYear(),
        type: initialData.type || ChampionshipType.International,
        description: initialData.description || ''
      });
    }
  }, [initialData]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    
    // Convert year to number if it's the year field
    if (name === 'year') {
      setForm({ ...form, [name]: parseInt(value) });
    } else {
      setForm({ ...form, [name]: value });
    }
  };

  const validateForm = () => {
    const errors: Record<string, string> = {};

    if (!form.name.trim()) errors.name = 'Championship name is required';
    if (!form.year) errors.year = 'Year is required';
    if (form.year < 1950 || form.year > 2100) errors.year = 'Year must be between 1950 and 2100';
    if (!form.type) errors.type = 'Championship type is required';

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    setLoading(true);
    setError('');
    
    try {
      if (onSubmit) {
        await onSubmit(form);
      } else {
        // Default submission logic if no onSubmit provided
        const url = initialData ? `/api/championships/${initialData.id}` : '/api/championships';
        const method = initialData ? 'PUT' : 'POST';
        
        // Log the form data being sent
        console.log('Submitting championship form data:', form);
        
        const response = await fetch(url, {
          method,
          headers: { 'Content-Type': 'application/json' },
          credentials: 'include',
          body: JSON.stringify(form)
        });

        // Log the response
        console.log('Response status:', response.status);
        const responseData = await response.json();
        console.log('Response data:', responseData);

        if (!response.ok) {
          throw new Error(responseData.message || responseData.error || `Failed to ${initialData ? 'update' : 'add'} championship`);
        }

        navigate('/admin/championships');
      }
    } catch (err: any) {
      setError(err.message);
      window.scrollTo(0, 0); // Scroll to top to show error
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!initialData || !onDelete) return;
    
    if (!window.confirm('Are you sure you want to delete this championship?')) {
      return;
    }

    try {
      await onDelete();
    } catch (err: any) {
      setError(err.message);
    }
  };

  const handleCancel = () => {
    navigate('/admin/championships');
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      {error && (
        <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          {error}
        </div>
      )}
      
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Championship Name
          </label>
          <input
            name="name"
            value={form.name}
            onChange={handleChange}
            required
            placeholder="Championship Name"
            className={`w-full px-3 py-2 rounded border ${
              validationErrors.name ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
            } dark:bg-gray-700 dark:text-white`}
          />
          {validationErrors.name && (
            <p className="mt-1 text-sm text-red-500">{validationErrors.name}</p>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Year
            </label>
            <input
              name="year"
              value={form.year}
              onChange={handleChange}
              required
              type="number"
              min="1950"
              max="2100"
              placeholder="Year"
              className={`w-full px-3 py-2 rounded border ${
                validationErrors.year ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
              } dark:bg-gray-700 dark:text-white`}
            />
            {validationErrors.year && (
              <p className="mt-1 text-sm text-red-500">{validationErrors.year}</p>
            )}
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Type
            </label>
            <select
              name="type"
              value={form.type}
              onChange={handleChange}
              required
              className={`w-full px-3 py-2 rounded border ${
                validationErrors.type ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
              } dark:bg-gray-700 dark:text-white`}
            >
              <option value={ChampionshipType.International}>International</option>
              <option value={ChampionshipType.Regional}>Regional</option>
              <option value={ChampionshipType.National}>National</option>
            </select>
            {validationErrors.type && (
              <p className="mt-1 text-sm text-red-500">{validationErrors.type}</p>
            )}
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Description
          </label>
          <textarea
            name="description"
            value={form.description}
            onChange={handleChange}
            rows={4}
            placeholder="Championship description"
            className="w-full px-3 py-2 rounded border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
          />
        </div>

        <div className="flex justify-between pt-4">
          <div>
            {initialData && onDelete && (
              <button
                type="button"
                onClick={handleDelete}
                className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
              >
                Delete
              </button>
            )}
          </div>
          <div className="flex space-x-3">
            <button
              type="button"
              onClick={handleCancel}
              className="px-4 py-2 border border-gray-300 rounded text-gray-700 bg-white hover:bg-gray-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:hover:bg-gray-600"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors disabled:opacity-50"
            >
              {loading ? (initialData ? 'Saving...' : 'Adding...') : (initialData ? 'Save Changes' : 'Add Championship')}
            </button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default ChampionshipForm;
