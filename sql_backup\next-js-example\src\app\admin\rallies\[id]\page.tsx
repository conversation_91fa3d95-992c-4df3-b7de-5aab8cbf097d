import { redirect, notFound } from "next/navigation";
import { createClient } from "@/lib/supabase/server";
import RallyForm from "@/components/admin/rally-form";
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import StagesManager from "@/components/admin/stages-manager";
import EntriesManager from "@/components/admin/entries-manager";

export default async function EditRallyPage({ params }: { params: { id: string } }) {
  const supabase = await createClient();

  const {
    data: { session },
  } = await supabase.auth.getSession();

  if (!session) {
    redirect("/login");
  }

  // Fetch rally details
  const { data: rally, error } = await supabase
    .from("rallies")
    .select("*")
    .eq("id", params.id)
    .single();

  if (error || !rally) {
    notFound();
  }

  // Fetch championships for dropdown
  const { data: championships } = await supabase
    .from("championships")
    .select("id, name, year");

  return (
    <div className="container py-8">
      <h1 className="text-3xl font-bold mb-8">Edit Rally: {rally.name}</h1>

      <Tabs defaultValue="details" className="w-full">
        <TabsList className="grid w-full md:w-auto md:inline-grid grid-cols-3 md:grid-cols-3 mb-8">
          <TabsTrigger value="details">Rally Details</TabsTrigger>
          <TabsTrigger value="stages">Stages</TabsTrigger>
          <TabsTrigger value="entries">Entries</TabsTrigger>
        </TabsList>

        <TabsContent value="details">
          <RallyForm championships={championships || []} initialData={rally} />
        </TabsContent>

        <TabsContent value="stages">
          <StagesManager rallyId={params.id} />
        </TabsContent>

        <TabsContent value="entries">
          <EntriesManager rallyId={params.id} />
        </TabsContent>
      </Tabs>
    </div>
  );
}
