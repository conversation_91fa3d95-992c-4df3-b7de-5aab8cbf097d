-- Migration: Convert space-separated classes to comma-separated
-- This will help with easier parsing and splitting of multiple classes

-- First, let's see what we're working with
SELECT id, number, class, 
       'Original: ' || class as original_format
FROM entries 
WHERE class IS NOT NULL AND class != ''
LIMIT 10;

-- Create a backup of the original data
CREATE TABLE IF NOT EXISTS entries_class_backup AS
SELECT id, class as original_class, NOW() as backup_date
FROM entries;

-- Function to convert space-separated classes to comma-separated
-- This handles the complex cases like "C6 F2 Under 30"
-- You'll need to manually review and adjust this based on your specific class names

-- For now, let's do a simple conversion that assumes most classes are single words
-- and handles some common multi-word cases

UPDATE entries 
SET class = CASE 
  -- Handle common multi-word classes first
  WHEN class LIKE '%Under 30%' THEN REPLACE(class, 'Under 30', 'Under_30')
  WHEN class LIKE '%Over 30%' THEN REPLACE(class, 'Over 30', 'Over_30')
  WHEN class LIKE '%Under 25%' THEN REPLACE(class, 'Under 25', 'Under_25')
  WHEN class LIKE '%Over 25%' THEN REPLACE(class, 'Over 25', 'Over_25')
  WHEN class LIKE '%Under 35%' THEN REPLACE(class, 'Under 35', 'Under_35')
  WHEN class LIKE '%Over 35%' THEN REPLACE(class, 'Over 35', 'Over_35')
  WHEN class LIKE '%Under 40%' THEN REPLACE(class, 'Under 40', 'Under_40')
  WHEN class LIKE '%Over 40%' THEN REPLACE(class, 'Over 40', 'Over_40')
  WHEN class LIKE '%Ladies Class%' THEN REPLACE(class, 'Ladies Class', 'Ladies_Class')
  WHEN class LIKE '%Women Class%' THEN REPLACE(class, 'Women Class', 'Women_Class')
  WHEN class LIKE '%Junior Class%' THEN REPLACE(class, 'Junior Class', 'Junior_Class')
  WHEN class LIKE '%Senior Class%' THEN REPLACE(class, 'Senior Class', 'Senior_Class')
  ELSE class
END
WHERE class IS NOT NULL AND class != '';

-- Now convert spaces to commas (after protecting multi-word classes with underscores)
UPDATE entries 
SET class = REPLACE(TRIM(REGEXP_REPLACE(class, '\s+', ', ', 'g')), '_', ' ')
WHERE class IS NOT NULL AND class != '';

-- Clean up any double commas or trailing commas
UPDATE entries 
SET class = TRIM(REGEXP_REPLACE(REGEXP_REPLACE(class, ',\s*,+', ', ', 'g'), ',\s*$|^\s*,', '', 'g'))
WHERE class IS NOT NULL AND class != '';

-- Show the results
SELECT id, number, 
       eb.original_class as before,
       e.class as after,
       'Converted: ' || e.class as new_format
FROM entries e
JOIN entries_class_backup eb ON e.id = eb.id
WHERE e.class IS NOT NULL AND e.class != ''
LIMIT 20;

-- Verify the conversion worked correctly
SELECT 
  'Total entries with classes' as description,
  COUNT(*) as count
FROM entries 
WHERE class IS NOT NULL AND class != ''
UNION ALL
SELECT 
  'Entries with comma-separated classes' as description,
  COUNT(*) as count
FROM entries 
WHERE class LIKE '%,%'
UNION ALL
SELECT 
  'Entries with single class (no comma)' as description,
  COUNT(*) as count
FROM entries 
WHERE class IS NOT NULL AND class != '' AND class NOT LIKE '%,%';

-- If you need to rollback, you can use:
-- UPDATE entries 
-- SET class = eb.original_class
-- FROM entries_class_backup eb
-- WHERE entries.id = eb.id;
