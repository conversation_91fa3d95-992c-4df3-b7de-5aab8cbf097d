import express from 'express';
import pool from '../config/db.js';
import { verifyToken, verifyAdmin } from '../middleware/auth.js';

const router = express.Router();

// Create split
router.post('/', verifyToken, verifyAdmin, async (req, res) => {
  try {
    const { stage_id, entry_id, split_number, time } = req.body;
    const result = await pool.query(
      `INSERT INTO splits (stage_id, entry_id, split_number, time) VALUES ($1, $2, $3, $4) RETURNING *`,
      [stage_id, entry_id, split_number, time]
    );
    res.status(201).json(result.rows[0]);
  } catch (error) {
    res.status(500).json({ message: 'Failed to add split' });
  }
});

// Get all splits (with entry, stage, and rally info) - PUBLIC ROUTE
router.get('/', async (req, res) => {
  try {
    const result = await pool.query(`
      SELECT s.*, e.number AS entry_number, st.name AS stage_name, r.name AS rally_name
      FROM splits s
      JOIN entries e ON s.entry_id = e.id
      JOIN stages st ON s.stage_id = st.id
      JOIN rallies r ON st.rally_id = r.id
      ORDER BY s.stage_id, s.split_number ASC
    `);
    res.json(result.rows);
  } catch (error) {
    res.status(500).json({ message: 'Failed to fetch splits' });
  }
});

// Get single split (with entry, stage, and rally info) - PUBLIC ROUTE
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const result = await pool.query(`
      SELECT s.*, e.number AS entry_number, st.name AS stage_name, r.name AS rally_name
      FROM splits s
      JOIN entries e ON s.entry_id = e.id
      JOIN stages st ON s.stage_id = st.id
      JOIN rallies r ON st.rally_id = r.id
      WHERE s.id = $1
    `, [id]);
    if (result.rows.length === 0) return res.status(404).json({ message: 'Split not found' });
    res.json(result.rows[0]);
  } catch (error) {
    res.status(500).json({ message: 'Failed to fetch split' });
  }
});

// Update split
router.put('/:id', verifyToken, verifyAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { stage_id, entry_id, split_number, time } = req.body;
    const result = await pool.query(
      `UPDATE splits SET stage_id=$1, entry_id=$2, split_number=$3, time=$4 WHERE id=$5 RETURNING *`,
      [stage_id, entry_id, split_number, time, id]
    );
    if (result.rows.length === 0) return res.status(404).json({ message: 'Split not found' });
    res.json(result.rows[0]);
  } catch (error) {
    res.status(500).json({ message: 'Failed to update split' });
  }
});

// Delete split
router.delete('/:id', verifyToken, verifyAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const result = await pool.query('DELETE FROM splits WHERE id = $1 RETURNING *', [id]);
    if (result.rows.length === 0) return res.status(404).json({ message: 'Split not found' });
    res.json({ message: 'Split deleted', split: result.rows[0] });
  } catch (error) {
    res.status(500).json({ message: 'Failed to delete split' });
  }
});

export default router;
