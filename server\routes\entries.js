import express from 'express';
import pool from '../config/db.js';
import { verifyToken, verifyAdmin } from '../middleware/auth.js';
import multer from 'multer';
import { parse } from 'csv-parse/sync';

const router = express.Router();
const upload = multer({ dest: 'uploads/' });

// Create entry
router.post('/', verifyToken, verifyAdmin, async (req, res) => {
  // Use a client with a transaction to ensure all operations succeed or fail together
  const client = await pool.connect();

  try {
    // Start transaction
    await client.query('BEGIN');

    const { rally_id, driver_id, codriver_id, team_id, car, number, class: entryClass, status } = req.body;

    console.log('Creating entry with data:', req.body);

    // Validate input data
    if (!rally_id || !driver_id || !codriver_id || !car || number === undefined || !entryClass || !status) {
      throw new Error('Missing required fields');
    }

    // Validate status is a valid entry_status enum value
    const validStatuses = ['entered', 'running', 'finished', 'retired', 'dns', 'dnf', 'dsq'];
    if (!validStatuses.includes(status)) {
      throw new Error(`Invalid status value: "${status}". Valid values are: ${validStatuses.join(', ')}`);
    }

    // Check if the driver exists in the drivers table
    const driverCheck = await client.query('SELECT * FROM drivers WHERE id = $1', [driver_id]);
    if (driverCheck.rows.length === 0) {
      // Check if the person exists
      const personCheck = await client.query('SELECT * FROM persons WHERE id = $1', [driver_id]);
      if (personCheck.rows.length === 0) {
        await client.query('ROLLBACK');
        return res.status(404).json({ message: 'Driver person not found' });
      }

      // Add the person as a driver
      await client.query('INSERT INTO drivers (id) VALUES ($1)', [driver_id]);
      console.log(`Added person ${driver_id} as a driver automatically`);
    }

    // Check if the codriver exists in the codrivers table
    const codriverCheck = await client.query('SELECT * FROM codrivers WHERE id = $1', [codriver_id]);
    if (codriverCheck.rows.length === 0) {
      // Check if the person exists
      const personCheck = await client.query('SELECT * FROM persons WHERE id = $1', [codriver_id]);
      if (personCheck.rows.length === 0) {
        await client.query('ROLLBACK');
        return res.status(404).json({ message: 'Codriver person not found' });
      }

      // Add the person as a codriver
      await client.query('INSERT INTO codrivers (id) VALUES ($1)', [codriver_id]);
      console.log(`Added person ${codriver_id} as a codriver automatically`);
    }

    // Create the entry
    const result = await client.query(
      `INSERT INTO entries (rally_id, driver_id, codriver_id, team_id, car, number, class, status) VALUES ($1, $2, $3, $4, $5, $6, $7, $8) RETURNING *`,
      [rally_id, driver_id, codriver_id, team_id, car, number, entryClass, status]
    );

    // Fetch with joined details for response
    const entryId = result.rows[0].id;
    const details = await client.query(`
      SELECT e.*,
        pd.first_name AS driver_first_name, pd.last_name AS driver_last_name, pd.nationality AS driver_nationality,
        pcd.first_name AS codriver_first_name, pcd.last_name AS codriver_last_name, pcd.nationality AS codriver_nationality,
        t.name AS team_name
      FROM entries e
      JOIN persons pd ON pd.id = e.driver_id
      JOIN persons pcd ON pcd.id = e.codriver_id
      LEFT JOIN teams t ON t.id = e.team_id
      WHERE e.id = $1
    `, [entryId]);

    // Commit the transaction
    await client.query('COMMIT');

    res.status(201).json(details.rows[0]);
  } catch (error) {
    // Rollback the transaction in case of error
    await client.query('ROLLBACK');

    console.error('Error creating entry:', error);
    console.error('Error details:', req.body);
    res.status(500).json({ message: 'Failed to add entry', error: error.message });
  } finally {
    // Release the client back to the pool
    client.release();
  }
});

// Get all entries (with driver/codriver/team details) - PUBLIC ROUTE
router.get('/', async (req, res) => {
  try {
    const { rally_id } = req.query;

    // If rally_id is provided, filter entries by rally_id
    let query = `
      SELECT e.*,
        pd.first_name AS driver_first_name, pd.last_name AS driver_last_name, pd.nationality AS driver_nationality,
        pcd.first_name AS codriver_first_name, pcd.last_name AS codriver_last_name, pcd.nationality AS codriver_nationality,
        t.name AS team_name,
        oc.position,
        oc.total_time,
        oc.time_diff
      FROM entries e
      JOIN persons pd ON pd.id = e.driver_id
      JOIN persons pcd ON pcd.id = e.codriver_id
      LEFT JOIN teams t ON t.id = e.team_id
      LEFT JOIN overall_classification oc ON oc.entry_id = e.id
    `;

    const queryParams = [];

    if (rally_id) {
      query += ` WHERE e.rally_id = $1`;
      queryParams.push(rally_id);
    }

    query += ` ORDER BY e.number ASC`;

    const result = await pool.query(query, queryParams);
    res.json(result.rows);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Failed to fetch entries' });
  }
});

// Debug endpoint to check if a person exists - ADMIN ROUTE
router.get('/debug/check-person', verifyToken, verifyAdmin, async (req, res) => {
  try {
    const { first_name, last_name, flexible } = req.query;

    if (!first_name || !last_name) {
      return res.status(400).json({ message: 'Missing required parameters: first_name and last_name' });
    }

    // Try exact match first
    let result = await pool.query(
      `SELECT * FROM persons
       WHERE LOWER(first_name) = LOWER($1) AND LOWER(last_name) = LOWER($2)`,
      [first_name, last_name]
    );

    // If not found and flexible search is enabled, try reversed order
    if (result.rows.length === 0 && flexible === 'true') {
      result = await pool.query(
        `SELECT * FROM persons
         WHERE LOWER(first_name) = LOWER($1) AND LOWER(last_name) = LOWER($2)`,
        [last_name, first_name]
      );
    }

    // If still not found and flexible search is enabled, try partial matches
    if (result.rows.length === 0 && flexible === 'true') {
      // Create combined search terms
      const combinedName = `${first_name} ${last_name}`.toLowerCase();
      const reversedName = `${last_name} ${first_name}`.toLowerCase();

      result = await pool.query(
        `SELECT * FROM persons
         WHERE LOWER(first_name || ' ' || last_name) LIKE $1
         OR LOWER(last_name || ' ' || first_name) LIKE $2
         OR LOWER(first_name || ' ' || last_name) LIKE $3
         OR LOWER(last_name || ' ' || first_name) LIKE $4`,
        [`%${combinedName}%`, `%${combinedName}%`, `%${reversedName}%`, `%${reversedName}%`]
      );
    }

    if (result.rows.length === 0) {
      return res.json({
        exists: false,
        message: `Person "${first_name} ${last_name}" not found in database`,
        searchParams: { first_name, last_name, flexible }
      });
    }

    return res.json({
      exists: true,
      persons: result.rows,
      count: result.rows.length,
      message: result.rows.length === 1
        ? `Person "${first_name} ${last_name}" found in database`
        : `Found ${result.rows.length} potential matches for "${first_name} ${last_name}"`,
      searchParams: { first_name, last_name, flexible }
    });
  } catch (error) {
    console.error('Error checking person:', error);
    res.status(500).json({ message: 'Failed to check person', error: error.message });
  }
});

// Get single entry (with driver/codriver/team details) - PUBLIC ROUTE
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const result = await pool.query(`
      SELECT e.*,
        pd.first_name AS driver_first_name, pd.last_name AS driver_last_name, pd.nationality AS driver_nationality,
        pcd.first_name AS codriver_first_name, pcd.last_name AS codriver_last_name, pcd.nationality AS codriver_nationality,
        t.name AS team_name,
        oc.position,
        oc.total_time,
        oc.time_diff
      FROM entries e
      JOIN persons pd ON pd.id = e.driver_id
      JOIN persons pcd ON pcd.id = e.codriver_id
      LEFT JOIN teams t ON t.id = e.team_id
      LEFT JOIN overall_classification oc ON oc.entry_id = e.id
      WHERE e.id = $1
    `, [id]);
    if (result.rows.length === 0) return res.status(404).json({ message: 'Entry not found' });
    res.json(result.rows[0]);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Failed to fetch entry' });
  }
});

// Update entry
router.put('/:id', verifyToken, verifyAdmin, async (req, res) => {
  // Use a client with a transaction to ensure all operations succeed or fail together
  const client = await pool.connect();

  try {
    // Start transaction
    await client.query('BEGIN');

    const { id } = req.params;
    const { rally_id, driver_id, codriver_id, team_id, car, number, class: entryClass, status } = req.body;

    console.log('Updating entry with ID:', id);
    console.log('Request body:', req.body);

    // Validate input data
    if (!rally_id || !driver_id || !codriver_id || !car || number === undefined || !entryClass || !status) {
      throw new Error('Missing required fields');
    }

    // Validate status is a valid entry_status enum value
    const validStatuses = ['entered', 'running', 'finished', 'retired', 'dns', 'dnf', 'dsq'];
    if (!validStatuses.includes(status)) {
      throw new Error(`Invalid status value: "${status}". Valid values are: ${validStatuses.join(', ')}`);
    }

    // Check if the entry exists
    const entryCheck = await client.query('SELECT * FROM entries WHERE id = $1', [id]);
    if (entryCheck.rows.length === 0) {
      await client.query('ROLLBACK');
      return res.status(404).json({ message: 'Entry not found' });
    }

    // Check if the driver exists in the drivers table
    const driverCheck = await client.query('SELECT * FROM drivers WHERE id = $1', [driver_id]);
    if (driverCheck.rows.length === 0) {
      // Check if the person exists
      const personCheck = await client.query('SELECT * FROM persons WHERE id = $1', [driver_id]);
      if (personCheck.rows.length === 0) {
        await client.query('ROLLBACK');
        return res.status(404).json({ message: 'Driver person not found' });
      }

      // Add the person as a driver
      await client.query('INSERT INTO drivers (id) VALUES ($1)', [driver_id]);
      console.log(`Added person ${driver_id} as a driver automatically during update`);
    }

    // Check if the codriver exists in the codrivers table
    const codriverCheck = await client.query('SELECT * FROM codrivers WHERE id = $1', [codriver_id]);
    if (codriverCheck.rows.length === 0) {
      // Check if the person exists
      const personCheck = await client.query('SELECT * FROM persons WHERE id = $1', [codriver_id]);
      if (personCheck.rows.length === 0) {
        await client.query('ROLLBACK');
        return res.status(404).json({ message: 'Codriver person not found' });
      }

      // Add the person as a codriver
      await client.query('INSERT INTO codrivers (id) VALUES ($1)', [codriver_id]);
      console.log(`Added person ${codriver_id} as a codriver automatically during update`);
    }

    // Update the entry
    const result = await client.query(
      `UPDATE entries SET rally_id=$1, driver_id=$2, codriver_id=$3, team_id=$4, car=$5, number=$6, class=$7, status=$8, updated_at=NOW() WHERE id=$9 RETURNING *`,
      [rally_id, driver_id, codriver_id, team_id, car, number, entryClass, status, id]
    );

    if (result.rows.length === 0) {
      await client.query('ROLLBACK');
      return res.status(404).json({ message: 'Entry not found' });
    }

    // Fetch with joined details for response
    const details = await client.query(`
      SELECT e.*,
        pd.first_name AS driver_first_name, pd.last_name AS driver_last_name, pd.nationality AS driver_nationality,
        pcd.first_name AS codriver_first_name, pcd.last_name AS codriver_last_name, pcd.nationality AS codriver_nationality,
        t.name AS team_name
      FROM entries e
      JOIN persons pd ON pd.id = e.driver_id
      JOIN persons pcd ON pcd.id = e.codriver_id
      LEFT JOIN teams t ON t.id = e.team_id
      WHERE e.id = $1
    `, [id]);

    // Commit the transaction
    await client.query('COMMIT');

    res.json(details.rows[0]);
  } catch (error) {
    // Rollback the transaction in case of error
    await client.query('ROLLBACK');

    console.error('Error updating entry:', error);

    // Use the variables from the try block if they're available, otherwise use req.body and req.params
    const errorDetails = {
      id: req.params.id,
      ...req.body
    };
    console.error('Error details:', errorDetails);

    res.status(500).json({ message: 'Failed to update entry', error: error.message });
  } finally {
    // Release the client back to the pool
    client.release();
  }
});

// Delete entry
router.delete('/:id', verifyToken, verifyAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const result = await pool.query('DELETE FROM entries WHERE id = $1 RETURNING *', [id]);
    if (result.rows.length === 0) return res.status(404).json({ message: 'Entry not found' });
    res.json({ message: 'Entry deleted', entry: result.rows[0] });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Failed to delete entry' });
  }
});

// Import entries from CSV - ADMIN ROUTE
router.post('/import', verifyToken, verifyAdmin, upload.single('file'), async (req, res) => {
  const client = await pool.connect();
  try {
    if (!req.file) {
      return res.status(400).json({ message: 'No file uploaded' });
    }

    // Parse CSV file
    const fs = await import('fs');
    const csvContent = fs.readFileSync(req.file.path, 'utf8');
    const records = parse(csvContent, {
      columns: true,
      skip_empty_lines: true,
      trim: true
    });

    console.log('Parsed', records.length, 'records from CSV');

    // Results tracking
    const results = [];
    const errors = [];
    const skipped = [];

    // Check for required column headers (case-insensitive)
    const requiredColumns = [
      'rally_id',
      'driver_first_name', 'driver_last_name',
      'codriver_first_name', 'codriver_last_name',
      'car', 'number', 'class', 'status'
    ];

    // Column mapping logic similar to your persons import
    const columnMapping = {};

    // Process records if any exist
    if (records.length > 0) {
      // Log the first record for debugging
      console.log('First record sample:', records[0]);

      const firstRecord = records[0];
      const headers = Object.keys(firstRecord);

      requiredColumns.forEach(expectedCol => {
        const matchingHeader = headers.find(h => h.toLowerCase() === expectedCol.toLowerCase());
        if (matchingHeader) {
          columnMapping[expectedCol] = matchingHeader;
        }
      });

      // Check for missing columns
      const missingColumns = requiredColumns.filter(col => !columnMapping[col]);
      if (missingColumns.length > 0) {
        return res.status(400).json({
          message: `Missing required columns: ${missingColumns.join(', ')}`,
          error: 'CSV file must contain all required columns (case-insensitive)'
        });
      }

      // Log the column mapping for debugging
      console.log('Column mapping:', columnMapping);
    }

    // Process each record individually without a transaction
    // This way, if one record fails, others can still be processed
    console.log('Processing records individually without a transaction');

    for (const row of records) {
      // Use a separate transaction for each row
      const rowClient = await pool.connect();
      try {
        await rowClient.query('BEGIN');
        // Extract values using column mapping
        let rallyId = row[columnMapping['rally_id']];
        const driverFirstName = row[columnMapping['driver_first_name']];
        const driverLastName = row[columnMapping['driver_last_name']];
        const codriverFirstName = row[columnMapping['codriver_first_name']];
        const codriverLastName = row[columnMapping['codriver_last_name']];
        const teamName = columnMapping['team_name'] ? row[columnMapping['team_name']] : null;
        const car = row[columnMapping['car']];
        const number = row[columnMapping['number']];
        const entryClass = row[columnMapping['class']];
        const status = row[columnMapping['status']];

        // Check for required fields
        if (!rallyId || !driverFirstName || !driverLastName || !codriverFirstName || !codriverLastName || !car || !number || !entryClass || !status) {
          await rowClient.query('ROLLBACK');
          skipped.push({
            row: row,
            reason: `Missing required fields. All fields except team_name are required.`
          });
          continue;
        }

        // Fix rally_id if it's not a valid UUID (36 characters)
        if (rallyId && rallyId.length !== 36) {
          console.log(`Rally ID "${rallyId}" has incorrect length (${rallyId.length}). Attempting to fix...`);

          // If it's longer than 36 characters, truncate it
          if (rallyId.length > 36) {
            const originalId = rallyId;
            rallyId = rallyId.substring(0, 36);
            console.log(`Fixed rally ID: "${originalId}" -> "${rallyId}"`);
          }

          // Verify the fixed ID is a valid UUID format
          const uuidPattern = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
          if (!uuidPattern.test(rallyId)) {
            console.log(`Warning: Fixed rally ID "${rallyId}" does not match UUID pattern`);
            await rowClient.query('ROLLBACK');
            skipped.push({
              row: row,
              reason: `Invalid rally ID format: "${rallyId}". Must be a valid UUID.`
            });
            continue;
          }
        }

        // Look up driver ID by name
        console.log(`Looking up driver: "${driverFirstName} ${driverLastName}"`);

        // Try normal order first
        let driverResult = await rowClient.query(
          `SELECT id FROM persons
           WHERE LOWER(first_name) = LOWER($1) AND LOWER(last_name) = LOWER($2)`,
          [driverFirstName, driverLastName]
        );

        // If not found, try reversed order (in case first/last names are swapped)
        if (driverResult.rows.length === 0) {
          console.log(`Driver not found with normal order, trying reversed: "${driverLastName} ${driverFirstName}"`);
          driverResult = await rowClient.query(
            `SELECT id FROM persons
             WHERE LOWER(first_name) = LOWER($1) AND LOWER(last_name) = LOWER($2)`,
            [driverLastName, driverFirstName]
          );
        }

        // If still not found, try a more flexible search
        if (driverResult.rows.length === 0) {
          console.log(`Driver not found with exact matches, trying flexible search for: "${driverFirstName} ${driverLastName}"`);

          // Create a combined search term
          const combinedName = `${driverFirstName} ${driverLastName}`.toLowerCase();
          const reversedName = `${driverLastName} ${driverFirstName}`.toLowerCase();

          // Search for any person where the combined first+last name contains our search terms
          driverResult = await rowClient.query(
            `SELECT id, first_name, last_name FROM persons
             WHERE LOWER(first_name || ' ' || last_name) LIKE $1
             OR LOWER(last_name || ' ' || first_name) LIKE $2
             OR LOWER(first_name || ' ' || last_name) LIKE $3
             OR LOWER(last_name || ' ' || first_name) LIKE $4`,
            [`%${combinedName}%`, `%${combinedName}%`, `%${reversedName}%`, `%${reversedName}%`]
          );

          if (driverResult.rows.length > 0) {
            console.log(`Found ${driverResult.rows.length} potential driver matches with flexible search:`,
              driverResult.rows.map(r => `${r.first_name} ${r.last_name} (${r.id})`));

            // Use the first match
            console.log(`Using first match: ${driverResult.rows[0].first_name} ${driverResult.rows[0].last_name}`);
          }
        }

        if (driverResult.rows.length === 0) {
          console.log(`Driver not found with any search method: "${driverFirstName} ${driverLastName}"`);
          await rowClient.query('ROLLBACK');
          skipped.push({
            row: row,
            reason: `Driver "${driverFirstName} ${driverLastName}" not found in database`
          });
          continue;
        }
        const driverId = driverResult.rows[0].id;
        console.log(`Found driver: "${driverFirstName} ${driverLastName}" with ID: ${driverId}`);

        // Look up codriver ID by name
        console.log(`Looking up codriver: "${codriverFirstName} ${codriverLastName}"`);

        // Try normal order first
        let codriverResult = await rowClient.query(
          `SELECT id FROM persons
           WHERE LOWER(first_name) = LOWER($1) AND LOWER(last_name) = LOWER($2)`,
          [codriverFirstName, codriverLastName]
        );

        // If not found, try reversed order (in case first/last names are swapped)
        if (codriverResult.rows.length === 0) {
          console.log(`Codriver not found with normal order, trying reversed: "${codriverLastName} ${codriverFirstName}"`);
          codriverResult = await rowClient.query(
            `SELECT id FROM persons
             WHERE LOWER(first_name) = LOWER($1) AND LOWER(last_name) = LOWER($2)`,
            [codriverLastName, codriverFirstName]
          );
        }

        // If still not found, try a more flexible search
        if (codriverResult.rows.length === 0) {
          console.log(`Codriver not found with exact matches, trying flexible search for: "${codriverFirstName} ${codriverLastName}"`);

          // Create a combined search term
          const combinedName = `${codriverFirstName} ${codriverLastName}`.toLowerCase();
          const reversedName = `${codriverLastName} ${codriverFirstName}`.toLowerCase();

          // Search for any person where the combined first+last name contains our search terms
          codriverResult = await rowClient.query(
            `SELECT id, first_name, last_name FROM persons
             WHERE LOWER(first_name || ' ' || last_name) LIKE $1
             OR LOWER(last_name || ' ' || first_name) LIKE $2
             OR LOWER(first_name || ' ' || last_name) LIKE $3
             OR LOWER(last_name || ' ' || first_name) LIKE $4`,
            [`%${combinedName}%`, `%${combinedName}%`, `%${reversedName}%`, `%${reversedName}%`]
          );

          if (codriverResult.rows.length > 0) {
            console.log(`Found ${codriverResult.rows.length} potential codriver matches with flexible search:`,
              codriverResult.rows.map(r => `${r.first_name} ${r.last_name} (${r.id})`));

            // Use the first match
            console.log(`Using first match: ${codriverResult.rows[0].first_name} ${codriverResult.rows[0].last_name}`);
          }
        }

        if (codriverResult.rows.length === 0) {
          console.log(`Codriver not found with any search method: "${codriverFirstName} ${codriverLastName}"`);
          await rowClient.query('ROLLBACK');
          skipped.push({
            row: row,
            reason: `Codriver "${codriverFirstName} ${codriverLastName}" not found in database`
          });
          continue;
        }
        const codriverId = codriverResult.rows[0].id;
        console.log(`Found codriver: "${codriverFirstName} ${codriverLastName}" with ID: ${codriverId}`);

        // Look up team ID by name if provided
        let teamId = null;
        if (teamName) {
          const teamResult = await rowClient.query(
            `SELECT id FROM teams WHERE LOWER(name) = LOWER($1)`,
            [teamName]
          );
          if (teamResult.rows.length > 0) {
            teamId = teamResult.rows[0].id;
          }
        }

        // Validate status is a valid entry_status enum value
        const validStatuses = ['entered', 'running', 'finished', 'retired', 'dns', 'dnf', 'dsq'];
        if (!validStatuses.includes(status.toLowerCase())) {
          await rowClient.query('ROLLBACK');
          // Don't release the client here, it will be released in the finally block
          skipped.push({
            row: row,
            reason: `Invalid status value: "${status}". Valid values are: ${validStatuses.join(', ')}`
          });
          continue;
        }

        // Check if the driver exists in the drivers table
        const driverCheck = await rowClient.query('SELECT * FROM drivers WHERE id = $1', [driverId]);
        if (driverCheck.rows.length === 0) {
          // Add the person as a driver
          await rowClient.query('INSERT INTO drivers (id) VALUES ($1)', [driverId]);
          console.log(`Added person ${driverId} as a driver automatically`);
        }

        // Check if the codriver exists in the codrivers table
        const codriverCheck = await rowClient.query('SELECT * FROM codrivers WHERE id = $1', [codriverId]);
        if (codriverCheck.rows.length === 0) {
          // Add the person as a codriver
          await rowClient.query('INSERT INTO codrivers (id) VALUES ($1)', [codriverId]);
          console.log(`Added person ${codriverId} as a codriver automatically`);
        }

        // Check if entry already exists
        const existingEntry = await rowClient.query(
          `SELECT * FROM entries
           WHERE rally_id = $1 AND driver_id = $2 AND codriver_id = $3`,
          [rallyId, driverId, codriverId]
        );

        if (existingEntry.rows.length > 0) {
          await rowClient.query('ROLLBACK');
          // Don't release the client here, it will be released in the finally block
          skipped.push({
            row: row,
            reason: `Entry for driver "${driverFirstName} ${driverLastName}" and codriver "${codriverFirstName} ${codriverLastName}" in this rally already exists`
          });
          continue;
        }

        // Check if the rally exists
        const rallyCheck = await rowClient.query('SELECT * FROM rallies WHERE id = $1', [rallyId]);
        if (rallyCheck.rows.length === 0) {
          await rowClient.query('ROLLBACK');
          // Don't release the client here, it will be released in the finally block
          skipped.push({
            row: row,
            reason: `Rally with ID "${rallyId}" does not exist`
          });
          continue;
        }

        // Create the entry
        const result = await rowClient.query(
          `INSERT INTO entries (rally_id, driver_id, codriver_id, team_id, car, number, class, status)
           VALUES ($1, $2, $3, $4, $5, $6, $7, $8) RETURNING *`,
          [rallyId, driverId, codriverId, teamId, car, number, entryClass, status.toLowerCase()]
        );

        results.push(result.rows[0]);

        // Commit this row's transaction
        await rowClient.query('COMMIT');
        console.log(`Successfully imported entry for ${driverFirstName} ${driverLastName} and ${codriverFirstName} ${codriverLastName}`);
      } catch (rowError) {
        // Rollback this row's transaction
        await rowClient.query('ROLLBACK');
        console.error('Error importing row:', row, rowError);
        errors.push({
          row: row,
          error: rowError.message
        });
      } finally {
        // Release the client for this row if it hasn't been released yet
        try {
          if (rowClient) {
            rowClient.release();
          }
        } catch (releaseError) {
          console.error('Error releasing client:', releaseError.message);
        }
      }
    }

    // Count reasons for skipping
    const skipReasons = {};
    skipped.forEach(item => {
      if (!skipReasons[item.reason]) {
        skipReasons[item.reason] = 0;
      }
      skipReasons[item.reason]++;
    });

    // Get the first few skipped entries for debugging
    const sampleSkipped = skipped.slice(0, 5).map(item => ({
      reason: item.reason,
      data: item.row
    }));

    res.json({
      success: true,
      imported: results.length,
      errors: errors.length,
      skipped: skipped.length,
      skipReasons,
      sampleSkipped,
      details: { results, errors, skipped }
    });
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Import error:', error);
    res.status(500).json({ message: 'Failed to import entries', error: error.message });
  } finally {
    // Clean up the temporary file
    try {
      const fs = await import('fs');
      if (req.file && req.file.path) {
        fs.unlinkSync(req.file.path);
      }
    } catch (cleanupError) {
      console.error('Error cleaning up temporary file:', cleanupError);
    }

    client.release();
  }
});

export default router;
