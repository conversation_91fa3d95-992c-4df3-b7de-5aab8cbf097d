-- Create championship views based on actual entry-championship associations
-- This replaces class-based filtering with explicit entry registrations from EWRC data

-- Drop existing views if they exist
DROP VIEW IF EXISTS championship_overall_classification_new CASCADE;
DROP VIEW IF EXISTS championship_standings_new CASCADE;

-- 1. Championship Overall Classification - Based on Entry Associations
CREATE VIEW championship_overall_classification_new AS
SELECT
    ce.championship_id,
    c.name as championship_name,
    r.id as rally_id,
    r.name as rally_name,
    r.start_date as rally_date,
    e.id as entry_id,
    e.number,
    CONCAT(pd.first_name, ' ', pd.last_name) as driver,
    pd.nationality as driver_nationality,
    CONCAT(pc.first_name, ' ', pc.last_name) as codriver,
    pc.nationality as codriver_nationality,
    e.car,
    e.class,
    oc.position as overall_position,
    -- Calculate championship position within this championship
    ROW_NUMBER() OVER (
        PARTITION BY ce.championship_id, r.id
        ORDER BY oc.position
    ) as position,
    oc.total_time,
    oc.time_diff,
    -- Calculate championship points based on championship position
    CASE
        WHEN ROW_NUMBER() OVER (PARTITION BY ce.championship_id, r.id ORDER BY oc.position) = 1 THEN 25 * ce.coefficient
        WHEN ROW_NUMBER() OVER (PARTITION BY ce.championship_id, r.id ORDER BY oc.position) = 2 THEN 18 * ce.coefficient
        WHEN ROW_NUMBER() OVER (PARTITION BY ce.championship_id, r.id ORDER BY oc.position) = 3 THEN 15 * ce.coefficient
        WHEN ROW_NUMBER() OVER (PARTITION BY ce.championship_id, r.id ORDER BY oc.position) = 4 THEN 12 * ce.coefficient
        WHEN ROW_NUMBER() OVER (PARTITION BY ce.championship_id, r.id ORDER BY oc.position) = 5 THEN 10 * ce.coefficient
        WHEN ROW_NUMBER() OVER (PARTITION BY ce.championship_id, r.id ORDER BY oc.position) = 6 THEN 8 * ce.coefficient
        WHEN ROW_NUMBER() OVER (PARTITION BY ce.championship_id, r.id ORDER BY oc.position) = 7 THEN 6 * ce.coefficient
        WHEN ROW_NUMBER() OVER (PARTITION BY ce.championship_id, r.id ORDER BY oc.position) = 8 THEN 4 * ce.coefficient
        WHEN ROW_NUMBER() OVER (PARTITION BY ce.championship_id, r.id ORDER BY oc.position) = 9 THEN 2 * ce.coefficient
        WHEN ROW_NUMBER() OVER (PARTITION BY ce.championship_id, r.id ORDER BY oc.position) = 10 THEN 1 * ce.coefficient
        ELSE 0
    END::INTEGER as championship_points
FROM championship_events ce
JOIN championships c ON ce.championship_id = c.id
JOIN rallies r ON ce.rally_id = r.id
JOIN entries e ON r.id = e.rally_id
JOIN entry_championships ec ON e.id = ec.entry_id AND ec.championship_id = ce.championship_id
JOIN persons pd ON e.driver_id = pd.id
JOIN persons pc ON e.codriver_id = pc.id
JOIN overall_classification oc ON e.id = oc.entry_id
WHERE e.status IN ('finished', 'retired', 'dnf')
ORDER BY ce.championship_id, r.start_date, position;

-- 2. Championship Standings - Based on Entry Associations
CREATE VIEW championship_standings_new AS
SELECT
    championship_id,
    championship_name,
    driver,
    driver_nationality,
    COUNT(DISTINCT rally_id) as rallies_completed,
    SUM(championship_points) as total_points,
    RANK() OVER (PARTITION BY championship_id ORDER BY SUM(championship_points) DESC) as position,
    STRING_AGG(
        rally_name || ' (' || position || 'th, ' || championship_points || 'pts)',
        ', ' ORDER BY rally_date
    ) as rally_results
FROM championship_overall_classification_new
GROUP BY championship_id, championship_name, driver, driver_nationality
ORDER BY championship_id, total_points DESC;

-- Add comments
COMMENT ON VIEW championship_overall_classification_new IS 'Championship classification based on actual entry registrations from EWRC data';
COMMENT ON VIEW championship_standings_new IS 'Championship standings based on actual entry registrations from EWRC data';

-- Show sample data
SELECT 'Sample Championship Overall Classification (Entry-Based):' as info;
SELECT
    championship_name,
    rally_name,
    driver,
    class,
    position,
    championship_points
FROM championship_overall_classification_new
WHERE rally_name LIKE '%Stereas Elladas 2025%'
ORDER BY championship_name, position
LIMIT 10;

SELECT 'Sample Championship Standings (Entry-Based):' as info;
SELECT
    championship_name,
    driver,
    rallies_completed,
    total_points,
    position
FROM championship_standings_new
ORDER BY championship_name, position
LIMIT 10;
