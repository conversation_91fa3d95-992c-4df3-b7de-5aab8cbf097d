import express from 'express';
import pool from '../config/db.js';

const router = express.Router();

// Get all individual classes for a rally - PUBLIC ROUTE
router.get('/rally/:rallyId', async (req, res) => {
  try {
    const { rallyId } = req.params;
    const { separator = ',' } = req.query; // Default to comma separator
    
    const result = await pool.query(`
      SELECT DISTINCT
        TRIM(unnest(string_to_array(e.class, $2))) AS class_name,
        COUNT(*) OVER (PARTITION BY TRIM(unnest(string_to_array(e.class, $2)))) AS entry_count
      FROM entries e
      WHERE e.rally_id = $1
        AND e.class IS NOT NULL 
        AND e.class != ''
      ORDER BY class_name
    `, [rallyId, separator]);
    
    res.json(result.rows);
  } catch (error) {
    console.error('Error fetching rally classes:', error);
    res.status(500).json({ message: 'Failed to fetch rally classes' });
  }
});

// Get entries for a specific class in a rally - PUBLIC ROUTE
router.get('/rally/:rallyId/class/:className', async (req, res) => {
  try {
    const { rallyId, className } = req.params;
    const { separator = ',' } = req.query;
    
    const result = await pool.query(`
      SELECT 
        e.id,
        e.number,
        e.car,
        e.class AS all_classes,
        e.status,
        pd.first_name AS driver_first_name,
        pd.last_name AS driver_last_name,
        pd.nationality AS driver_nationality,
        pcd.first_name AS codriver_first_name,
        pcd.last_name AS codriver_last_name,
        pcd.nationality AS codriver_nationality,
        t.name AS team_name,
        oc.position AS overall_position,
        oc.total_time,
        oc.time_diff
      FROM entries e
      JOIN persons pd ON pd.id = e.driver_id
      JOIN persons pcd ON pcd.id = e.codriver_id
      LEFT JOIN teams t ON t.id = e.team_id
      LEFT JOIN overall_classification oc ON oc.entry_id = e.id
      WHERE e.rally_id = $1
        AND $2 = ANY(string_to_array(e.class, $3))
      ORDER BY oc.position NULLS LAST, e.number
    `, [rallyId, className, separator]);
    
    res.json(result.rows);
  } catch (error) {
    console.error('Error fetching class entries:', error);
    res.status(500).json({ message: 'Failed to fetch class entries' });
  }
});

// Get class standings for a rally - PUBLIC ROUTE
router.get('/rally/:rallyId/standings', async (req, res) => {
  try {
    const { rallyId } = req.params;
    const { separator = ',' } = req.query;
    
    const result = await pool.query(`
      SELECT
        e.rally_id,
        TRIM(unnest(string_to_array(e.class, $2))) AS class_name,
        e.id AS entry_id,
        e.number,
        pd.first_name || ' ' || pd.last_name AS driver,
        pcd.first_name || ' ' || pcd.last_name AS codriver,
        e.car,
        t.name AS team_name,
        SUM(r.time + COALESCE(p.time, 0)) AS total_time,
        RANK() OVER (
          PARTITION BY TRIM(unnest(string_to_array(e.class, $2)))
          ORDER BY SUM(r.time + COALESCE(p.time, 0))
        ) AS class_position
      FROM entries e
      JOIN results r ON r.entry_id = e.id
      JOIN persons pd ON pd.id = e.driver_id
      JOIN persons pcd ON pcd.id = e.codriver_id
      LEFT JOIN teams t ON t.id = e.team_id
      LEFT JOIN penalties p ON p.entry_id = e.id AND p.stage_id = r.stage_id
      WHERE e.rally_id = $1
        AND e.status NOT IN ('retired', 'dnf', 'dsq')
      GROUP BY 
        e.rally_id, 
        e.id, 
        e.number,
        pd.first_name, 
        pd.last_name,
        pcd.first_name,
        pcd.last_name,
        e.car,
        t.name,
        e.class
      ORDER BY class_name, class_position
    `, [rallyId, separator]);
    
    // Group results by class
    const classStandings = {};
    result.rows.forEach(row => {
      if (!classStandings[row.class_name]) {
        classStandings[row.class_name] = [];
      }
      classStandings[row.class_name].push(row);
    });
    
    res.json(classStandings);
  } catch (error) {
    console.error('Error fetching class standings:', error);
    res.status(500).json({ message: 'Failed to fetch class standings' });
  }
});

// Utility endpoint to split classes for an entry - PUBLIC ROUTE
router.get('/entry/:entryId/classes', async (req, res) => {
  try {
    const { entryId } = req.params;
    const { separator = ',' } = req.query;
    
    const result = await pool.query(`
      SELECT 
        e.id,
        e.class AS original_class,
        ARRAY(
          SELECT TRIM(unnest(string_to_array(e.class, $2)))
        ) AS individual_classes
      FROM entries e
      WHERE e.id = $1
    `, [entryId, separator]);
    
    if (result.rows.length === 0) {
      return res.status(404).json({ message: 'Entry not found' });
    }
    
    res.json(result.rows[0]);
  } catch (error) {
    console.error('Error splitting entry classes:', error);
    res.status(500).json({ message: 'Failed to split entry classes' });
  }
});

export default router;
