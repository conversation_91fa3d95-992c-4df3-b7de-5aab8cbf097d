import express from 'express';
import pool from '../config/db.js';
import { verifyToken, verifyAdmin } from '../middleware/auth.js';

const router = express.Router();

// Get all codrivers (with person details) - PUBLIC ROUTE
router.get('/', async (req, res) => {
  try {
    const result = await pool.query(`
      SELECT c.id, p.first_name, p.last_name, p.nationality, p.date_of_birth, p.photo_url, p.bio, p.created_at, p.updated_at
      FROM codrivers c
      JOIN persons p ON c.id = p.id
      ORDER BY p.last_name, p.first_name
    `);
    res.json(result.rows);
  } catch (error) {
    res.status(500).json({ message: 'Failed to fetch codrivers' });
  }
});

// Get single codriver (with person details) - PUBLIC ROUTE
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const result = await pool.query(`
      SELECT c.id, p.first_name, p.last_name, p.nationality, p.date_of_birth, p.photo_url, p.bio, p.created_at, p.updated_at
      FROM codrivers c
      JOIN persons p ON c.id = p.id
      WHERE c.id = $1
    `, [id]);
    if (result.rows.length === 0) return res.status(404).json({ message: 'Codriver not found' });
    res.json(result.rows[0]);
  } catch (error) {
    res.status(500).json({ message: 'Failed to fetch codriver' });
  }
});

// Create codriver (person + codriver)
router.post('/', verifyToken, verifyAdmin, async (req, res) => {
  try {
    // Check if we're adding an existing person as a codriver
    if (req.body.id) {
      const personId = req.body.id;

      // Check if the person exists
      const personCheck = await pool.query('SELECT * FROM persons WHERE id = $1', [personId]);
      if (personCheck.rows.length === 0) {
        return res.status(404).json({ message: 'Person not found' });
      }

      // Check if the person is already a codriver
      const codriverCheck = await pool.query('SELECT * FROM codrivers WHERE id = $1', [personId]);
      if (codriverCheck.rows.length > 0) {
        return res.status(400).json({ message: 'Person is already a codriver' });
      }

      // Add the person as a codriver
      await pool.query('INSERT INTO codrivers (id) VALUES ($1)', [personId]);

      // Return the codriver with person details
      const codriver = await pool.query(`
        SELECT c.id, p.first_name, p.last_name, p.nationality, p.date_of_birth, p.photo_url, p.bio, p.created_at, p.updated_at
        FROM codrivers c
        JOIN persons p ON c.id = p.id
        WHERE c.id = $1
      `, [personId]);

      return res.status(201).json(codriver.rows[0]);
    }

    // Original code for creating a new person and codriver
    const { first_name, last_name, nationality, date_of_birth, photo_url, bio } = req.body;
    const personResult = await pool.query(
      `INSERT INTO persons (first_name, last_name, nationality, date_of_birth, photo_url, bio) VALUES ($1, $2, $3, $4, $5, $6) RETURNING id`,
      [first_name, last_name, nationality, date_of_birth, photo_url, bio]
    );
    const personId = personResult.rows[0].id;
    await pool.query(`INSERT INTO codrivers (id) VALUES ($1)`, [personId]);
    const codriver = await pool.query(`
      SELECT c.id, p.first_name, p.last_name, p.nationality, p.date_of_birth, p.photo_url, p.bio, p.created_at, p.updated_at
      FROM codrivers c
      JOIN persons p ON c.id = p.id
      WHERE c.id = $1
    `, [personId]);
    res.status(201).json(codriver.rows[0]);
  } catch (error) {
    res.status(500).json({ message: 'Failed to add codriver' });
  }
});

// Update codriver (person)
router.put('/:id', verifyToken, verifyAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { first_name, last_name, nationality, date_of_birth, photo_url, bio } = req.body;
    const result = await pool.query(
      `UPDATE persons SET first_name=$1, last_name=$2, nationality=$3, date_of_birth=$4, photo_url=$5, bio=$6, updated_at=NOW() WHERE id=$7 RETURNING *`,
      [first_name, last_name, nationality, date_of_birth, photo_url, bio, id]
    );
    if (result.rows.length === 0) return res.status(404).json({ message: 'Codriver not found' });
    res.json(result.rows[0]);
  } catch (error) {
    res.status(500).json({ message: 'Failed to update codriver' });
  }
});

// Delete codriver (person + codriver)
router.delete('/:id', verifyToken, verifyAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    // Deleting from persons will cascade to codrivers due to FK
    const result = await pool.query('DELETE FROM persons WHERE id = $1 RETURNING *', [id]);
    if (result.rows.length === 0) return res.status(404).json({ message: 'Codriver not found' });
    res.json({ message: 'Codriver deleted', codriver: result.rows[0] });
  } catch (error) {
    res.status(500).json({ message: 'Failed to delete codriver' });
  }
});

export default router;
