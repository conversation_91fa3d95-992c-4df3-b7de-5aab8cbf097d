import React from 'react';
import { Trophy, Clock } from 'lucide-react';
import { ChampionshipOverallClassification } from '../../types';
import { formatTime } from '../../utils/timeUtils';
import { getCountryCode } from '../../utils/countryUtils';

interface ChampionshipResultsTableProps {
  results: ChampionshipOverallClassification[];
  championshipName?: string;
  showChampionshipInfo?: boolean;
}

const ChampionshipResultsTable: React.FC<ChampionshipResultsTableProps> = ({
  results,
  championshipName,
  showChampionshipInfo = false
}) => {
  if (results.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500 dark:text-gray-400">
        No results available for this championship
      </div>
    );
  }

  const getPositionIcon = (position: number) => {
    if (position === 1) return <Trophy className="w-4 h-4 text-yellow-500" />;
    if (position === 2) return <Trophy className="w-4 h-4 text-gray-400" />;
    if (position === 3) return <Trophy className="w-4 h-4 text-amber-700" />;
    return null;
  };

  const getPositionClass = (position: number) => {
    if (position === 1) return 'text-yellow-600 font-bold';
    if (position === 2) return 'text-gray-500 font-semibold';
    if (position === 3) return 'text-amber-700 font-semibold';
    return 'text-gray-900 dark:text-white';
  };

  return (
    <div className="overflow-x-auto">
      {showChampionshipInfo && championshipName && (
        <div className="mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <h3 className="text-lg font-semibold text-blue-900 dark:text-blue-100">
            {championshipName} Results
          </h3>
          {results[0]?.coefficient !== 1.0 && (
            <p className="text-sm text-blue-700 dark:text-blue-300">
              Points coefficient: {results[0].coefficient}x
            </p>
          )}
        </div>
      )}

      <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
        <thead className="bg-gray-50 dark:bg-gray-800">
          <tr>
            <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              Pos
            </th>
            <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              #
            </th>
            <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              Driver / Codriver
            </th>
            <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              Car
            </th>
            <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              Class
            </th>
            <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              Time
            </th>
            <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              Diff
            </th>
          </tr>
        </thead>
        <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
          {results.map((result) => (
            <tr key={result.entry_id} className="hover:bg-gray-50 dark:hover:bg-gray-800">
              <td className="px-3 py-4 whitespace-nowrap">
                <div className={`flex items-center space-x-1 ${getPositionClass(result.championship_position)}`}>
                  {getPositionIcon(result.championship_position)}
                  <span className="text-sm font-medium">
                    {result.championship_position}
                  </span>
                </div>
              </td>

              <td className="px-3 py-4 whitespace-nowrap">
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  {result.number}
                </span>
              </td>

              <td className="px-3 py-4">
                <div className="space-y-1">
                  <div className="flex items-center space-x-2">
                    {result.driver_nationality && (
                      <span
                        className={`fi fi-${getCountryCode(result.driver_nationality)}`}
                        style={{ width: '16px', height: '12px' }}
                      ></span>
                    )}
                    <span className="text-sm font-medium text-gray-900 dark:text-white">
                      {result.driver}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    {result.codriver_nationality && (
                      <span
                        className={`fi fi-${getCountryCode(result.codriver_nationality)}`}
                        style={{ width: '16px', height: '12px' }}
                      ></span>
                    )}
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      {result.codriver}
                    </span>
                  </div>
                </div>
              </td>

              <td className="px-3 py-4 whitespace-nowrap">
                <span className="text-sm text-gray-900 dark:text-white">
                  {result.car}
                </span>
              </td>

              <td className="px-3 py-4 whitespace-nowrap">
                <span className="inline-flex px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 rounded-full">
                  {result.class}
                </span>
              </td>

              <td className="px-3 py-4 whitespace-nowrap">
                <div className="flex items-center space-x-1">
                  <Clock className="w-4 h-4 text-gray-400" />
                  <span className="text-sm font-mono text-gray-900 dark:text-white">
                    {formatTime(result.total_time)}
                  </span>
                </div>
              </td>

              <td className="px-3 py-4 whitespace-nowrap">
                <span className="text-sm font-mono text-gray-600 dark:text-gray-400">
                  {result.time_diff > 0 ? `+${formatTime(result.time_diff)}` : '-'}
                </span>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default ChampionshipResultsTable;
