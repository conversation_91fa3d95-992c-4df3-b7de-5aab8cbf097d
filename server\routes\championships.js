import express from 'express';
import pool from '../config/db.js';
import { verifyToken, verifyAdmin } from '../middleware/auth.js';

const router = express.Router();

// Create championship
router.post('/', verifyToken, verifyAdmin, async (req, res) => {
  try {
    const { name, year, type, description } = req.body;
    const result = await pool.query(
      `INSERT INTO championships (name, year, type, description) VALUES ($1, $2, $3, $4) RETURNING *`,
      [name, year, type, description]
    );
    res.status(201).json(result.rows[0]);
  } catch (error) {
    res.status(500).json({ message: 'Failed to add championship' });
  }
});

// Get all championships - PUBLIC ROUTE
router.get('/', async (req, res) => {
  try {
    const result = await pool.query('SELECT * FROM championships ORDER BY year DESC, name ASC');
    res.json(result.rows);
  } catch (error) {
    res.status(500).json({ message: 'Failed to fetch championships' });
  }
});

// Handle the /manage path by redirecting to the main championships endpoint - ADMIN ONLY
router.get('/manage', verifyToken, verifyAdmin, async (req, res) => {
  try {
    const result = await pool.query('SELECT * FROM championships ORDER BY year DESC, name ASC');
    res.json(result.rows);
  } catch (error) {
    res.status(500).json({ message: 'Failed to fetch championships' });
  }
});

// Get single championship - PUBLIC ROUTE
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const result = await pool.query('SELECT * FROM championships WHERE id = $1', [id]);
    if (result.rows.length === 0) return res.status(404).json({ message: 'Championship not found' });
    res.json(result.rows[0]);
  } catch (error) {
    res.status(500).json({ message: 'Failed to fetch championship' });
  }
});

// Update championship
router.put('/:id', verifyToken, verifyAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { name, year, type, description } = req.body;
    const result = await pool.query(
      `UPDATE championships SET name=$1, year=$2, type=$3, description=$4, updated_at=NOW() WHERE id=$5 RETURNING *`,
      [name, year, type, description, id]
    );
    if (result.rows.length === 0) return res.status(404).json({ message: 'Championship not found' });
    res.json(result.rows[0]);
  } catch (error) {
    res.status(500).json({ message: 'Failed to update championship' });
  }
});

// Delete championship
router.delete('/:id', verifyToken, verifyAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const result = await pool.query('DELETE FROM championships WHERE id = $1 RETURNING *', [id]);
    if (result.rows.length === 0) return res.status(404).json({ message: 'Championship not found' });
    res.json({ message: 'Championship deleted', championship: result.rows[0] });
  } catch (error) {
    res.status(500).json({ message: 'Failed to delete championship' });
  }
});

export default router;
