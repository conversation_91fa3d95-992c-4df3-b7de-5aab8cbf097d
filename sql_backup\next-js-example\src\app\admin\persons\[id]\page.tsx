'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { supabase } from '@/lib/supabase/client';

interface Person {
  id: string;
  first_name: string;
  last_name: string;
  nationality: string;
  flag_code?: string;
  date_of_birth?: string;
  photo_url?: string;
  bio?: string;
  created_at?: string;
  updated_at?: string;
}

export default function EditPersonPage({ params }: { params: { id: string } }) {
  const [form, setForm] = useState<Partial<Person>>({
    first_name: '',
    last_name: '',
    nationality: '',
    date_of_birth: '',
    photo_url: '',
    bio: ''
  });
  const [flagCode, setFlagCode] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  // Update flag code when nationality changes
  useEffect(() => {
    setFlagCode(getCountryCode(form.nationality || ''));
  }, [form.nationality]);

  useEffect(() => {
    fetchPerson();
    // eslint-disable-next-line
  }, [params.id]);

  // Helper function to get country code from nationality
  const getCountryCode = (nationality: string): string => {
    // This is a placeholder - you would implement your actual country code logic here
    return nationality.toLowerCase().substring(0, 2);
  };

  const fetchPerson = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('persons')
        .select('*')
        .eq('id', params.id)
        .single();
      
      if (error) throw error;
      
      setForm({
        first_name: data.first_name || '',
        last_name: data.last_name || '',
        nationality: data.nationality || '',
        date_of_birth: data.date_of_birth ? data.date_of_birth.slice(0, 10) : '',
        photo_url: data.photo_url || '',
        bio: data.bio || ''
      });

      // Set flag code from API or calculate it
      if (data.flag_code) {
        setFlagCode(data.flag_code);
      } else if (data.nationality) {
        setFlagCode(getCountryCode(data.nationality));
      }
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    try {
      const { error } = await supabase
        .from('persons')
        .update(form)
        .eq('id', params.id);
      
      if (error) throw error;
      
      router.push('/admin/persons');
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!window.confirm('Are you sure you want to delete this person?')) {
      return;
    }

    setLoading(true);
    try {
      const { error } = await supabase
        .from('persons')
        .delete()
        .eq('id', params.id);
      
      if (error) throw error;
      
      router.push('/admin/persons');
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-lg mx-auto bg-white dark:bg-gray-800 rounded-lg shadow p-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Edit Person</h1>
          <button
            onClick={handleDelete}
            className="px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700 transition-colors"
          >
            Delete
          </button>
        </div>
        {error && <div className="mb-4 p-3 bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-200 rounded">{error}</div>}
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <input
              name="first_name"
              value={form.first_name}
              onChange={handleChange}
              required
              placeholder="First Name"
              className="w-full px-3 py-2 rounded border dark:bg-gray-700 dark:text-white"
            />
            <input
              name="last_name"
              value={form.last_name}
              onChange={handleChange}
              required
              placeholder="Last Name"
              className="w-full px-3 py-2 rounded border dark:bg-gray-700 dark:text-white"
            />
          </div>
          <div className="relative">
            <input
              name="nationality"
              value={form.nationality}
              onChange={handleChange}
              required
              placeholder="Nationality"
              className={`w-full px-3 py-2 rounded border dark:bg-gray-700 dark:text-white ${flagCode ? 'pl-10' : ''}`}
            />
            {flagCode && (
              <span
                className={`fi fi-${flagCode} absolute left-3 top-1/2 transform -translate-y-1/2`}
                style={{ width: '20px', height: '15px' }}
              ></span>
            )}
          </div>
          <div className="grid grid-cols-1 gap-4">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Date of Birth
              <input
                name="date_of_birth"
                value={form.date_of_birth}
                onChange={handleChange}
                type="date"
                className="mt-1 w-full px-3 py-2 rounded border dark:bg-gray-700 dark:text-white"
              />
            </label>
          </div>
          <input
            name="photo_url"
            value={form.photo_url}
            onChange={handleChange}
            placeholder="Photo URL"
            className="w-full px-3 py-2 rounded border dark:bg-gray-700 dark:text-white"
          />
          <textarea
            name="bio"
            value={form.bio}
            onChange={handleChange}
            placeholder="Biography"
            rows={4}
            className="w-full px-3 py-2 rounded border dark:bg-gray-700 dark:text-white"
          />
          <div className="flex justify-between">
            <button
              type="button"
              onClick={() => router.push('/admin/persons')}
              className="px-4 py-2 bg-gray-300 text-gray-800 rounded hover:bg-gray-400 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors disabled:opacity-50"
            >
              {loading ? 'Saving...' : 'Save Changes'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
