// Fixed API Endpoints - Works with your existing schema
// Replace the content in server/routes/championshipResults.js

const express = require('express');
const router = express.Router();
const pool = require('../db');

// Get all championships - PUBLIC ROUTE
router.get('/championships', async (req, res) => {
  try {
    const result = await pool.query(`
      SELECT 
        c.id,
        c.name,
        c.year,
        c.type,
        c.description,
        COUNT(DISTINCT ce.rally_id) as rally_count
      FROM championships c
      LEFT JOIN championship_events ce ON c.id = ce.championship_id
      GROUP BY c.id, c.name, c.year, c.type, c.description
      ORDER BY c.year DESC, c.name
    `);
    res.json(result.rows);
  } catch (error) {
    console.error('Error fetching championships:', error);
    res.status(500).json({ message: 'Failed to fetch championships' });
  }
});

// Get championships for a specific rally - PUBLIC ROUTE
router.get('/rallies/:rallyId/championships', async (req, res) => {
  try {
    const { rallyId } = req.params;
    
    const result = await pool.query(`
      SELECT 
        c.id,
        c.name,
        c.year,
        c.type,
        ce.coefficient
      FROM championship_events ce
      JOIN championships c ON c.id = ce.championship_id
      WHERE ce.rally_id = $1
      ORDER BY c.name
    `, [rallyId]);
    
    res.json(result.rows);
  } catch (error) {
    console.error('Error fetching rally championships:', error);
    res.status(500).json({ message: 'Failed to fetch rally championships' });
  }
});

// Get championship-specific results for a rally - PUBLIC ROUTE
router.get('/rallies/:rallyId/results', async (req, res) => {
  try {
    const { rallyId } = req.params;
    const { championshipId } = req.query;
    
    let query;
    let params;
    
    if (championshipId) {
      // Championship-specific results using existing view
      query = `
        SELECT 
          coc.entry_id,
          coc.number,
          coc.driver,
          coc.codriver,
          coc.class,
          coc.car,
          coc.total_time,
          coc.championship_position,
          coc.time_diff,
          -- Calculate points using existing logic
          CASE
            WHEN coc.championship_position = 1 THEN 25 * coc.coefficient
            WHEN coc.championship_position = 2 THEN 18 * coc.coefficient
            WHEN coc.championship_position = 3 THEN 15 * coc.coefficient
            WHEN coc.championship_position = 4 THEN 12 * coc.coefficient
            WHEN coc.championship_position = 5 THEN 10 * coc.coefficient
            WHEN coc.championship_position = 6 THEN 8 * coc.coefficient
            WHEN coc.championship_position = 7 THEN 6 * coc.coefficient
            WHEN coc.championship_position = 8 THEN 4 * coc.coefficient
            WHEN coc.championship_position = 9 THEN 2 * coc.coefficient
            WHEN coc.championship_position = 10 THEN 1 * coc.coefficient
            ELSE 0
          END as points
        FROM championship_overall_classification coc
        WHERE coc.rally_id = $1 AND coc.championship_id = $2
        ORDER BY coc.championship_position ASC
      `;
      params = [rallyId, championshipId];
    } else {
      // All results (no championship filter) using existing view
      query = `
        SELECT 
          oc.entry_id,
          oc.number,
          oc.driver,
          oc.codriver,
          e.class,
          e.car,
          oc.total_time,
          oc.position as championship_position,
          oc.time_diff,
          0 as points
        FROM overall_classification oc
        JOIN entries e ON e.id = oc.entry_id
        WHERE oc.rally_id = $1
        ORDER BY oc.position ASC
      `;
      params = [rallyId];
    }
    
    const result = await pool.query(query, params);
    res.json(result.rows);
  } catch (error) {
    console.error('Error fetching championship results:', error);
    res.status(500).json({ message: 'Failed to fetch championship results' });
  }
});

// Get championship standings - PUBLIC ROUTE
router.get('/championships/:championshipId/standings', async (req, res) => {
  try {
    const { championshipId } = req.params;
    const { className } = req.query;
    
    let query;
    let params;
    
    if (className) {
      // Class-specific standings using existing view
      query = `
        SELECT 
          ccs.championship_id,
          ccs.championship_name,
          ccs.class,
          ccs.driver_id,
          ccs.driver,
          ccs.rallies_completed,
          ccs.class_points as total_points,
          ccs.class_position as position,
          ccs.total_power_stage_points,
          ccs.grand_total_points
        FROM championship_class_standings ccs
        WHERE ccs.championship_id = $1 AND ccs.class = $2
        ORDER BY ccs.class_position
      `;
      params = [championshipId, className];
    } else {
      // Overall championship standings using existing view
      query = `
        SELECT 
          cs.championship_id,
          cs.championship_name,
          cs.driver_id,
          cs.driver,
          cs.rallies_completed,
          cs.total_points,
          cs.position,
          cs.total_power_stage_points,
          cs.grand_total_points
        FROM championship_standings cs
        WHERE cs.championship_id = $1
        ORDER BY cs.position
      `;
      params = [championshipId];
    }
    
    const result = await pool.query(query, params);
    res.json(result.rows);
  } catch (error) {
    console.error('Error fetching championship standings:', error);
    res.status(500).json({ message: 'Failed to fetch championship standings' });
  }
});

// Get championship points breakdown for a driver - PUBLIC ROUTE
router.get('/championships/:championshipId/drivers/:driverId/points', async (req, res) => {
  try {
    const { championshipId, driverId } = req.params;
    
    const result = await pool.query(`
      SELECT 
        cp.rally_id,
        cp.rally_name,
        cp.championship_position,
        cp.rally_points,
        cp.coefficient,
        cp.total_time,
        r.start_date as rally_date
      FROM championship_points cp
      JOIN rallies r ON r.id = cp.rally_id
      WHERE cp.championship_id = $1 AND cp.driver_id = $2
      ORDER BY r.start_date DESC
    `, [championshipId, driverId]);
    
    res.json(result.rows);
  } catch (error) {
    console.error('Error fetching driver championship points:', error);
    res.status(500).json({ message: 'Failed to fetch driver championship points' });
  }
});

// Get all classes in a championship - PUBLIC ROUTE
router.get('/championships/:championshipId/classes', async (req, res) => {
  try {
    const { championshipId } = req.params;
    
    const result = await pool.query(`
      SELECT DISTINCT 
        coc.class as class_name,
        COUNT(DISTINCT coc.driver) AS driver_count,
        COUNT(*) as total_entries
      FROM championship_overall_classification coc
      WHERE coc.championship_id = $1
      GROUP BY coc.class
      ORDER BY coc.class
    `, [championshipId]);
    
    res.json(result.rows);
  } catch (error) {
    console.error('Error fetching championship classes:', error);
    res.status(500).json({ message: 'Failed to fetch championship classes' });
  }
});

// Get championship calendar - PUBLIC ROUTE
router.get('/championships/:championshipId/calendar', async (req, res) => {
  try {
    const { championshipId } = req.params;
    
    const result = await pool.query(`
      SELECT 
        r.*,
        ce.coefficient,
        COUNT(DISTINCT coc.entry_id) AS championship_entry_count,
        COUNT(DISTINCT e.id) AS total_entry_count
      FROM championship_events ce
      JOIN rallies r ON r.id = ce.rally_id
      LEFT JOIN entries e ON e.rally_id = r.id
      LEFT JOIN championship_overall_classification coc ON coc.rally_id = r.id AND coc.championship_id = ce.championship_id
      WHERE ce.championship_id = $1
      GROUP BY r.id, ce.coefficient, ce.created_at
      ORDER BY r.start_date
    `, [championshipId]);
    
    res.json(result.rows);
  } catch (error) {
    console.error('Error fetching championship calendar:', error);
    res.status(500).json({ message: 'Failed to fetch championship calendar' });
  }
});

module.exports = router;
