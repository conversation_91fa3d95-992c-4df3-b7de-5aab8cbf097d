import React from 'react';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  message?: string;
  fullScreen?: boolean;
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ 
  size = 'md', 
  message = 'Loading...', 
  fullScreen = false 
}) => {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-8 w-8',
    lg: 'h-12 w-12'
  };

  const containerClasses = fullScreen 
    ? 'fixed inset-0 bg-gray-50 dark:bg-gray-900 flex items-center justify-center z-50'
    : 'flex items-center justify-center py-12';

  return (
    <div className={containerClasses}>
      <div className="text-center">
        <div className={`inline-block animate-spin rounded-full border-4 border-gray-300 border-t-red-600 ${sizeClasses[size]}`}></div>
        {message && (
          <p className="mt-2 text-gray-700 dark:text-gray-300">{message}</p>
        )}
      </div>
    </div>
  );
};

export default LoadingSpinner;
