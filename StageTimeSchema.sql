-- Ενεργοποίηση της επέκτασης για UUIDs
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- <PERSON>NUM types
CREATE TYPE rally_status AS ENUM ('upcoming', 'running', 'finished');
CREATE TYPE stage_status AS ENUM ('upcoming', 'running', 'finished', 'canceled');
CREATE TYPE entry_status AS ENUM ('entered', 'running', 'finished', 'retired', 'dns', 'dnf', 'dsq');
CREATE TYPE championship_type AS ENUM ('international', 'national', 'local');

-- Users table (admin/editor roles)
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  email TEXT UNIQUE NOT NULL,
  name TEXT NOT NULL,
  role TEXT NOT NULL CHECK (role IN ('admin', 'editor')),
  password_hash TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Persons table (common structure for drivers & codrivers)
CREATE TABLE persons (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  first_name TEXT NOT NULL,
  last_name TEXT NOT NULL,
  nationality TEXT NOT NULL,
  date_of_birth DATE,
  photo_url TEXT,
  bio TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Drivers / Codrivers mapped to persons
CREATE TABLE drivers (
  id UUID PRIMARY KEY REFERENCES persons(id)
);

CREATE TABLE codrivers (
  id UUID PRIMARY KEY REFERENCES persons(id)
);

-- Championships
CREATE TABLE championships (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  year INTEGER NOT NULL,
  type championship_type NOT NULL,
  description TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Rallies
CREATE TABLE rallies (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  country TEXT NOT NULL,
  start_date TIMESTAMPTZ NOT NULL,
  end_date TIMESTAMPTZ NOT NULL,
  status rally_status NOT NULL,
  surface TEXT NOT NULL,
  banner_url TEXT,
  logo_url TEXT,
  views INTEGER DEFAULT 0,
  current_stage TEXT,
  current_stage_start TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Stages
CREATE TABLE stages (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  rally_id UUID NOT NULL REFERENCES rallies(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  number INTEGER NOT NULL,
  length NUMERIC(6,2) NOT NULL,
  surface TEXT NOT NULL,
  start_time TIMESTAMPTZ NOT NULL,
  status stage_status NOT NULL,
  is_power_stage BOOLEAN DEFAULT FALSE,
  is_super_special BOOLEAN DEFAULT FALSE,
  leg_number INTEGER DEFAULT 1,
  day_number INTEGER DEFAULT 1,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
-- Teams
CREATE TABLE teams (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  country TEXT NOT NULL,
  logo_url TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Entries
CREATE TABLE entries (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  rally_id UUID NOT NULL REFERENCES rallies(id) ON DELETE CASCADE,
  driver_id UUID NOT NULL REFERENCES persons(id),
  codriver_id UUID NOT NULL REFERENCES persons(id),
  team_id UUID REFERENCES teams(id),
  car TEXT NOT NULL,
  number INTEGER NOT NULL,
  class TEXT NOT NULL,
  status entry_status NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Results
CREATE TABLE results (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  rally_id UUID NOT NULL REFERENCES rallies(id) ON DELETE CASCADE,
  stage_id UUID NOT NULL REFERENCES stages(id) ON DELETE CASCADE,
  entry_id UUID NOT NULL REFERENCES entries(id) ON DELETE CASCADE,
  time NUMERIC(10, 3) NOT NULL, -- Time in seconds
  is_active BOOLEAN DEFAULT TRUE, -- True if entry was active for this stage
  nominal_time BOOLEAN DEFAULT FALSE, -- True if this is a nominal/theoretical time
  super_rally BOOLEAN DEFAULT FALSE, -- True if entry was under Super Rally rules for this stage
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Penalties
CREATE TABLE penalties (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  rally_id UUID NOT NULL REFERENCES rallies(id) ON DELETE CASCADE,
  stage_id UUID REFERENCES stages(id) ON DELETE CASCADE,
  stage_number INTEGER,
  entry_id UUID NOT NULL REFERENCES entries(id) ON DELETE CASCADE,
  time NUMERIC(10, 3) NOT NULL,
  reason TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Championship events
CREATE TABLE championship_events (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  championship_id UUID NOT NULL REFERENCES championships(id) ON DELETE CASCADE,
  rally_id UUID NOT NULL REFERENCES rallies(id) ON DELETE CASCADE,
  coefficient NUMERIC(3, 1) DEFAULT 1.0,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(championship_id, rally_id)
);

-- Championship eligibility (defines which classes are eligible for each championship)
CREATE TABLE championship_eligibility (
  id SERIAL PRIMARY KEY,
  championship_id UUID NOT NULL REFERENCES championships(id) ON DELETE CASCADE,
  class_pattern VARCHAR NOT NULL,
  description TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(championship_id, class_pattern)
);

-- Entry championships (tracks which championships each entry participates in)
CREATE TABLE entry_championships (
  id SERIAL PRIMARY KEY,
  entry_id UUID NOT NULL REFERENCES entries(id) ON DELETE CASCADE,
  championship_id UUID NOT NULL REFERENCES championships(id) ON DELETE CASCADE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(entry_id, championship_id)
);

-- Power stage points
CREATE TABLE power_stage_points (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  rally_id UUID NOT NULL REFERENCES rallies(id) ON DELETE CASCADE,
  stage_id UUID NOT NULL REFERENCES stages(id) ON DELETE CASCADE,
  entry_id UUID NOT NULL REFERENCES entries(id) ON DELETE CASCADE,
  points INTEGER NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- News
CREATE TABLE news (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  title TEXT NOT NULL,
  content TEXT NOT NULL,
  excerpt TEXT NOT NULL,
  author_id UUID NOT NULL REFERENCES users(id),
  image_url TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes
-- Rallies
CREATE INDEX idx_rallies_status ON rallies(status);

-- Stages
CREATE INDEX idx_stages_rally_id ON stages(rally_id);
CREATE INDEX idx_stages_status ON stages(status);

-- Entries
CREATE INDEX idx_entries_rally_id ON entries(rally_id);
CREATE INDEX idx_entries_driver_id ON entries(driver_id);
CREATE INDEX idx_entries_codriver_id ON entries(codriver_id);
CREATE INDEX idx_entries_team_id ON entries(team_id);
CREATE INDEX idx_entries_class ON entries(class);

-- Results
CREATE INDEX idx_results_rally_id ON results(rally_id);
CREATE INDEX idx_results_stage_id ON results(stage_id);
CREATE INDEX idx_results_entry_id ON results(entry_id);

-- Penalties
CREATE INDEX idx_penalties_rally_id ON penalties(rally_id);
CREATE INDEX idx_penalties_entry_id ON penalties(entry_id);
CREATE INDEX idx_penalties_stage_id ON penalties(stage_id);

-- Championship Events
CREATE INDEX idx_championship_events_championship_id ON championship_events(championship_id);
CREATE INDEX idx_championship_events_rally_id ON championship_events(rally_id);
CREATE INDEX idx_championship_events_championship_rally ON championship_events(championship_id, rally_id);

-- Championship Eligibility
CREATE INDEX idx_championship_eligibility_championship_id ON championship_eligibility(championship_id);
CREATE INDEX idx_championship_eligibility_class_pattern ON championship_eligibility(class_pattern);

-- Entry Championships
CREATE INDEX idx_entry_championships_entry ON entry_championships(entry_id);
CREATE INDEX idx_entry_championships_championship ON entry_championships(championship_id);
CREATE INDEX idx_entry_championships_combined ON entry_championships(entry_id, championship_id);

-- Power Stage Points
CREATE INDEX idx_power_stage_points_rally_id ON power_stage_points(rally_id);
CREATE INDEX idx_power_stage_points_stage_id ON power_stage_points(stage_id);
CREATE INDEX idx_power_stage_points_entry_id ON power_stage_points(entry_id);

-- Entry Status History
CREATE INDEX idx_entry_status_history_entry_id ON entry_status_history(entry_id);
CREATE INDEX idx_entry_status_history_rally_id ON entry_status_history(rally_id);
CREATE INDEX idx_entry_status_history_changed_at ON entry_status_history(changed_at);

-- Shakedowns
CREATE INDEX idx_shakedown_rally_id ON shakedowns(rally_id);
CREATE INDEX idx_shakedown_runs_shakedown_id ON shakedown_runs(shakedown_id);
CREATE INDEX idx_shakedown_runs_entry_id ON shakedown_runs(entry_id);

-- Itinerary Items
CREATE INDEX idx_itinerary_items_rally_id ON itinerary_items(rally_id);
CREATE INDEX idx_itinerary_items_type ON itinerary_items(type);
CREATE INDEX idx_itinerary_items_leg_day ON itinerary_items(rally_id, leg_number, day_number);

-- Results (additional index for is_active)
CREATE INDEX idx_results_is_active ON results(is_active);


-- Entry status history (tracks status changes)
CREATE TABLE entry_status_history (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  entry_id UUID NOT NULL REFERENCES entries(id) ON DELETE CASCADE,
  rally_id UUID NOT NULL REFERENCES rallies(id) ON DELETE CASCADE,
  old_status entry_status,
  new_status entry_status NOT NULL,
  reason TEXT,
  changed_at TIMESTAMPTZ DEFAULT NOW(),
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Shakedown table
CREATE TABLE shakedowns (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  rally_id UUID NOT NULL REFERENCES rallies(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  location TEXT NOT NULL,
  length NUMERIC(6,2) NOT NULL,
  date DATE NOT NULL,
  start_time TIMESTAMPTZ NOT NULL,
  end_time TIMESTAMPTZ NOT NULL,
  max_runs INTEGER DEFAULT 3,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Shakedown runs table - tracks individual runs by each entry
CREATE TABLE shakedown_runs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  shakedown_id UUID NOT NULL REFERENCES shakedowns(id) ON DELETE CASCADE,
  entry_id UUID NOT NULL REFERENCES entries(id) ON DELETE CASCADE,
  run_number INTEGER NOT NULL,
  time NUMERIC(10,3), -- Time in seconds, NULL if not completed
  timestamp TIMESTAMPTZ NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(shakedown_id, entry_id, run_number)
);

-- Itinerary items table - single table approach with type discriminator
CREATE TABLE itinerary_items (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  rally_id UUID NOT NULL REFERENCES rallies(id) ON DELETE CASCADE,
  type TEXT NOT NULL CHECK (type IN (
    'shakedown', 'start', 'service', 'regroup', 'tyre_fitting',
    'remote_service', 'refuel', 'finish', 'podium', 'stage', 'parc_ferme'
  )),
  name TEXT NOT NULL,
  location TEXT,
  start_time TIMESTAMPTZ NOT NULL,
  duration INTEGER, -- in minutes, for service, regroup, etc.
  leg_number INTEGER NOT NULL,
  day_number INTEGER NOT NULL,
  order_in_day INTEGER NOT NULL,
  related_id UUID, -- Can reference stage_id if type is 'stage'
  additional_info JSONB, -- For type-specific data
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Splits (for future intermediate timing data)
CREATE TABLE splits (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  stage_id UUID NOT NULL REFERENCES stages(id) ON DELETE CASCADE,
  entry_id UUID NOT NULL REFERENCES entries(id) ON DELETE CASCADE,
  split_number INTEGER NOT NULL,
  time NUMERIC(10, 3) NOT NULL, -- in seconds
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- View: overall_classification
CREATE VIEW overall_classification AS
SELECT
  e.rally_id,
  e.id AS entry_id,
  e.number,
  e.status,
  pd.first_name || ' ' || pd.last_name AS driver,
  pcd.first_name || ' ' || pcd.last_name AS codriver,
  SUM(r.time + COALESCE(p.time, 0)) AS total_time,
  RANK() OVER (
    PARTITION BY e.rally_id
    ORDER BY SUM(r.time + COALESCE(p.time, 0))
  ) AS position,
  SUM(r.time + COALESCE(p.time, 0)) - MIN(SUM(r.time + COALESCE(p.time, 0))) OVER (PARTITION BY e.rally_id) AS time_diff
FROM entries e
JOIN results r ON r.entry_id = e.id
JOIN persons pd ON pd.id = e.driver_id
JOIN persons pcd ON pcd.id = e.codriver_id
LEFT JOIN penalties p ON p.entry_id = e.id AND p.stage_id = r.stage_id
WHERE e.status NOT IN ('retired', 'dnf', 'dsq')
GROUP BY e.rally_id, e.id, e.number, e.status, pd.first_name, pd.last_name, pcd.first_name, pcd.last_name;

-- View: power_stage_classification
CREATE VIEW power_stage_classification AS
SELECT
  ps.rally_id,
  ps.stage_id,
  e.id AS entry_id,
  e.number,
  pd.first_name || ' ' || pd.last_name AS driver,
  ps.points,
  RANK() OVER (PARTITION BY ps.stage_id ORDER BY ps.points DESC) AS position
FROM power_stage_points ps
JOIN entries e ON e.id = ps.entry_id
JOIN drivers d ON d.id = e.driver_id
JOIN persons pd ON pd.id = d.id;

-- View: current_stage_results
CREATE VIEW current_stage_results AS
SELECT
  r.rally_id,
  s.id AS stage_id,
  e.number,
  pd.first_name || ' ' || pd.last_name AS driver,
  r.time,
  RANK() OVER (PARTITION BY s.id ORDER BY r.time) AS position
FROM rallies ra
JOIN stages s ON s.rally_id = ra.id AND ra.current_stage = s.name
JOIN results r ON r.stage_id = s.id
JOIN entries e ON e.id = r.entry_id
JOIN drivers d ON d.id = e.driver_id
JOIN persons pd ON pd.id = d.id;

-- View: class_classification (ανά κατηγορία)
CREATE VIEW class_classification AS
SELECT
  e.rally_id,
  e.class,
  e.id AS entry_id,
  pd.first_name || ' ' || pd.last_name AS driver,
  SUM(r.time + COALESCE(p.time, 0)) AS total_time,
  RANK() OVER (PARTITION BY e.rally_id, e.class ORDER BY SUM(r.time + COALESCE(p.time, 0))) AS class_position
FROM entries e
JOIN results r ON r.entry_id = e.id
JOIN drivers d ON d.id = e.driver_id
JOIN persons pd ON pd.id = d.id
LEFT JOIN penalties p ON p.entry_id = e.id AND p.stage_id = r.stage_id
GROUP BY e.rally_id, e.class, e.id, pd.first_name, pd.last_name;

-- View: stage_winners
CREATE VIEW stage_winners AS
SELECT
  DISTINCT ON (r.stage_id)
  r.stage_id,
  s.name AS stage_name,
  r.rally_id,
  e.number,
  pd.first_name || ' ' || pd.last_name AS driver,
  r.time
FROM results r
JOIN entries e ON e.id = r.entry_id
JOIN drivers d ON d.id = e.driver_id
JOIN persons pd ON pd.id = d.id
JOIN stages s ON s.id = r.stage_id
ORDER BY r.stage_id, r.time ASC;

-- View: driver_stats (νίκες, podiums, συμμετοχές)
CREATE VIEW driver_stats AS
SELECT
  d.id AS driver_id,
  pd.first_name || ' ' || pd.last_name AS driver,
  COUNT(DISTINCT e.rally_id) AS rallies_participated,
  COUNT(CASE WHEN oc.position = 1 THEN 1 END) AS wins,
  COUNT(CASE WHEN oc.position <= 3 THEN 1 END) AS podiums
FROM drivers d
JOIN persons pd ON pd.id = d.id
JOIN entries e ON e.driver_id = d.id
JOIN overall_classification oc ON oc.entry_id = e.id
GROUP BY d.id, pd.first_name, pd.last_name;

-- View: stage_win_count
CREATE VIEW stage_win_count AS
SELECT
  d.id AS driver_id,
  pd.first_name || ' ' || pd.last_name AS driver,
  COUNT(sw.stage_id) AS stage_wins
FROM drivers d
JOIN persons pd ON pd.id = d.id
JOIN entries e ON e.driver_id = d.id
JOIN stage_winners sw ON sw.number = e.number AND sw.driver = pd.first_name || ' ' || pd.last_name
GROUP BY d.id, pd.first_name, pd.last_name;

-- View: championship_overall_classification
CREATE VIEW championship_overall_classification AS
SELECT
  ce.championship_id,
  c.name AS championship_name,
  e.rally_id,
  r.name AS rally_name,
  e.id AS entry_id,
  e.number,
  pd.first_name || ' ' || pd.last_name AS driver,
  pcd.first_name || ' ' || pcd.last_name AS codriver,
  e.class,
  e.car,
  SUM(res.time + COALESCE(p.time, 0)) AS total_time,
  RANK() OVER (
    PARTITION BY ce.championship_id, e.rally_id
    ORDER BY SUM(res.time + COALESCE(p.time, 0))
  ) AS championship_position,
  SUM(res.time + COALESCE(p.time, 0)) - MIN(SUM(res.time + COALESCE(p.time, 0))) OVER (
    PARTITION BY ce.championship_id, e.rally_id
  ) AS time_diff,
  ce.coefficient
FROM championship_events ce
JOIN championships c ON c.id = ce.championship_id
JOIN rallies r ON r.id = ce.rally_id
JOIN entries e ON e.rally_id = ce.rally_id
JOIN results res ON res.entry_id = e.id
JOIN persons pd ON pd.id = e.driver_id
JOIN persons pcd ON pcd.id = e.codriver_id
LEFT JOIN penalties p ON p.entry_id = e.id AND p.stage_id = res.stage_id
WHERE e.status NOT IN ('retired', 'dnf', 'dsq')
GROUP BY ce.championship_id, c.name, e.rally_id, r.name, e.id, e.number,
         pd.first_name, pd.last_name, pcd.first_name, pcd.last_name, e.class, e.car, ce.coefficient;

-- View: championship_points
CREATE VIEW championship_points AS
SELECT
  ce.championship_id,
  c.name AS championship_name,
  e.rally_id,
  r.name AS rally_name,
  e.driver_id,
  pd.first_name || ' ' || pd.last_name AS driver,
  e.id AS entry_id,
  e.number,
  e.class,
  coc.championship_position,
  coc.total_time,
  CASE
    WHEN coc.championship_position = 1 THEN 25 * ce.coefficient
    WHEN coc.championship_position = 2 THEN 18 * ce.coefficient
    WHEN coc.championship_position = 3 THEN 15 * ce.coefficient
    WHEN coc.championship_position = 4 THEN 12 * ce.coefficient
    WHEN coc.championship_position = 5 THEN 10 * ce.coefficient
    WHEN coc.championship_position = 6 THEN 8 * ce.coefficient
    WHEN coc.championship_position = 7 THEN 6 * ce.coefficient
    WHEN coc.championship_position = 8 THEN 4 * ce.coefficient
    WHEN coc.championship_position = 9 THEN 2 * ce.coefficient
    WHEN coc.championship_position = 10 THEN 1 * ce.coefficient
    ELSE 0
  END AS rally_points,
  ce.coefficient
FROM championship_events ce
JOIN championships c ON c.id = ce.championship_id
JOIN rallies r ON r.id = ce.rally_id
JOIN championship_overall_classification coc ON coc.championship_id = ce.championship_id
  AND coc.rally_id = ce.rally_id
JOIN entries e ON e.id = coc.entry_id
JOIN persons pd ON pd.id = e.driver_id;

-- View: championship_standings
CREATE VIEW championship_standings AS
SELECT
  cp.championship_id,
  cp.championship_name,
  cp.driver_id,
  cp.driver,
  COUNT(DISTINCT cp.rally_id) AS rallies_completed,
  SUM(cp.rally_points) AS total_points,
  RANK() OVER (PARTITION BY cp.championship_id ORDER BY SUM(cp.rally_points) DESC) AS position,
  -- Add power stage points if they exist
  COALESCE(SUM(psp_total.power_stage_points), 0) AS total_power_stage_points,
  SUM(cp.rally_points) + COALESCE(SUM(psp_total.power_stage_points), 0) AS grand_total_points
FROM championship_points cp
LEFT JOIN (
  SELECT
    ce.championship_id,
    e.driver_id,
    SUM(psp.points) AS power_stage_points
  FROM championship_events ce
  JOIN power_stage_points psp ON psp.rally_id = ce.rally_id
  JOIN entries e ON e.id = psp.entry_id
  GROUP BY ce.championship_id, e.driver_id
) psp_total ON psp_total.championship_id = cp.championship_id AND psp_total.driver_id = cp.driver_id
GROUP BY cp.championship_id, cp.championship_name, cp.driver_id, cp.driver;

-- View: championship_class_standings
CREATE VIEW championship_class_standings AS
SELECT
  cp.championship_id,
  cp.championship_name,
  cp.class,
  cp.driver_id,
  cp.driver,
  COUNT(DISTINCT cp.rally_id) AS rallies_completed,
  SUM(cp.rally_points) AS class_points,
  RANK() OVER (
    PARTITION BY cp.championship_id, cp.class
    ORDER BY SUM(cp.rally_points) DESC
  ) AS class_position,
  -- Add power stage points if they exist
  COALESCE(SUM(psp_total.power_stage_points), 0) AS total_power_stage_points,
  SUM(cp.rally_points) + COALESCE(SUM(psp_total.power_stage_points), 0) AS grand_total_points
FROM championship_points cp
LEFT JOIN (
  SELECT
    ce.championship_id,
    e.driver_id,
    e.class,
    SUM(psp.points) AS power_stage_points
  FROM championship_events ce
  JOIN power_stage_points psp ON psp.rally_id = ce.rally_id
  JOIN entries e ON e.id = psp.entry_id
  GROUP BY ce.championship_id, e.driver_id, e.class
) psp_total ON psp_total.championship_id = cp.championship_id
  AND psp_total.driver_id = cp.driver_id
  AND psp_total.class = cp.class
GROUP BY cp.championship_id, cp.championship_name, cp.class, cp.driver_id, cp.driver;

-- View: championship_overall_classification_new (improved version using entry_championships)
CREATE VIEW championship_overall_classification_new AS
SELECT
  ce.championship_id,
  c.name AS championship_name,
  r.id AS rally_id,
  r.name AS rally_name,
  r.start_date AS rally_date,
  e.id AS entry_id,
  e.number,
  pd.first_name || ' ' || pd.last_name AS driver,
  pd.nationality AS driver_nationality,
  pc.first_name || ' ' || pc.last_name AS codriver,
  pc.nationality AS codriver_nationality,
  e.car,
  e.class,
  oc.position AS overall_position,
  ROW_NUMBER() OVER (
    PARTITION BY ce.championship_id, r.id
    ORDER BY oc.position
  ) AS position,
  oc.total_time,
  oc.time_diff,
  CASE
    WHEN ROW_NUMBER() OVER (PARTITION BY ce.championship_id, r.id ORDER BY oc.position) = 1 THEN (25 * ce.coefficient)::INTEGER
    WHEN ROW_NUMBER() OVER (PARTITION BY ce.championship_id, r.id ORDER BY oc.position) = 2 THEN (18 * ce.coefficient)::INTEGER
    WHEN ROW_NUMBER() OVER (PARTITION BY ce.championship_id, r.id ORDER BY oc.position) = 3 THEN (15 * ce.coefficient)::INTEGER
    WHEN ROW_NUMBER() OVER (PARTITION BY ce.championship_id, r.id ORDER BY oc.position) = 4 THEN (12 * ce.coefficient)::INTEGER
    WHEN ROW_NUMBER() OVER (PARTITION BY ce.championship_id, r.id ORDER BY oc.position) = 5 THEN (10 * ce.coefficient)::INTEGER
    WHEN ROW_NUMBER() OVER (PARTITION BY ce.championship_id, r.id ORDER BY oc.position) = 6 THEN (8 * ce.coefficient)::INTEGER
    WHEN ROW_NUMBER() OVER (PARTITION BY ce.championship_id, r.id ORDER BY oc.position) = 7 THEN (6 * ce.coefficient)::INTEGER
    WHEN ROW_NUMBER() OVER (PARTITION BY ce.championship_id, r.id ORDER BY oc.position) = 8 THEN (4 * ce.coefficient)::INTEGER
    WHEN ROW_NUMBER() OVER (PARTITION BY ce.championship_id, r.id ORDER BY oc.position) = 9 THEN (2 * ce.coefficient)::INTEGER
    WHEN ROW_NUMBER() OVER (PARTITION BY ce.championship_id, r.id ORDER BY oc.position) = 10 THEN (1 * ce.coefficient)::INTEGER
    ELSE 0
  END AS championship_points
FROM championship_events ce
JOIN championships c ON ce.championship_id = c.id
JOIN rallies r ON ce.rally_id = r.id
JOIN entries e ON r.id = e.rally_id
JOIN entry_championships ec ON e.id = ec.entry_id AND ec.championship_id = ce.championship_id
JOIN persons pd ON e.driver_id = pd.id
JOIN persons pc ON e.codriver_id = pc.id
JOIN overall_classification oc ON e.id = oc.entry_id
WHERE e.status IN ('finished', 'retired', 'dnf')
ORDER BY ce.championship_id, r.start_date, ROW_NUMBER() OVER (PARTITION BY ce.championship_id, r.id ORDER BY oc.position);

-- View: championship_standings_new (improved version using entry_championships)
CREATE VIEW championship_standings_new AS
SELECT
  championship_id,
  championship_name,
  driver,
  driver_nationality,
  COUNT(DISTINCT rally_id) AS rallies_completed,
  SUM(championship_points) AS total_points,
  RANK() OVER (PARTITION BY championship_id ORDER BY SUM(championship_points) DESC) AS position,
  STRING_AGG(
    rally_name || ' (' || position || 'th, ' || championship_points || 'pts)',
    ', ' ORDER BY rally_date
  ) AS rally_results
FROM championship_overall_classification_new
GROUP BY championship_id, championship_name, driver, driver_nationality
ORDER BY championship_id, SUM(championship_points) DESC;
