-- Cleanup Championship Mess
-- Remove all the unnecessary tables and views we created

-- 1. Drop the views we created (they conflict with existing ones)
-- Need to use CASCADE because of dependencies
DROP VIEW IF EXISTS championship_results CASCADE;
DROP VIEW IF EXISTS championship_calendar CASCADE;

-- 2. Drop the unnecessary tables we created
DROP TABLE IF EXISTS championship_classes;
DROP TABLE IF EXISTS championship_points_system;
DROP TABLE IF EXISTS championship_settings;

-- 3. Verify existing schema is intact
SELECT 'Existing championship views (should still exist):' as status;
SELECT schemaname, viewname
FROM pg_views
WHERE viewname LIKE '%championship%'
ORDER BY viewname;

SELECT 'Existing championship tables (should still exist):' as status;
SELECT tablename
FROM pg_tables
WHERE tablename LIKE '%championship%'
ORDER BY tablename;

-- 4. Check existing championship data
SELECT 'Current championships:' as data;
SELECT id, name, year, type FROM championships ORDER BY year DESC, name;

SELECT 'Current championship events:' as data;
SELECT
    c.name as championship,
    COUNT(ce.rally_id) as rallies_assigned
FROM championships c
LEFT JOIN championship_events ce ON c.id = ce.championship_id
GROUP BY c.id, c.name
ORDER BY rallies_assigned DESC;

-- 5. Test existing views work
SELECT 'Testing existing championship_overall_classification view:' as test;
SELECT
    championship_name,
    COUNT(*) as entries
FROM championship_overall_classification
GROUP BY championship_id, championship_name
ORDER BY entries DESC
LIMIT 5;

SELECT 'Testing existing championship_standings view:' as test;
SELECT
    championship_name,
    position,
    driver,
    total_points
FROM championship_standings
WHERE position <= 3
ORDER BY championship_name, position
LIMIT 10;

SELECT 'Cleanup complete! Your original schema is restored.' as result;
