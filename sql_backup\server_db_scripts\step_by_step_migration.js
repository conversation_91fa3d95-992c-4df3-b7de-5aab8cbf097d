import pool from '../config/db.js';

async function stepByStepMigration() {
  console.log('🔧 Step-by-Step Migration...\n');

  const client = await pool.connect();
  
  try {
    // Step 1: Add is_active column
    console.log('📝 Step 1: Adding is_active column to results table...');
    try {
      await client.query('ALTER TABLE results ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT TRUE');
      console.log('✅ is_active column added successfully');
    } catch (error) {
      console.log('❌ Failed to add is_active column:', error.message);
      throw error;
    }

    // Step 2: Add indexes
    console.log('\n📝 Step 2: Adding indexes...');
    try {
      await client.query('CREATE INDEX IF NOT EXISTS idx_results_is_active ON results(is_active)');
      await client.query('CREATE INDEX IF NOT EXISTS idx_results_entry_active ON results(entry_id, is_active)');
      console.log('✅ Indexes added successfully');
    } catch (error) {
      console.log('❌ Failed to add indexes:', error.message);
      throw error;
    }

    // Step 3: Create entry_status_history table
    console.log('\n📝 Step 3: Creating entry_status_history table...');
    try {
      await client.query(`
        CREATE TABLE IF NOT EXISTS entry_status_history (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          entry_id UUID NOT NULL REFERENCES entries(id) ON DELETE CASCADE,
          rally_id UUID NOT NULL REFERENCES rallies(id) ON DELETE CASCADE,
          stage_id UUID REFERENCES stages(id) ON DELETE CASCADE,
          old_status VARCHAR(20),
          new_status VARCHAR(20) NOT NULL,
          changed_at TIMESTAMPTZ DEFAULT NOW(),
          changed_by UUID REFERENCES users(id),
          reason TEXT,
          created_at TIMESTAMPTZ DEFAULT NOW()
        )
      `);
      console.log('✅ entry_status_history table created successfully');
    } catch (error) {
      console.log('❌ Failed to create entry_status_history table:', error.message);
      throw error;
    }

    // Step 4: Add indexes for history table
    console.log('\n📝 Step 4: Adding indexes for entry_status_history...');
    try {
      await client.query('CREATE INDEX IF NOT EXISTS idx_entry_status_history_entry_id ON entry_status_history(entry_id)');
      await client.query('CREATE INDEX IF NOT EXISTS idx_entry_status_history_rally_id ON entry_status_history(rally_id)');
      await client.query('CREATE INDEX IF NOT EXISTS idx_entry_status_history_stage_id ON entry_status_history(stage_id)');
      await client.query('CREATE INDEX IF NOT EXISTS idx_entry_status_history_changed_at ON entry_status_history(changed_at)');
      console.log('✅ History table indexes added successfully');
    } catch (error) {
      console.log('❌ Failed to add history table indexes:', error.message);
      throw error;
    }

    // Step 5: Update existing results
    console.log('\n📝 Step 5: Updating existing results with is_active values...');
    try {
      const updateResult = await client.query(`
        UPDATE results 
        SET is_active = (
          SELECT CASE 
            WHEN e.status IN ('retired', 'dns', 'dnf', 'dsq') THEN FALSE
            ELSE TRUE
          END
          FROM entries e 
          WHERE e.id = results.entry_id
        )
        WHERE is_active IS NULL OR is_active = TRUE
      `);
      console.log(`✅ Updated ${updateResult.rowCount} results with is_active values`);
    } catch (error) {
      console.log('❌ Failed to update existing results:', error.message);
      throw error;
    }

    // Step 6: Create simple function (without trigger for now)
    console.log('\n📝 Step 6: Creating status tracking function...');
    try {
      await client.query('DROP FUNCTION IF EXISTS track_entry_status_change() CASCADE');
      
      await client.query(`
        CREATE OR REPLACE FUNCTION track_entry_status_change()
        RETURNS TRIGGER AS $$
        BEGIN
          IF OLD.status IS DISTINCT FROM NEW.status THEN
            INSERT INTO entry_status_history (
              entry_id, rally_id, old_status, new_status, reason
            ) VALUES (
              NEW.id, NEW.rally_id, OLD.status, NEW.status,
              'Status changed from ' || COALESCE(OLD.status, 'null') || ' to ' || NEW.status
            );
          END IF;
          RETURN NEW;
        END;
        $$ LANGUAGE plpgsql
      `);
      console.log('✅ Function created successfully');
    } catch (error) {
      console.log('❌ Failed to create function:', error.message);
      console.log('   This might be due to PostgreSQL version or permissions');
      console.log('   Continuing without trigger for now...');
    }

    // Step 7: Create trigger
    console.log('\n📝 Step 7: Creating trigger...');
    try {
      await client.query('DROP TRIGGER IF EXISTS trigger_track_entry_status_change ON entries');
      await client.query(`
        CREATE TRIGGER trigger_track_entry_status_change
          AFTER UPDATE ON entries
          FOR EACH ROW
          EXECUTE FUNCTION track_entry_status_change()
      `);
      console.log('✅ Trigger created successfully');
    } catch (error) {
      console.log('❌ Failed to create trigger:', error.message);
      console.log('   You can add the trigger manually later if needed');
    }

    console.log('\n🎉 Step-by-step migration completed!');
    
    // Verification
    console.log('\n🔍 Verification:');
    const columnCheck = await client.query(`
      SELECT column_name FROM information_schema.columns 
      WHERE table_name = 'results' AND column_name = 'is_active'
    `);
    console.log(`   is_active column: ${columnCheck.rows.length > 0 ? '✅' : '❌'}`);
    
    const tableCheck = await client.query(`
      SELECT table_name FROM information_schema.tables 
      WHERE table_name = 'entry_status_history'
    `);
    console.log(`   entry_status_history table: ${tableCheck.rows.length > 0 ? '✅' : '❌'}`);

  } catch (error) {
    console.error('❌ Migration failed at step:', error.message);
    throw error;
  } finally {
    client.release();
  }
}

// Run the migration
stepByStepMigration()
  .then(() => {
    console.log('\n✨ Migration completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n💥 Migration failed:', error.message);
    process.exit(1);
  });
