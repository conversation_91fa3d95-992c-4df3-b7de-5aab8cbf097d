import pool from '../config/db.js';

async function testActivityTracking() {
  console.log('🧪 Testing Entry Activity Tracking Implementation...\n');

  try {
    // Test 1: Check if is_active column exists in results table
    console.log('1. Checking if is_active column exists in results table...');
    const columnCheck = await pool.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns
      WHERE table_name = 'results' AND column_name = 'is_active'
    `);

    if (columnCheck.rows.length > 0) {
      console.log('✅ is_active column exists:', columnCheck.rows[0]);
    } else {
      console.log('❌ is_active column does not exist - run migration first');
      return;
    }

    // Test 2: Check if entry_status_history table exists
    console.log('\n2. Checking if entry_status_history table exists...');
    const tableCheck = await pool.query(`
      SELECT table_name
      FROM information_schema.tables
      WHERE table_name = 'entry_status_history'
    `);

    if (tableCheck.rows.length > 0) {
      console.log('✅ entry_status_history table exists');
    } else {
      console.log('❌ entry_status_history table does not exist');
      return;
    }

    // Test 3: Check if views exist
    console.log('\n3. Checking if views exist...');
    const viewsCheck = await pool.query(`
      SELECT table_name
      FROM information_schema.views
      WHERE table_name IN ('active_entries', 'retired_entries')
    `);

    console.log('✅ Views found:', viewsCheck.rows.map(r => r.table_name));

    // Test 4: Check if trigger exists
    console.log('\n4. Checking if trigger exists...');
    const triggerCheck = await pool.query(`
      SELECT trigger_name, event_manipulation, event_object_table
      FROM information_schema.triggers
      WHERE trigger_name = 'trigger_track_entry_status_change'
    `);

    if (triggerCheck.rows.length > 0) {
      console.log('✅ Trigger exists:', triggerCheck.rows[0]);
    } else {
      console.log('❌ Trigger does not exist');
    }

    // Test 5: Sample data check
    console.log('\n5. Checking sample data...');

    // Check total entries
    const entriesCount = await pool.query('SELECT COUNT(*) as count FROM entries');
    console.log(`📊 Total entries: ${entriesCount.rows[0].count}`);

    // Check results with is_active data
    const resultsCount = await pool.query(`
      SELECT
        COUNT(*) as total_results,
        COUNT(CASE WHEN is_active = true THEN 1 END) as active_results,
        COUNT(CASE WHEN is_active = false THEN 1 END) as inactive_results
      FROM results
    `);
    console.log('📊 Results breakdown:', resultsCount.rows[0]);

    // Check entry status distribution
    const statusDistribution = await pool.query(`
      SELECT
        status,
        COUNT(*) as count
      FROM entries
      GROUP BY status
      ORDER BY count DESC
    `);
    console.log('📊 Entry status distribution:');
    statusDistribution.rows.forEach(row => {
      console.log(`   ${row.status}: ${row.count}`);
    });

    console.log('\n✅ Activity tracking implementation test completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  } finally {
    await pool.end();
  }
}

// Run the test
testActivityTracking();
