import pool from './server/config/db.js';

async function debugPapadimitriouPosition() {
  try {
    console.log('=== Debugging <PERSON><PERSON><PERSON> Position Issue ===');
    
    // Check overall classification for Papadimitriou
    const overallResults = await pool.query(`
      SELECT 
        r.name as rally_name,
        e.number,
        CONCAT(pd.first_name, ' ', pd.last_name) as driver,
        oc.position,
        oc.total_time,
        oc.time_diff,
        e.status
      FROM entries e
      JOIN persons pd ON e.driver_id = pd.id
      JOIN rallies r ON e.rally_id = r.id
      LEFT JOIN overall_classification oc ON e.id = oc.entry_id
      WHERE pd.first_name = '<PERSON><PERSON><PERSON>' AND pd.last_name = 'Papadimitriou'
      ORDER BY r.start_date DESC
    `);
    
    console.log('Ioannis Papadimitriou overall results:');
    overallResults.rows.forEach(row => {
      console.log(`  ${row.rally_name}: #${row.number} - Position ${row.position || 'NULL'} - Time: ${row.total_time || 'NULL'} - Status: ${row.status}`);
    });
    
    // Check championship results for Papadimitriou
    const championshipResults = await pool.query(`
      SELECT 
        coc.rally_name,
        coc.championship_name,
        coc.driver,
        coc.position,
        coc.total_time,
        coc.championship_points
      FROM championship_overall_classification_new coc
      WHERE coc.driver LIKE '%Papadimitriou%'
      ORDER BY coc.rally_name, coc.championship_name
    `);
    
    console.log('\nIoannis Papadimitriou championship results:');
    championshipResults.rows.forEach(row => {
      console.log(`  ${row.rally_name} - ${row.championship_name}: Position ${row.position} (${row.championship_points} pts) - Time: ${row.total_time}`);
    });
    
    // Check the top 10 overall classification for Rally Stereas Elladas 2025
    const topResults = await pool.query(`
      SELECT 
        oc.position,
        e.number,
        CONCAT(pd.first_name, ' ', pd.last_name) as driver,
        oc.total_time,
        e.status
      FROM overall_classification oc
      JOIN entries e ON oc.entry_id = e.id
      JOIN persons pd ON e.driver_id = pd.id
      JOIN rallies r ON e.rally_id = r.id
      WHERE r.name LIKE '%Stereas Elladas 2025%'
      ORDER BY oc.position
      LIMIT 20
    `);
    
    console.log('\nTop 20 overall classification for Rally Stereas Elladas 2025:');
    topResults.rows.forEach(row => {
      console.log(`  ${row.position}. #${row.number} ${row.driver} - ${row.total_time} - ${row.status}`);
    });
    
    // Check if there are multiple rallies and which one Papadimitriou won
    const allRallies = await pool.query(`
      SELECT 
        r.id,
        r.name,
        r.start_date,
        COUNT(e.id) as entry_count
      FROM rallies r
      LEFT JOIN entries e ON r.id = e.rally_id
      WHERE r.name LIKE '%2025%'
      GROUP BY r.id, r.name, r.start_date
      ORDER BY r.start_date DESC
    `);
    
    console.log('\nAll 2025 rallies:');
    allRallies.rows.forEach(row => {
      console.log(`  ${row.name} (${row.start_date}) - ${row.entry_count} entries - ID: ${row.id}`);
    });
    
    // Check if Papadimitriou won a different rally
    const papadimitriouWins = await pool.query(`
      SELECT 
        r.name as rally_name,
        e.number,
        CONCAT(pd.first_name, ' ', pd.last_name) as driver,
        oc.position,
        oc.total_time
      FROM overall_classification oc
      JOIN entries e ON oc.entry_id = e.id
      JOIN persons pd ON e.driver_id = pd.id
      JOIN rallies r ON e.rally_id = r.id
      WHERE pd.first_name = 'Ioannis' AND pd.last_name = 'Papadimitriou'
      AND oc.position = 1
      ORDER BY r.start_date DESC
    `);
    
    console.log('\nPapadimitriou wins (position 1):');
    papadimitriouWins.rows.forEach(row => {
      console.log(`  ${row.rally_name}: #${row.number} - 1st place - Time: ${row.total_time}`);
    });
    
    process.exit(0);
  } catch (error) {
    console.error('Error debugging Papadimitriou position:', error);
    process.exit(1);
  }
}

debugPapadimitriouPosition();
