# Server-Side Changes for Entries Management

To properly handle the case where a person can be both a driver and a codriver in different rallies, we need to make the following changes to the server-side code:

## 1. Modify the POST endpoint in drivers.js

Add a new case to handle adding an existing person to the drivers table:

```javascript
// Create driver (person + driver)
router.post('/', verifyToken, verifyAdmin, async (req, res) => {
  try {
    // Check if we're adding an existing person as a driver
    if (req.body.id) {
      const personId = req.body.id;
      
      // Check if the person exists
      const personCheck = await pool.query('SELECT * FROM persons WHERE id = $1', [personId]);
      if (personCheck.rows.length === 0) {
        return res.status(404).json({ message: 'Person not found' });
      }
      
      // Check if the person is already a driver
      const driverCheck = await pool.query('SELECT * FROM drivers WHERE id = $1', [personId]);
      if (driverCheck.rows.length > 0) {
        return res.status(400).json({ message: 'Person is already a driver' });
      }
      
      // Add the person as a driver
      await pool.query('INSERT INTO drivers (id) VALUES ($1)', [personId]);
      
      // Return the driver with person details
      const driver = await pool.query(`
        SELECT d.id, p.first_name, p.last_name, p.nationality, p.date_of_birth, p.photo_url, p.bio, p.created_at, p.updated_at
        FROM drivers d
        JOIN persons p ON d.id = p.id
        WHERE d.id = $1
      `, [personId]);
      
      return res.status(201).json(driver.rows[0]);
    }
    
    // Original code for creating a new person and driver
    const { first_name, last_name, nationality, date_of_birth, photo_url, bio } = req.body;
    const personResult = await pool.query(
      `INSERT INTO persons (first_name, last_name, nationality, date_of_birth, photo_url, bio) VALUES ($1, $2, $3, $4, $5, $6) RETURNING id`,
      [first_name, last_name, nationality, date_of_birth, photo_url, bio]
    );
    const personId = personResult.rows[0].id;
    await pool.query(`INSERT INTO drivers (id) VALUES ($1)`, [personId]);
    const driver = await pool.query(`
      SELECT d.id, p.first_name, p.last_name, p.nationality, p.date_of_birth, p.photo_url, p.bio, p.created_at, p.updated_at
      FROM drivers d
      JOIN persons p ON d.id = p.id
      WHERE d.id = $1
    `, [personId]);
    res.status(201).json(driver.rows[0]);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Failed to add driver' });
  }
});
```

## 2. Modify the POST endpoint in codrivers.js

Add a new case to handle adding an existing person to the codrivers table:

```javascript
// Create codriver (person + codriver)
router.post('/', verifyToken, verifyAdmin, async (req, res) => {
  try {
    // Check if we're adding an existing person as a codriver
    if (req.body.id) {
      const personId = req.body.id;
      
      // Check if the person exists
      const personCheck = await pool.query('SELECT * FROM persons WHERE id = $1', [personId]);
      if (personCheck.rows.length === 0) {
        return res.status(404).json({ message: 'Person not found' });
      }
      
      // Check if the person is already a codriver
      const codriverCheck = await pool.query('SELECT * FROM codrivers WHERE id = $1', [personId]);
      if (codriverCheck.rows.length > 0) {
        return res.status(400).json({ message: 'Person is already a codriver' });
      }
      
      // Add the person as a codriver
      await pool.query('INSERT INTO codrivers (id) VALUES ($1)', [personId]);
      
      // Return the codriver with person details
      const codriver = await pool.query(`
        SELECT c.id, p.first_name, p.last_name, p.nationality, p.date_of_birth, p.photo_url, p.bio, p.created_at, p.updated_at
        FROM codrivers c
        JOIN persons p ON c.id = p.id
        WHERE c.id = $1
      `, [personId]);
      
      return res.status(201).json(codriver.rows[0]);
    }
    
    // Original code for creating a new person and codriver
    const { first_name, last_name, nationality, date_of_birth, photo_url, bio } = req.body;
    const personResult = await pool.query(
      `INSERT INTO persons (first_name, last_name, nationality, date_of_birth, photo_url, bio) VALUES ($1, $2, $3, $4, $5, $6) RETURNING id`,
      [first_name, last_name, nationality, date_of_birth, photo_url, bio]
    );
    const personId = personResult.rows[0].id;
    await pool.query(`INSERT INTO codrivers (id) VALUES ($1)`, [personId]);
    const codriver = await pool.query(`
      SELECT c.id, p.first_name, p.last_name, p.nationality, p.date_of_birth, p.photo_url, p.bio, p.created_at, p.updated_at
      FROM codrivers c
      JOIN persons p ON c.id = p.id
      WHERE c.id = $1
    `, [personId]);
    res.status(201).json(codriver.rows[0]);
  } catch (error) {
    res.status(500).json({ message: 'Failed to add codriver' });
  }
});
```

## 3. Modify the POST endpoint in entries.js

Update the entries creation endpoint to automatically add the driver and codriver to their respective tables if they're not already there:

```javascript
// Create entry
router.post('/', verifyToken, verifyAdmin, async (req, res) => {
  try {
    const { rally_id, driver_id, codriver_id, team_id, car, number, class: entryClass, status } = req.body;
    
    // Check if the driver exists in the drivers table
    const driverCheck = await pool.query('SELECT * FROM drivers WHERE id = $1', [driver_id]);
    if (driverCheck.rows.length === 0) {
      // Check if the person exists
      const personCheck = await pool.query('SELECT * FROM persons WHERE id = $1', [driver_id]);
      if (personCheck.rows.length === 0) {
        return res.status(404).json({ message: 'Driver person not found' });
      }
      
      // Add the person as a driver
      await pool.query('INSERT INTO drivers (id) VALUES ($1)', [driver_id]);
    }
    
    // Check if the codriver exists in the codrivers table
    const codriverCheck = await pool.query('SELECT * FROM codrivers WHERE id = $1', [codriver_id]);
    if (codriverCheck.rows.length === 0) {
      // Check if the person exists
      const personCheck = await pool.query('SELECT * FROM persons WHERE id = $1', [codriver_id]);
      if (personCheck.rows.length === 0) {
        return res.status(404).json({ message: 'Codriver person not found' });
      }
      
      // Add the person as a codriver
      await pool.query('INSERT INTO codrivers (id) VALUES ($1)', [codriver_id]);
    }
    
    // Create the entry
    const result = await pool.query(
      `INSERT INTO entries (rally_id, driver_id, codriver_id, team_id, car, number, class, status) VALUES ($1, $2, $3, $4, $5, $6, $7, $8) RETURNING *`,
      [rally_id, driver_id, codriver_id, team_id, car, number, entryClass, status]
    );
    
    // Fetch with joined details for response
    const entryId = result.rows[0].id;
    const details = await pool.query(`
      SELECT e.*,
        pd.first_name AS driver_first_name, pd.last_name AS driver_last_name, pd.nationality AS driver_nationality,
        pcd.first_name AS codriver_first_name, pcd.last_name AS codriver_last_name, pcd.nationality AS codriver_nationality,
        t.name AS team_name
      FROM entries e
      JOIN persons pd ON pd.id = e.driver_id
      JOIN persons pcd ON pcd.id = e.codriver_id
      LEFT JOIN teams t ON t.id = e.team_id
      WHERE e.id = $1
    `, [entryId]);
    
    res.status(201).json(details.rows[0]);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Failed to add entry' });
  }
});
```

## Implementation Steps

1. Make the changes to the server-side code as described above.
2. Test the changes by creating entries with persons who are not yet drivers or codrivers.
3. Verify that the persons are automatically added to the drivers and codrivers tables as needed.

These changes will ensure that a person can be both a driver and a codriver in different rallies, and the system will automatically handle the necessary database entries.
