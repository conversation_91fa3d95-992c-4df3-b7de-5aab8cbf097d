import React from 'react';
import { useParams } from 'react-router-dom';
import { Clock } from 'lucide-react';
import ItineraryManager from '../../../../components/admin/itinerary-manager';

const ItineraryTab: React.FC = () => {
  const { id } = useParams<{ id: string }>();

  if (!id) {
    return (
      <div className="bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-200 p-4 rounded-lg">
        Rally ID is missing
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
          <Clock className="mr-2 h-6 w-6 text-red-600" />
          Itinerary Management
        </h2>
      </div>
      
      <p className="text-gray-600 dark:text-gray-400">
        Manage the rally itinerary including stages, service parks, and other schedule items.
      </p>
      
      <ItineraryManager rallyId={id} />
    </div>
  );
};

export default ItineraryTab;
