import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Trophy, Twitter, Facebook, Instagram, Github, Mail } from 'lucide-react';

const Footer: React.FC = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-gray-900 text-gray-100 pt-12 pb-8">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div className="col-span-1 md:col-span-1">
            <Link to="/" className="flex items-center mb-4">
              <Trophy className="w-8 h-8 text-red-600" />
              <span className="ml-2 text-xl font-bold text-white">
                StageTime<span className="text-red-600">.gr</span>
              </span>
            </Link>
            <p className="text-sm text-gray-400 mb-4">
              Your ultimate rally tracking and results platform. Follow live rally events, check driver statistics, and explore championship standings with StageTime.gr.
            </p>
            <div className="flex space-x-4">
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <Twitter className="w-5 h-5" />
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <Facebook className="w-5 h-5" />
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <Instagram className="w-5 h-5" />
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <Github className="w-5 h-5" />
              </a>
            </div>
          </div>

          <div className="col-span-1">
            <h3 className="text-lg font-semibold mb-4">Quick Links</h3>
            <ul className="space-y-2">
              <li>
                <Link to="/" className="text-gray-400 hover:text-white transition-colors">Home</Link>
              </li>
              <li>
                <Link to="/rallies" className="text-gray-400 hover:text-white transition-colors">Rallies</Link>
              </li>
              <li>
                <Link to="/drivers" className="text-gray-400 hover:text-white transition-colors">Drivers</Link>
              </li>
              <li>
                <Link to="/championships" className="text-gray-400 hover:text-white transition-colors">Championships</Link>
              </li>
              <li>
                <Link to="/live" className="text-gray-400 hover:text-white transition-colors">Live Timing</Link>
              </li>
            </ul>
          </div>

          <div className="col-span-1">
            <h3 className="text-lg font-semibold mb-4">Championships</h3>
            <ul className="space-y-2">
              <li>
                <Link to="/championships/c1" className="text-gray-400 hover:text-white transition-colors">WRC</Link>
              </li>
              <li>
                <Link to="/championships/c2" className="text-gray-400 hover:text-white transition-colors">ERC</Link>
              </li>
              <li>
                <a href="#" className="text-gray-400 hover:text-white transition-colors">WRC2</a>
              </li>
              <li>
                <a href="#" className="text-gray-400 hover:text-white transition-colors">WRC3</a>
              </li>
              <li>
                <a href="#" className="text-gray-400 hover:text-white transition-colors">National Championships</a>
              </li>
            </ul>
          </div>

          <div className="col-span-1">
            <h3 className="text-lg font-semibold mb-4">Contact</h3>
            <div className="flex items-center mb-3">
              <Mail className="w-5 h-5 text-gray-400 mr-2" />
              <a href="mailto:<EMAIL>" className="text-gray-400 hover:text-white transition-colors">
                <EMAIL>
              </a>
            </div>
            <p className="text-sm text-gray-400">
              Have a question or feedback? We'd love to hear from you.
            </p>
            <div className="mt-4">
              <Link to="/admin" className="text-gray-400 hover:text-white transition-colors text-sm">Admin Panel</Link>
            </div>
          </div>
        </div>

        <div className="border-t border-gray-800 mt-8 pt-8 text-sm text-gray-500 flex flex-col md:flex-row justify-between">
          <p>© {currentYear} StageTime.gr. All rights reserved.</p>
          <div className="flex space-x-4 mt-4 md:mt-0">
            <a href="#" className="hover:text-white transition-colors">Privacy Policy</a>
            <a href="#" className="hover:text-white transition-colors">Terms of Service</a>
            <a href="#" className="hover:text-white transition-colors">Cookie Policy</a>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;