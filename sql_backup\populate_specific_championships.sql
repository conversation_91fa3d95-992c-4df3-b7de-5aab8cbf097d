-- Populate Specific Championships Based on Your Data
-- Targeted approach using your actual class structure

-- 1. Show current status
SELECT 'Current Championship Status:' as status;
SELECT 
    c.name as championship,
    COUNT(ce.rally_id) as rallies_assigned
FROM championships c
LEFT JOIN championship_events ce ON c.id = ce.championship_id
GROUP BY c.id, c.name
ORDER BY rallies_assigned DESC, c.name;

-- 2. Find unassigned rallies with their classes
SELECT 'Unassigned rallies with classes:' as unassigned;
SELECT 
    r.id,
    r.name,
    r.country,
    r.start_date,
    COUNT(e.id) as total_entries,
    STRING_AGG(DISTINCT e.class, ', ') as classes_available
FROM rallies r
LEFT JOIN entries e ON r.id = e.rally_id
WHERE r.id NOT IN (SELECT DISTINCT rally_id FROM championship_events WHERE rally_id IS NOT NULL)
AND e.class IS NOT NULL
GROUP BY r.id, r.name, r.country, r.start_date
ORDER BY r.start_date DESC;

-- 3. Populate championships based on your class structure

-- Greece Championship: Add rallies with C-classes and F2 classes
INSERT INTO championship_events (championship_id, rally_id, coefficient)
SELECT DISTINCT 
    '55a003a9-66ff-4a37-b11d-2e14df10bae3'::uuid,
    r.id,
    1.0
FROM rallies r
JOIN entries e ON r.id = e.rally_id
WHERE (
    e.class LIKE '%C1%' OR e.class LIKE '%C2%' OR e.class LIKE '%C3%' 
    OR e.class LIKE '%C4%' OR e.class LIKE '%C5%' OR e.class LIKE '%C6%'
    OR e.class LIKE '%F2%' OR e.class LIKE '%N%'
)
AND r.id NOT IN (
    SELECT rally_id FROM championship_events 
    WHERE championship_id = '55a003a9-66ff-4a37-b11d-2e14df10bae3'
)
ON CONFLICT DO NOTHING;

-- Historic Championship: Add rallies with numeric classes (2, 3, 4)
INSERT INTO championship_events (championship_id, rally_id, coefficient)
SELECT DISTINCT 
    'cc1e9147-fdb3-4483-aad3-9e27795eff17'::uuid,
    r.id,
    1.0
FROM rallies r
JOIN entries e ON r.id = e.rally_id
WHERE e.class IN ('2', '3', '4')
AND r.id NOT IN (
    SELECT rally_id FROM championship_events 
    WHERE championship_id = 'cc1e9147-fdb3-4483-aad3-9e27795eff17'
)
ON CONFLICT DO NOTHING;

-- Historic Gravel Cup: Same as Historic
INSERT INTO championship_events (championship_id, rally_id, coefficient)
SELECT DISTINCT 
    'aa934657-d4f5-49bb-80d5-b0be90e72b4c'::uuid,
    r.id,
    1.0
FROM rallies r
JOIN entries e ON r.id = e.rally_id
WHERE e.class IN ('2', '3', '4')
AND r.id NOT IN (
    SELECT rally_id FROM championship_events 
    WHERE championship_id = 'aa934657-d4f5-49bb-80d5-b0be90e72b4c'
)
ON CONFLICT DO NOTHING;

-- Greece Asphalt Championship: Add rallies with asphalt-specific classes
INSERT INTO championship_events (championship_id, rally_id, coefficient)
SELECT DISTINCT 
    'acf8a5b6-3824-4b98-8370-7c12badcae8c'::uuid,
    r.id,
    1.0
FROM rallies r
JOIN entries e ON r.id = e.rally_id
WHERE (
    e.class LIKE '%C1%' OR e.class LIKE '%C2%' OR e.class LIKE '%C3%' 
    OR e.class LIKE '%C4%' OR e.class LIKE '%C5%' OR e.class LIKE '%C6%'
    OR e.class LIKE '%F2%'
)
AND r.surface = 'asphalt'
AND r.id NOT IN (
    SELECT rally_id FROM championship_events 
    WHERE championship_id = 'acf8a5b6-3824-4b98-8370-7c12badcae8c'
)
ON CONFLICT DO NOTHING;

-- Rally3 Championship: Add rallies with RC1, RC2, or specific Rally3 cars
INSERT INTO championship_events (championship_id, rally_id, coefficient)
SELECT DISTINCT 
    '895f159e-f147-439b-b5ab-04972033a7bb'::uuid,
    r.id,
    1.0
FROM rallies r
JOIN entries e ON r.id = e.rally_id
WHERE (
    e.class LIKE '%RC1%' OR e.class LIKE '%RC2%' OR 
    e.class LIKE '%Rally3%' OR e.class LIKE '%R3%' OR
    e.class LIKE '%C2%' -- Rally3 cars are often in C2
)
AND r.id NOT IN (
    SELECT rally_id FROM championship_events 
    WHERE championship_id = '895f159e-f147-439b-b5ab-04972033a7bb'
)
ON CONFLICT DO NOTHING;

-- Rally3 Asphalt Championship: Same as Rally3 but asphalt only
INSERT INTO championship_events (championship_id, rally_id, coefficient)
SELECT DISTINCT 
    '0f822c2c-e5e9-430c-b0e5-b33bb3387ab4'::uuid,
    r.id,
    1.0
FROM rallies r
JOIN entries e ON r.id = e.rally_id
WHERE (
    e.class LIKE '%RC1%' OR e.class LIKE '%RC2%' OR 
    e.class LIKE '%Rally3%' OR e.class LIKE '%R3%' OR
    e.class LIKE '%C2%'
)
AND r.surface = 'asphalt'
AND r.id NOT IN (
    SELECT rally_id FROM championship_events 
    WHERE championship_id = '0f822c2c-e5e9-430c-b0e5-b33bb3387ab4'
)
ON CONFLICT DO NOTHING;

-- 4. Show results after population
SELECT 'Championship Population Results:' as results;
SELECT 
    c.name as championship,
    COUNT(DISTINCT ce.rally_id) as rallies_assigned,
    COUNT(DISTINCT r.country) as countries_covered
FROM championships c
LEFT JOIN championship_events ce ON c.id = ce.championship_id
LEFT JOIN rallies r ON ce.rally_id = r.id
GROUP BY c.id, c.name
ORDER BY rallies_assigned DESC, c.name;

-- 5. Show rally details per championship
SELECT 'Rally Details per Championship:' as details;
SELECT 
    c.name as championship,
    r.name as rally_name,
    r.country,
    r.start_date,
    r.surface,
    ce.coefficient,
    COUNT(e.id) as total_entries
FROM championships c
JOIN championship_events ce ON c.id = ce.championship_id
JOIN rallies r ON ce.rally_id = r.id
LEFT JOIN entries e ON e.rally_id = r.id
GROUP BY c.id, c.name, r.id, r.name, r.country, r.start_date, r.surface, ce.coefficient
ORDER BY c.name, r.start_date;

-- 6. Test championship views with populated data
SELECT 'Testing championship_standings (Top 3 per championship):' as test;
SELECT 
    championship_name,
    position,
    driver,
    rallies_completed,
    total_points,
    total_power_stage_points,
    grand_total_points
FROM championship_standings
WHERE position <= 3
ORDER BY championship_name, position
LIMIT 20;

-- 7. Show class distribution per championship
SELECT 'Class Distribution per Championship:' as distribution;
SELECT 
    coc.championship_name,
    coc.class,
    COUNT(*) as entry_count,
    COUNT(DISTINCT coc.driver) as unique_drivers
FROM championship_overall_classification coc
GROUP BY coc.championship_id, coc.championship_name, coc.class
ORDER BY coc.championship_name, entry_count DESC;

SELECT 'Championship population complete!' as result;
