-- Create function to track entry status changes
CREATE OR <PERSON><PERSON><PERSON>CE FUNCTION track_entry_status_change()
RETURNS TRIGGER AS $$
BEGIN
  -- Only track if status actually changed
  IF OLD.status IS DISTINCT FROM NEW.status THEN
    INSERT INTO entry_status_history (
      entry_id,
      rally_id,
      old_status,
      new_status,
      reason
    ) VALUES (
      NEW.id,
      NEW.rally_id,
      OLD.status,
      NEW.status,
      CASE
        WHEN NEW.status IN ('retired', 'dns', 'dnf', 'dsq') THEN 'Entry marked as ' || NEW.status
        ELSE 'Status updated to ' || NEW.status
      END
    );
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- <PERSON>reate trigger to automatically track entry status changes
DROP TRIGGER IF EXISTS trigger_track_entry_status_change ON entries;
CREATE TRIGGER trigger_track_entry_status_change
  AFTER UPDATE ON entries
  FOR EACH ROW
  EXECUTE FUNCTION track_entry_status_change();
