-- Restore Original Championship Assignments
-- This will undo any changes made by the population scripts

-- 1. Clear all championship events (we'll restore the original ones)
DELETE FROM championship_events;

-- 2. Restore your original championship assignments based on what you showed me
-- These are the EXACT assignments you had before:

-- Greece Championship
INSERT INTO championship_events (championship_id, rally_id, coefficient) VALUES
('55a003a9-66ff-4a37-b11d-2e14df10bae3'::uuid, 
 (SELECT id FROM rallies WHERE name = 'Olympiako Rally 2025'), 1.0),
('55a003a9-66ff-4a37-b11d-2e14df10bae3'::uuid, 
 (SELECT id FROM rallies WHERE name = 'Rally Kritis 2025'), 1.0),
('55a003a9-66ff-4a37-b11d-2e14df10bae3'::uuid, 
 (SELECT id FROM rallies WHERE name = 'Rally Stereas Elladas 2025'), 1.0);

-- Historic Championship
INSERT INTO championship_events (championship_id, rally_id, coefficient) VALUES
('cc1e9147-fdb3-4483-aad3-9e27795eff17'::uuid, 
 (SELECT id FROM rallies WHERE name = 'Rally Stereas Elladas 2025'), 1.0);

-- Historic Gravel Cup
INSERT INTO championship_events (championship_id, rally_id, coefficient) VALUES
('aa934657-d4f5-49bb-80d5-b0be90e72b4c'::uuid, 
 (SELECT id FROM rallies WHERE name = 'Rally Stereas Elladas 2025'), 1.0);

-- Rally3 Championship
INSERT INTO championship_events (championship_id, rally_id, coefficient) VALUES
('895f159e-f147-439b-b5ab-04972033a7bb'::uuid, 
 (SELECT id FROM rallies WHERE name = 'Olympiako Rally 2025'), 1.0),
('895f159e-f147-439b-b5ab-04972033a7bb'::uuid, 
 (SELECT id FROM rallies WHERE name = 'Rally Stereas Elladas 2025'), 1.0);

-- 3. Verify the restoration
SELECT 'Restored Championship Events:' as status;
SELECT 
    c.name as championship,
    r.name as rally,
    r.start_date,
    ce.coefficient
FROM championship_events ce
JOIN championships c ON c.id = ce.championship_id
JOIN rallies r ON r.id = ce.rally_id
ORDER BY c.name, r.start_date;

-- 4. Test that championship views work correctly
SELECT 'Testing championship_standings after restoration:' as test;
SELECT 
    championship_name,
    position,
    driver,
    rallies_completed,
    total_points,
    total_power_stage_points,
    grand_total_points
FROM championship_standings
WHERE position <= 3
ORDER BY championship_name, position
LIMIT 10;

SELECT 'Original championship assignments restored!' as result;
