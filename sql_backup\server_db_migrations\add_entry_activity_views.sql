-- Create views for active and retired entries

-- Drop existing views if they exist
DROP VIEW IF EXISTS active_entries;
DROP VIEW IF EXISTS retired_entries;

-- Create view for active entries in current rally
CREATE VIEW active_entries AS
SELECT 
  e.*,
  pd.first_name AS driver_first_name,
  pd.last_name AS driver_last_name,
  pd.nationality AS driver_nationality,
  pcd.first_name AS codriver_first_name,
  pcd.last_name AS codriver_last_name,
  pcd.nationality AS codriver_nationality,
  t.name AS team_name
FROM entries e
JOIN persons pd ON pd.id = e.driver_id
JOIN persons pcd ON pcd.id = e.codriver_id
LEFT JOIN teams t ON t.id = e.team_id
WHERE e.status NOT IN ('retired', 'dns', 'dnf', 'dsq');

-- Create view for retired entries
CREATE VIEW retired_entries AS
SELECT 
  e.*,
  pd.first_name AS driver_first_name,
  pd.last_name AS driver_last_name,
  pd.nationality AS driver_nationality,
  pcd.first_name AS codriver_first_name,
  pcd.last_name AS codriver_last_name,
  pcd.nationality AS codriver_nationality,
  t.name AS team_name,
  esh.changed_at AS retired_at,
  esh.reason AS retirement_reason
FROM entries e
JOIN persons pd ON pd.id = e.driver_id
JOIN persons pcd ON pcd.id = e.codriver_id
LEFT JOIN teams t ON t.id = e.team_id
LEFT JOIN entry_status_history esh ON esh.entry_id = e.id 
  AND esh.new_status = e.status 
  AND esh.new_status IN ('retired', 'dns', 'dnf', 'dsq')
WHERE e.status IN ('retired', 'dns', 'dnf', 'dsq');

-- Add comments to document the views
COMMENT ON VIEW active_entries IS 'View of all active entries (not retired, dns, dnf, or dsq)';
COMMENT ON VIEW retired_entries IS 'View of all retired entries with retirement information';
