import pool from '../config/db.js';

async function testRemoteConnection() {
  console.log('🌐 Testing Remote Database Connection...\n');

  try {
    console.log('📡 Connecting to:', process.env.database_host);
    console.log('🔌 Port:', process.env.database_port);
    console.log('👤 User:', process.env.database_user);
    console.log('🗄️  Database:', process.env.database_name);
    console.log('');

    // Test basic connection
    console.log('1. Testing basic connection...');
    const result = await pool.query('SELECT NOW() as current_time, version() as pg_version');
    console.log('✅ Connection successful!');
    console.log('   Current time:', result.rows[0].current_time);
    console.log('   PostgreSQL version:', result.rows[0].pg_version.split(' ')[0]);

    // Check if results table exists
    console.log('\n2. Checking if results table exists...');
    const resultsTable = await pool.query(`
      SELECT table_name, table_schema
      FROM information_schema.tables 
      WHERE table_name = 'results'
    `);
    
    if (resultsTable.rows.length > 0) {
      console.log('✅ Results table exists in schema:', resultsTable.rows[0].table_schema);
    } else {
      console.log('❌ Results table does not exist');
      
      // List all tables to see what's available
      const allTables = await pool.query(`
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public'
        ORDER BY table_name
      `);
      console.log('📋 Available tables:');
      allTables.rows.forEach(row => console.log('   -', row.table_name));
    }

    // Check if entries table exists
    console.log('\n3. Checking if entries table exists...');
    const entriesTable = await pool.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_name = 'entries'
    `);
    console.log(`   Entries table: ${entriesTable.rows.length > 0 ? '✅ Exists' : '❌ Missing'}`);

    // Check current results table schema
    if (resultsTable.rows.length > 0) {
      console.log('\n4. Checking results table schema...');
      const schema = await pool.query(`
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns 
        WHERE table_name = 'results'
        ORDER BY ordinal_position
      `);
      
      console.log('📋 Results table columns:');
      schema.rows.forEach(col => {
        console.log(`   ${col.column_name}: ${col.data_type}`);
      });

      // Check if is_active already exists
      const hasIsActive = schema.rows.some(col => col.column_name === 'is_active');
      console.log(`\n   is_active column: ${hasIsActive ? '✅ Already exists' : '❌ Needs to be added'}`);
    }

    console.log('\n🎉 Remote database connection test completed successfully!');

  } catch (error) {
    console.error('❌ Connection test failed:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 Troubleshooting tips:');
      console.log('   - Check if the remote server is running');
      console.log('   - Verify your internet connection');
      console.log('   - Check if the hostname is correct');
      console.log('   - Verify firewall settings');
    } else if (error.code === 'ENOTFOUND') {
      console.log('\n💡 DNS resolution failed:');
      console.log('   - Check if the hostname is spelled correctly');
      console.log('   - Try pinging the host:', process.env.database_host);
    } else if (error.message.includes('authentication')) {
      console.log('\n💡 Authentication failed:');
      console.log('   - Check username and password');
      console.log('   - Verify database permissions');
    }
    
    throw error;
  } finally {
    await pool.end();
  }
}

// Run the test
testRemoteConnection()
  .then(() => {
    console.log('\n✨ Test completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n💥 Test failed:', error.message);
    process.exit(1);
  });
