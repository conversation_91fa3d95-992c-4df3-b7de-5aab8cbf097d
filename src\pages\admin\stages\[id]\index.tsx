import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Timer } from 'lucide-react';
import StageForm from '../../../../components/admin/stage-form';

interface Stage {
  id: string;
  rally_id: string;
  rally_name?: string;
  name: string;
  number: number;
  length: number;
  surface: string;
  start_time: string;
  status: string;
  is_super_special: boolean;
  is_power_stage: boolean;
}

const EditStagePage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [stage, setStage] = useState<Stage | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const navigate = useNavigate();

  useEffect(() => {
    if (id) {
      fetchStage();
    }
  }, [id]);

  const fetchStage = async () => {
    try {
      const res = await fetch(`/api/stages/${id}`, { credentials: 'include' });
      if (!res.ok) throw new Error('Failed to fetch stage');
      const data = await res.json();
      setStage(data);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (formData: any) => {
    console.log('Updating stage with data:', formData);
    
    const response = await fetch(`/api/stages/${id}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      credentials: 'include',
      body: JSON.stringify(formData)
    });

    // Log the response for debugging
    console.log('Response status:', response.status);
    
    if (!response.ok) {
      const data = await response.json();
      console.error('Error response:', data);
      throw new Error(data.message || data.error || 'Failed to update stage');
    }

    navigate('/admin/stages');
  };

  const handleDelete = async () => {
    const response = await fetch(`/api/stages/${id}`, {
      method: 'DELETE',
      credentials: 'include',
    });

    if (!response.ok) {
      const data = await response.json();
      throw new Error(data.message || data.error || 'Failed to delete stage');
    }

    navigate('/admin/stages');
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8 text-center">
        <div className="inline-block animate-spin rounded-full h-8 w-8 border-4 border-gray-300 border-t-red-600"></div>
        <p className="mt-2 text-gray-600 dark:text-gray-400">Loading stage...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="p-4 bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-200 rounded">
          {error}
        </div>
      </div>
    );
  }

  if (!stage) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="p-4 bg-yellow-100 dark:bg-yellow-900 text-yellow-700 dark:text-yellow-200 rounded">
          Stage not found
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center mb-6">
        <Timer className="w-6 h-6 text-red-600 mr-2" />
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          Edit Stage: {stage.name}
        </h1>
      </div>

      <StageForm 
        initialData={stage} 
        onSubmit={handleSubmit}
        onDelete={handleDelete}
      />
    </div>
  );
};

export default EditStagePage;
