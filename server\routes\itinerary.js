import express from 'express';
import pool from '../config/db.js';
import { verifyToken, verifyAdmin } from '../middleware/auth.js';

const router = express.Router();

// Create itinerary item
router.post('/', verifyToken, verifyAdmin, async (req, res) => {
  try {
    const {
      rally_id,
      type,
      name,
      location,
      start_time,
      duration,
      leg_number,
      day_number,
      order_in_day,
      related_id,
      additional_info
    } = req.body;

    // Convert empty string related_id to null
    const relatedIdValue = related_id === '' ? null : related_id;

    // Convert additional_info to JSON string if it's an object
    const additionalInfoValue = typeof additional_info === 'object'
      ? JSON.stringify(additional_info)
      : additional_info;

    const result = await pool.query(
      `INSERT INTO itinerary_items (
        rally_id, type, name, location, start_time, duration,
        leg_number, day_number, order_in_day, related_id, additional_info
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11) RETURNING *`,
      [
        rally_id, type, name, location, start_time, duration,
        leg_number, day_number, order_in_day, relatedIdValue, additionalInfoValue
      ]
    );

    res.status(201).json(result.rows[0]);
  } catch (error) {
    console.error('Error creating itinerary item:', error);
    res.status(500).json({
      message: 'Failed to create itinerary item',
      error: error.message,
      details: error.stack
    });
  }
});

// Get all itinerary items for a rally
router.get('/rally/:rallyId', async (req, res) => {
  try {
    const { rallyId } = req.params;

    const result = await pool.query(`
      SELECT * FROM itinerary_items
      WHERE rally_id = $1
      ORDER BY day_number, leg_number, order_in_day, start_time
    `, [rallyId]);

    res.json(result.rows);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Failed to fetch itinerary items' });
  }
});

// Get itinerary summary (legs, days, totals)
router.get('/rally/:rallyId/summary', async (req, res) => {
  try {
    const { rallyId } = req.params;

    // Get legs and days
    const legsResult = await pool.query(`
      SELECT DISTINCT leg_number, day_number
      FROM itinerary_items
      WHERE rally_id = $1
      ORDER BY leg_number, day_number
    `, [rallyId]);

    // Get stage totals per leg
    const stageTotalsResult = await pool.query(`
      SELECT
        leg_number,
        COUNT(*) as stage_count,
        SUM(CASE WHEN type = 'stage' THEN
          CAST(additional_info->>'length' AS NUMERIC)
        ELSE 0 END) as stage_distance
      FROM itinerary_items
      WHERE rally_id = $1 AND type = 'stage'
      GROUP BY leg_number
      ORDER BY leg_number
    `, [rallyId]);

    // Get overall totals
    const overallTotalsResult = await pool.query(`
      SELECT
        COUNT(*) as total_stages,
        SUM(CAST(additional_info->>'length' AS NUMERIC)) as total_stage_distance
      FROM itinerary_items
      WHERE rally_id = $1 AND type = 'stage'
    `, [rallyId]);

    res.json({
      legs: legsResult.rows,
      stageTotals: stageTotalsResult.rows,
      overallTotals: overallTotalsResult.rows[0]
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Failed to fetch itinerary summary' });
  }
});

// Update itinerary item
router.put('/:id', verifyToken, verifyAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const {
      type,
      name,
      location,
      start_time,
      duration,
      leg_number,
      day_number,
      order_in_day,
      related_id,
      additional_info
    } = req.body;

    // Convert empty string related_id to null
    const relatedIdValue = related_id === '' ? null : related_id;

    // Convert additional_info to JSON string if it's an object
    const additionalInfoValue = typeof additional_info === 'object'
      ? JSON.stringify(additional_info)
      : additional_info;

    const result = await pool.query(
      `UPDATE itinerary_items SET
        type = $1,
        name = $2,
        location = $3,
        start_time = $4,
        duration = $5,
        leg_number = $6,
        day_number = $7,
        order_in_day = $8,
        related_id = $9,
        additional_info = $10,
        updated_at = NOW()
      WHERE id = $11 RETURNING *`,
      [
        type, name, location, start_time, duration,
        leg_number, day_number, order_in_day, relatedIdValue,
        additionalInfoValue, id
      ]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ message: 'Itinerary item not found' });
    }

    res.json(result.rows[0]);
  } catch (error) {
    console.error('Error updating itinerary item:', error);
    res.status(500).json({
      message: 'Failed to update itinerary item',
      error: error.message,
      details: error.stack
    });
  }
});

// Delete itinerary item
router.delete('/:id', verifyToken, verifyAdmin, async (req, res) => {
  try {
    const { id } = req.params;

    const result = await pool.query(
      'DELETE FROM itinerary_items WHERE id = $1 RETURNING *',
      [id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ message: 'Itinerary item not found' });
    }

    res.json({ message: 'Itinerary item deleted successfully' });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Failed to delete itinerary item' });
  }
});

// Generate itinerary items from stages
router.post('/generate-from-stages/:rallyId', verifyToken, verifyAdmin, async (req, res) => {
  try {
    const { rallyId } = req.params;

    // Get all stages for the rally
    const stagesResult = await pool.query(
      'SELECT * FROM stages WHERE rally_id = $1 ORDER BY start_time',
      [rallyId]
    );

    const stages = stagesResult.rows;

    // Begin transaction
    await pool.query('BEGIN');

    // Delete existing stage-type itinerary items
    await pool.query(
      'DELETE FROM itinerary_items WHERE rally_id = $1 AND type = $2',
      [rallyId, 'stage']
    );

    // Insert new itinerary items for each stage
    for (let i = 0; i < stages.length; i++) {
      const stage = stages[i];

      await pool.query(
        `INSERT INTO itinerary_items (
          rally_id, type, name, location, start_time,
          leg_number, day_number, order_in_day, related_id, additional_info
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)`,
        [
          rallyId,
          'stage',
          `SS${stage.number}: ${stage.name}`,
          null,
          stage.start_time,
          stage.leg_number || 1,
          stage.day_number || 1,
          (i + 1) * 10, // Order in day, spaced by 10 to allow manual inserts
          stage.id,
          JSON.stringify({
            length: stage.length,
            surface: stage.surface,
            is_power_stage: stage.is_power_stage,
            is_super_special: stage.is_super_special
          })
        ]
      );
    }

    // Commit transaction
    await pool.query('COMMIT');

    res.json({ message: 'Itinerary items generated from stages successfully' });
  } catch (error) {
    // Rollback transaction on error
    await pool.query('ROLLBACK');
    console.error(error);
    res.status(500).json({ message: 'Failed to generate itinerary items' });
  }
});

export default router;