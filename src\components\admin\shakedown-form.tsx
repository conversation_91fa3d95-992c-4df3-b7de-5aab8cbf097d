import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Clock, MapPin, Calendar, Flag } from 'lucide-react';

interface Rally {
  id: string;
  name: string;
}

interface ShakedownFormProps {
  initialData?: any;
  onSubmit?: (formData: any) => Promise<void>;
  onDelete?: () => Promise<void>;
  preselectedRallyId?: string;
}

const ShakedownForm: React.FC<ShakedownFormProps> = ({ 
  initialData, 
  onSubmit,
  onDelete,
  preselectedRallyId
}) => {
  const [form, setForm] = useState({
    rally_id: '',
    name: '',
    location: '',
    length: '',
    date: '',
    start_time: '',
    end_time: '',
    max_runs: '3'
  });
  const [rallies, setRallies] = useState<Rally[]>([]);
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const navigate = useNavigate();

  useEffect(() => {
    fetchRallies();
  }, []);

  useEffect(() => {
    if (initialData) {
      // Format dates and times for form inputs
      const formattedData = {
        ...initialData,
        date: initialData.date ? new Date(initialData.date).toISOString().split('T')[0] : '',
        start_time: initialData.start_time ? new Date(initialData.start_time).toISOString().slice(0, 16) : '',
        end_time: initialData.end_time ? new Date(initialData.end_time).toISOString().slice(0, 16) : '',
      };
      setForm(formattedData);
    } else if (preselectedRallyId) {
      setForm(prev => ({ ...prev, rally_id: preselectedRallyId }));
    }
  }, [initialData, preselectedRallyId]);

  const fetchRallies = async () => {
    try {
      const res = await fetch('/api/rallies', { credentials: 'include' });
      if (!res.ok) throw new Error('Failed to fetch rallies');
      const data = await res.json();
      setRallies(Array.isArray(data) ? data : data.rallies || []);
    } catch (err: any) {
      console.error('Failed to fetch rallies:', err.message);
      setError('Failed to load rallies. Please try again.');
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setForm(prev => ({ ...prev, [name]: value }));
    
    // Clear validation error when field is edited
    if (validationErrors[name]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  const validateForm = () => {
    const errors: Record<string, string> = {};
    
    if (!form.rally_id) errors.rally_id = 'Rally is required';
    if (!form.name) errors.name = 'Name is required';
    if (!form.location) errors.location = 'Location is required';
    if (!form.length) errors.length = 'Length is required';
    if (!form.date) errors.date = 'Date is required';
    if (!form.start_time) errors.start_time = 'Start time is required';
    if (!form.end_time) errors.end_time = 'End time is required';
    if (!form.max_runs) errors.max_runs = 'Maximum runs is required';
    
    // Validate numeric fields
    if (form.length && isNaN(Number(form.length))) {
      errors.length = 'Length must be a number';
    }
    
    if (form.max_runs && isNaN(Number(form.max_runs))) {
      errors.max_runs = 'Maximum runs must be a number';
    }
    
    // Validate date and times
    if (form.start_time && form.end_time) {
      const start = new Date(form.start_time);
      const end = new Date(form.end_time);
      
      if (start >= end) {
        errors.end_time = 'End time must be after start time';
      }
    }
    
    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setLoading(true);
    setError('');
    
    try {
      // Format the data for API
      const formattedData = {
        ...form,
        length: parseFloat(form.length),
        max_runs: parseInt(form.max_runs)
      };
      
      if (onSubmit) {
        await onSubmit(formattedData);
      } else {
        // Default submission logic
        const url = initialData ? `/api/shakedowns/${initialData.id}` : '/api/shakedowns';
        const method = initialData ? 'PUT' : 'POST';
        
        const response = await fetch(url, {
          method,
          headers: { 'Content-Type': 'application/json' },
          credentials: 'include',
          body: JSON.stringify(formattedData)
        });
        
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || 'Failed to save shakedown');
        }
        
        navigate('/admin/shakedowns');
      }
    } catch (err: any) {
      console.error('Error saving shakedown:', err);
      setError(err.message || 'An error occurred while saving the shakedown');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!initialData || !initialData.id) return;
    
    if (!window.confirm('Are you sure you want to delete this shakedown?')) {
      return;
    }
    
    setLoading(true);
    
    try {
      if (onDelete) {
        await onDelete();
      } else {
        const response = await fetch(`/api/shakedowns/${initialData.id}`, {
          method: 'DELETE',
          credentials: 'include'
        });
        
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || 'Failed to delete shakedown');
        }
        
        navigate('/admin/shakedowns');
      }
    } catch (err: any) {
      console.error('Error deleting shakedown:', err);
      setError(err.message || 'An error occurred while deleting the shakedown');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
      {error && (
        <div className="mb-4 p-3 bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-200 rounded-lg">
          {error}
        </div>
      )}
      
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Rally <span className="text-red-500">*</span>
            </label>
            <div className="relative">
              <Flag className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500" size={18} />
              <select
                name="rally_id"
                value={form.rally_id}
                onChange={handleChange}
                className={`w-full pl-10 pr-3 py-2 rounded-lg border ${
                  validationErrors.rally_id 
                    ? 'border-red-500 dark:border-red-500' 
                    : 'border-gray-300 dark:border-gray-600'
                } bg-white dark:bg-gray-700 text-gray-900 dark:text-white`}
                disabled={!!preselectedRallyId}
              >
                <option value="">Select Rally</option>
                {rallies.map(rally => (
                  <option key={rally.id} value={rally.id}>
                    {rally.name}
                  </option>
                ))}
              </select>
            </div>
            {validationErrors.rally_id && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">{validationErrors.rally_id}</p>
            )}
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Name <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              name="name"
              value={form.name}
              onChange={handleChange}
              className={`w-full px-3 py-2 rounded-lg border ${
                validationErrors.name 
                  ? 'border-red-500 dark:border-red-500' 
                  : 'border-gray-300 dark:border-gray-600'
              } bg-white dark:bg-gray-700 text-gray-900 dark:text-white`}
              placeholder="Shakedown Name"
            />
            {validationErrors.name && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">{validationErrors.name}</p>
            )}
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Location <span className="text-red-500">*</span>
            </label>
            <div className="relative">
              <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500" size={18} />
              <input
                type="text"
                name="location"
                value={form.location}
                onChange={handleChange}
                className={`w-full pl-10 pr-3 py-2 rounded-lg border ${
                  validationErrors.location 
                    ? 'border-red-500 dark:border-red-500' 
                    : 'border-gray-300 dark:border-gray-600'
                } bg-white dark:bg-gray-700 text-gray-900 dark:text-white`}
                placeholder="Location"
              />
            </div>
            {validationErrors.location && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">{validationErrors.location}</p>
            )}
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Length (km) <span className="text-red-500">*</span>
            </label>
            <input
              type="number"
              step="0.01"
              min="0.1"
              name="length"
              value={form.length}
              onChange={handleChange}
              className={`w-full px-3 py-2 rounded-lg border ${
                validationErrors.length 
                  ? 'border-red-500 dark:border-red-500' 
                  : 'border-gray-300 dark:border-gray-600'
              } bg-white dark:bg-gray-700 text-gray-900 dark:text-white`}
              placeholder="Length in kilometers"
            />
            {validationErrors.length && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">{validationErrors.length}</p>
            )}
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Date <span className="text-red-500">*</span>
            </label>
            <div className="relative">
              <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500" size={18} />
              <input
                type="date"
                name="date"
                value={form.date}
                onChange={handleChange}
                className={`w-full pl-10 pr-3 py-2 rounded-lg border ${
                  validationErrors.date 
                    ? 'border-red-500 dark:border-red-500' 
                    : 'border-gray-300 dark:border-gray-600'
                } bg-white dark:bg-gray-700 text-gray-900 dark:text-white`}
              />
            </div>
            {validationErrors.date && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">{validationErrors.date}</p>
            )}
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Start Time <span className="text-red-500">*</span>
            </label>
            <div className="relative">
              <Clock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500" size={18} />
              <input
                type="datetime-local"
                name="start_time"
                value={form.start_time}
                onChange={handleChange}
                className={`w-full pl-10 pr-3 py-2 rounded-lg border ${
                  validationErrors.start_time 
                    ? 'border-red-500 dark:border-red-500' 
                    : 'border-gray-300 dark:border-gray-600'
                } bg-white dark:bg-gray-700 text-gray-900 dark:text-white`}
              />
            </div>
            {validationErrors.start_time && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">{validationErrors.start_time}</p>
            )}
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              End Time <span className="text-red-500">*</span>
            </label>
            <div className="relative">
              <Clock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500" size={18} />
              <input
                type="datetime-local"
                name="end_time"
                value={form.end_time}
                onChange={handleChange}
                className={`w-full pl-10 pr-3 py-2 rounded-lg border ${
                  validationErrors.end_time 
                    ? 'border-red-500 dark:border-red-500' 
                    : 'border-gray-300 dark:border-gray-600'
                } bg-white dark:bg-gray-700 text-gray-900 dark:text-white`}
              />
            </div>
            {validationErrors.end_time && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">{validationErrors.end_time}</p>
            )}
          </div>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Maximum Runs <span className="text-red-500">*</span>
          </label>
          <input
            type="number"
            min="1"
            max="10"
            name="max_runs"
            value={form.max_runs}
            onChange={handleChange}
            className={`w-full px-3 py-2 rounded-lg border ${
              validationErrors.max_runs 
                ? 'border-red-500 dark:border-red-500' 
                : 'border-gray-300 dark:border-gray-600'
            } bg-white dark:bg-gray-700 text-gray-900 dark:text-white`}
            placeholder="Maximum number of runs"
          />
          {validationErrors.max_runs && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">{validationErrors.max_runs}</p>
          )}
        </div>
        
        <div className="flex justify-between pt-4">
          <div>
            {initialData && (
              <button
                type="button"
                onClick={handleDelete}
                disabled={loading}
                className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg disabled:opacity-50 transition-colors"
              >
                {loading ? 'Deleting...' : 'Delete Shakedown'}
              </button>
            )}
          </div>
          
          <div className="flex space-x-3">
            <button
              type="button"
              onClick={() => navigate('/admin/shakedowns')}
              className="bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 px-4 py-2 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
            >
              Cancel
            </button>
            
            <button
              type="submit"
              disabled={loading}
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg disabled:opacity-50 transition-colors"
            >
              {loading ? 'Saving...' : initialData ? 'Update Shakedown' : 'Create Shakedown'}
            </button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default ShakedownForm;
