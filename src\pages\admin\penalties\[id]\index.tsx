import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { AlertTriangle } from 'lucide-react';
import PenaltyForm from '../../../../components/admin/penalty-form';

interface Penalty {
  id: string;
  entry_id: string;
  stage_id: string;
  penalty_type: string;
  penalty_value: number;
  notes?: string;
  created_at?: string;
  updated_at?: string;
  rally_id?: string;
  rally_name?: string;
  stage_name?: string;
  entry_number?: number;
}

const EditPenaltyPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [penalty, setPenalty] = useState<Penalty | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const navigate = useNavigate();

  useEffect(() => {
    if (id) {
      fetchPenalty();
    }
  }, [id]);

  const fetchPenalty = async () => {
    try {
      const res = await fetch(`/api/penalties/${id}`, { credentials: 'include' });
      if (!res.ok) throw new Error('Failed to fetch penalty');
      const data = await res.json();
      setPenalty(data);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (formData: any) => {
    console.log('Updating penalty with data:', formData);
    
    const response = await fetch(`/api/penalties/${id}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      credentials: 'include',
      body: JSON.stringify(formData)
    });

    // Log the response for debugging
    console.log('Response status:', response.status);
    
    if (!response.ok) {
      const data = await response.json();
      console.error('Error response:', data);
      throw new Error(data.message || data.error || 'Failed to update penalty');
    }

    navigate('/admin/penalties');
  };

  const handleDelete = async () => {
    const response = await fetch(`/api/penalties/${id}`, {
      method: 'DELETE',
      credentials: 'include',
    });

    if (!response.ok) {
      const data = await response.json();
      throw new Error(data.message || data.error || 'Failed to delete penalty');
    }

    navigate('/admin/penalties');
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8 text-center">
        <div className="inline-block animate-spin rounded-full h-8 w-8 border-4 border-gray-300 border-t-red-600"></div>
        <p className="mt-2 text-gray-600 dark:text-gray-400">Loading penalty...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="p-4 bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-200 rounded">
          {error}
        </div>
      </div>
    );
  }

  if (!penalty) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="p-4 bg-yellow-100 dark:bg-yellow-900 text-yellow-700 dark:text-yellow-200 rounded">
          Penalty not found
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center mb-6">
        <AlertTriangle className="w-6 h-6 text-red-600 mr-2" />
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          Edit Penalty
          {penalty.entry_number && <span className="ml-2 text-gray-600 dark:text-gray-400 text-lg">
            (Entry #{penalty.entry_number})
          </span>}
        </h1>
      </div>

      <PenaltyForm 
        initialData={penalty} 
        onSubmit={handleSubmit}
        onDelete={handleDelete}
      />
    </div>
  );
};

export default EditPenaltyPage;
