import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Upload, ClipboardList } from 'lucide-react';
import { useAuth } from '../../../../context/AuthContext';

const ImportEntriesPage: React.FC = () => {
  const [file, setFile] = useState<File | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [result, setResult] = useState<any>(null);
  const [checkName, setCheckName] = useState({ firstName: '', lastName: '' });
  const [checkResult, setCheckResult] = useState<any>(null);
  const [checkingPerson, setCheckingPerson] = useState(false);
  const [flexibleSearch, setFlexibleSearch] = useState(true);
  const navigate = useNavigate();
  const { user, isAdmin } = useAuth();

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setFile(e.target.files[0]);
    }
  };

  const handleCheckPerson = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!checkName.firstName || !checkName.lastName) {
      setError('Please enter both first name and last name');
      return;
    }

    setCheckingPerson(true);
    setError('');
    setCheckResult(null);

    try {
      const response = await fetch(
        `/api/entries/debug/check-person?first_name=${encodeURIComponent(checkName.firstName)}&last_name=${encodeURIComponent(checkName.lastName)}&flexible=${flexibleSearch}`,
        {
          credentials: 'include',
        }
      );

      const data = await response.json();
      setCheckResult(data);
    } catch (err: any) {
      setError(err.message || 'Failed to check person');
    } finally {
      setCheckingPerson(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!file) {
      setError('Please select a CSV file');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch('/api/entries/import', {
        method: 'POST',
        credentials: 'include',
        body: formData,
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to import entries');
      }

      setResult(data);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center mb-6">
        <ClipboardList className="w-6 h-6 text-red-600 mr-2" />
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Import Entries</h1>
      </div>

      {error && (
        <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          {error}
        </div>
      )}

      {result ? (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">Import Results</h2>
          <div className="grid grid-cols-3 gap-4 mb-4">
            <div className="bg-green-100 dark:bg-green-900 p-4 rounded-lg">
              <p className="text-green-800 dark:text-green-200 text-2xl font-bold">{result.imported}</p>
              <p className="text-green-600 dark:text-green-400">Entries imported</p>
            </div>
            <div className="bg-red-100 dark:bg-red-900 p-4 rounded-lg">
              <p className="text-red-800 dark:text-red-200 text-2xl font-bold">{result.errors}</p>
              <p className="text-red-600 dark:text-red-400">Errors</p>
            </div>
            <div className="bg-yellow-100 dark:bg-yellow-900 p-4 rounded-lg">
              <p className="text-yellow-800 dark:text-yellow-200 text-2xl font-bold">{result.skipped}</p>
              <p className="text-yellow-600 dark:text-yellow-400">Skipped</p>
            </div>
          </div>

          {result.skipped > 0 && result.skipReasons && (
            <div className="mt-4 mb-6">
              <h3 className="text-lg font-semibold mb-2">Reasons for skipped entries:</h3>
              <ul className="list-disc pl-5 space-y-1">
                {Object.entries(result.skipReasons).map(([reason, count]) => (
                  <li key={reason} className="text-gray-700 dark:text-gray-300">
                    {reason}: <span className="font-semibold">{count as number}</span> entries
                  </li>
                ))}
              </ul>
            </div>
          )}

          {result.sampleSkipped && result.sampleSkipped.length > 0 && (
            <div className="mt-4 mb-6">
              <h3 className="text-lg font-semibold mb-2">Sample skipped entries:</h3>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <thead className="bg-gray-50 dark:bg-gray-700">
                    <tr>
                      <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">Reason</th>
                      <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">Data</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    {result.sampleSkipped.map((item: any, index: number) => (
                      <tr key={index}>
                        <td className="px-3 py-2 text-sm text-gray-700 dark:text-gray-300">{item.reason}</td>
                        <td className="px-3 py-2 text-sm text-gray-700 dark:text-gray-300">
                          <pre className="text-xs overflow-x-auto">{JSON.stringify(item.data, null, 2)}</pre>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          <div className="flex justify-end">
            <button
              onClick={() => navigate('/admin/entries')}
              className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
            >
              Go to Entries
            </button>
          </div>
        </div>
      ) : (
        <div className="space-y-6">
          {/* Person Checker Tool */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
            <h2 className="text-xl font-semibold mb-4">Person Checker Tool</h2>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
              Use this tool to check if a person exists in the database before importing entries.
            </p>

            <form onSubmit={handleCheckPerson} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    First Name
                  </label>
                  <input
                    type="text"
                    value={checkName.firstName}
                    onChange={(e) => setCheckName({...checkName, firstName: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:text-white"
                    placeholder="Enter first name"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Last Name
                  </label>
                  <input
                    type="text"
                    value={checkName.lastName}
                    onChange={(e) => setCheckName({...checkName, lastName: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:text-white"
                    placeholder="Enter last name"
                  />
                </div>
              </div>

              <div className="flex items-center mt-2">
                <input
                  type="checkbox"
                  id="flexibleSearch"
                  checked={flexibleSearch}
                  onChange={(e) => setFlexibleSearch(e.target.checked)}
                  className="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded"
                />
                <label htmlFor="flexibleSearch" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                  Use flexible matching (try reversed names and partial matches)
                </label>
              </div>

              <div className="flex justify-end">
                <button
                  type="submit"
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center"
                  disabled={checkingPerson}
                >
                  {checkingPerson ? (
                    <>
                      <span className="animate-spin mr-2">⟳</span>
                      Checking...
                    </>
                  ) : (
                    'Check Person'
                  )}
                </button>
              </div>
            </form>

            {checkResult && (
              <div className={`mt-4 p-4 rounded-lg ${checkResult.exists ? 'bg-green-100 dark:bg-green-900' : 'bg-red-100 dark:bg-red-900'}`}>
                <p className={`font-medium ${checkResult.exists ? 'text-green-800 dark:text-green-200' : 'text-red-800 dark:text-red-200'}`}>
                  {checkResult.message}
                </p>
                {checkResult.exists && (
                  <div className="mt-2">
                    {checkResult.count === 1 ? (
                      <div className="text-sm">
                        <p><strong>ID:</strong> {checkResult.persons[0].id}</p>
                        <p><strong>Name:</strong> {checkResult.persons[0].first_name} {checkResult.persons[0].last_name}</p>
                        <p><strong>Nationality:</strong> {checkResult.persons[0].nationality}</p>
                      </div>
                    ) : (
                      <div>
                        <p className="text-sm mb-2">Multiple matches found:</p>
                        <div className="overflow-x-auto">
                          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                            <thead className="bg-gray-50 dark:bg-gray-700">
                              <tr>
                                <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">ID</th>
                                <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">First Name</th>
                                <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">Last Name</th>
                                <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">Nationality</th>
                              </tr>
                            </thead>
                            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                              {checkResult.persons.map((person: any) => (
                                <tr key={person.id}>
                                  <td className="px-3 py-2 text-sm text-gray-700 dark:text-gray-300">{person.id}</td>
                                  <td className="px-3 py-2 text-sm text-gray-700 dark:text-gray-300">{person.first_name}</td>
                                  <td className="px-3 py-2 text-sm text-gray-700 dark:text-gray-300">{person.last_name}</td>
                                  <td className="px-3 py-2 text-sm text-gray-700 dark:text-gray-300">{person.nationality}</td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}
          </div>

          {/* CSV Import Form */}
          <form onSubmit={handleSubmit} className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">Import Entries from CSV</h2>
            <div className="mb-6">
              <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6">
                <label className="flex flex-col items-center cursor-pointer">
                  <div className="flex flex-col items-center">
                    <Upload className="w-12 h-12 text-gray-400 mb-2" />
                    <span className="text-gray-700 dark:text-gray-300 font-medium mb-1">
                      Click to select a CSV file
                    </span>
                    <div className="text-xs text-gray-500 dark:text-gray-400 text-center">
                      <p className="mb-1">
                        CSV file with columns: rally_id, driver_first_name, driver_last_name, codriver_first_name, codriver_last_name, car, number, class, status, team_name (optional)
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        <strong>Note:</strong> Persons must already exist in the database. Names are matched case-insensitively.
                      </p>
                    </div>
                    <input
                      type="file"
                      className="hidden"
                      accept=".csv"
                      onChange={handleFileChange}
                    />
                  </div>
                </label>
              </div>
              {file && (
                <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                  Selected file: {file.name}
                </p>
              )}
            </div>

            <div className="flex justify-end space-x-4">
              <button
                type="button"
                onClick={() => navigate('/admin/entries')}
                className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
                disabled={loading}
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 flex items-center"
                disabled={loading}
              >
                {loading ? (
                  <>
                    <span className="animate-spin mr-2">⟳</span>
                    Importing...
                  </>
                ) : (
                  <>
                    <Upload className="w-4 h-4 mr-2" />
                    Import Entries
                  </>
                )}
              </button>
            </div>
          </form>
        </div>
      )}
    </div>
  );
};

export default ImportEntriesPage;