-- Debug Championship Filtering
-- Let's see why Historic championships are including wrong classes

-- 1. Check current eligibility rules
SELECT 'Current Championship Eligibility Rules:' as debug;
SELECT 
    c.name as championship,
    ce.class_pattern,
    ce.description
FROM championship_eligibility ce
JOIN championships c ON c.id = ce.championship_id
ORDER BY c.name, ce.class_pattern;

-- 2. Check what classes exist in Rally Stereas Elladas 2025
SELECT 'Classes in Rally Stereas Elladas 2025:' as debug;
SELECT DISTINCT 
    e.class,
    COUNT(*) as driver_count
FROM entries e
JOIN rallies r ON e.rally_id = r.id
WHERE r.name = 'Rally Stereas Elladas 2025'
GROUP BY e.class
ORDER BY e.class;

-- 3. Test the eligibility matching logic
SELECT 'Eligibility Matching Test:' as debug;
SELECT 
    e.class,
    ce.class_pattern,
    c.name as championship,
    CASE 
        WHEN e.class = ce.class_pattern THEN 'EXACT MATCH'
        WHEN e.class LIKE '%' || ce.class_pattern || '%' THEN 'CONTAINS MATCH'
        ELSE 'NO MATCH'
    END as match_result
FROM entries e
JOIN rallies r ON e.rally_id = r.id
CROSS JOIN championship_eligibility ce
JOIN championships c ON ce.championship_id = c.id
WHERE r.name = 'Rally Stereas Elladas 2025'
AND c.name IN ('Historic', 'Historic Gravel Cup')
ORDER BY c.name, e.class, ce.class_pattern;

-- 4. The problem: Classes like "C6, F2" contain "2" so they match Historic class "2"
-- We need EXACT matching for numeric classes

-- 5. Fix the eligibility rules - use exact matching for Historic championships
DELETE FROM championship_eligibility 
WHERE championship_id IN (
    'cc1e9147-fdb3-4483-aad3-9e27795eff17'::uuid,
    'aa934657-d4f5-49bb-80d5-b0be90e72b4c'::uuid
);

-- Historic Championship: EXACT numeric classes only
INSERT INTO championship_eligibility (championship_id, class_pattern, description) VALUES
('cc1e9147-fdb3-4483-aad3-9e27795eff17'::uuid, '2', 'Historic class 2 - EXACT match only'),
('cc1e9147-fdb3-4483-aad3-9e27795eff17'::uuid, '3', 'Historic class 3 - EXACT match only'),
('cc1e9147-fdb3-4483-aad3-9e27795eff17'::uuid, '4', 'Historic class 4 - EXACT match only');

-- Historic Gravel Cup: EXACT numeric classes only
INSERT INTO championship_eligibility (championship_id, class_pattern, description) VALUES
('aa934657-d4f5-49bb-80d5-b0be90e72b4c'::uuid, '2', 'Historic Gravel class 2 - EXACT match only'),
('aa934657-d4f5-49bb-80d5-b0be90e72b4c'::uuid, '3', 'Historic Gravel class 3 - EXACT match only'),
('aa934657-d4f5-49bb-80d5-b0be90e72b4c'::uuid, '4', 'Historic Gravel class 4 - EXACT match only');

-- 6. Update the championship view to use EXACT matching for numeric classes
DROP VIEW IF EXISTS championship_overall_classification CASCADE;

CREATE VIEW championship_overall_classification AS
SELECT
  ce.championship_id,
  c.name AS championship_name,
  e.rally_id,
  r.name AS rally_name,
  e.id AS entry_id,
  e.number,
  pd.first_name || ' ' || pd.last_name AS driver,
  pcd.first_name || ' ' || pcd.last_name AS codriver,
  e.class,
  e.car,
  SUM(res.time + COALESCE(p.time, 0)) AS total_time,
  RANK() OVER (
    PARTITION BY ce.championship_id, e.rally_id
    ORDER BY SUM(res.time + COALESCE(p.time, 0))
  ) AS championship_position,
  SUM(res.time + COALESCE(p.time, 0)) - MIN(SUM(res.time + COALESCE(p.time, 0))) OVER (
    PARTITION BY ce.championship_id, e.rally_id
  ) AS time_diff,
  ce.coefficient
FROM championship_events ce
JOIN championships c ON c.id = ce.championship_id
JOIN rallies r ON r.id = ce.rally_id
JOIN entries e ON e.rally_id = ce.rally_id
JOIN results res ON res.entry_id = e.id
JOIN persons pd ON pd.id = e.driver_id
JOIN persons pcd ON pcd.id = e.codriver_id
LEFT JOIN penalties p ON p.entry_id = e.id AND p.stage_id = res.stage_id
WHERE e.status NOT IN ('retired', 'dnf', 'dsq')
-- IMPROVED CHAMPIONSHIP ELIGIBILITY FILTER
AND EXISTS (
    SELECT 1 FROM championship_eligibility elig 
    WHERE elig.championship_id = ce.championship_id 
    AND (
        -- For Historic championships (numeric classes): EXACT match only
        (c.name LIKE '%Historic%' AND e.class = elig.class_pattern) OR
        -- For other championships: CONTAINS match
        (c.name NOT LIKE '%Historic%' AND (
            e.class = elig.class_pattern OR 
            e.class LIKE '%' || elig.class_pattern || '%'
        ))
    )
)
GROUP BY ce.championship_id, c.name, e.rally_id, r.name, e.id, e.number,
         pd.first_name, pd.last_name, pcd.first_name, pcd.last_name, e.class, e.car, ce.coefficient;

-- 7. Recreate dependent views
CREATE VIEW championship_points AS
SELECT
  ce.championship_id,
  c.name AS championship_name,
  e.rally_id,
  r.name AS rally_name,
  e.driver_id,
  pd.first_name || ' ' || pd.last_name AS driver,
  e.id AS entry_id,
  e.number,
  e.class,
  coc.championship_position,
  coc.total_time,
  CASE
    WHEN coc.championship_position = 1 THEN 25 * ce.coefficient
    WHEN coc.championship_position = 2 THEN 18 * ce.coefficient
    WHEN coc.championship_position = 3 THEN 15 * ce.coefficient
    WHEN coc.championship_position = 4 THEN 12 * ce.coefficient
    WHEN coc.championship_position = 5 THEN 10 * ce.coefficient
    WHEN coc.championship_position = 6 THEN 8 * ce.coefficient
    WHEN coc.championship_position = 7 THEN 6 * ce.coefficient
    WHEN coc.championship_position = 8 THEN 4 * ce.coefficient
    WHEN coc.championship_position = 9 THEN 2 * ce.coefficient
    WHEN coc.championship_position = 10 THEN 1 * ce.coefficient
    ELSE 0
  END AS rally_points,
  ce.coefficient
FROM championship_events ce
JOIN championships c ON c.id = ce.championship_id
JOIN rallies r ON r.id = ce.rally_id
JOIN championship_overall_classification coc ON coc.championship_id = ce.championship_id
  AND coc.rally_id = ce.rally_id
JOIN entries e ON e.id = coc.entry_id
JOIN persons pd ON pd.id = e.driver_id;

CREATE VIEW championship_standings AS
SELECT
  cp.championship_id,
  cp.championship_name,
  cp.driver_id,
  cp.driver,
  COUNT(DISTINCT cp.rally_id) AS rallies_completed,
  SUM(cp.rally_points) AS total_points,
  RANK() OVER (PARTITION BY cp.championship_id ORDER BY SUM(cp.rally_points) DESC) AS position,
  COALESCE(SUM(psp_total.power_stage_points), 0) AS total_power_stage_points,
  SUM(cp.rally_points) + COALESCE(SUM(psp_total.power_stage_points), 0) AS grand_total_points
FROM championship_points cp
LEFT JOIN (
  SELECT
    ce.championship_id,
    e.driver_id,
    SUM(psp.points) AS power_stage_points
  FROM championship_events ce
  JOIN power_stage_points psp ON psp.rally_id = ce.rally_id
  JOIN entries e ON e.id = psp.entry_id
  GROUP BY ce.championship_id, e.driver_id
) psp_total ON psp_total.championship_id = cp.championship_id AND psp_total.driver_id = cp.driver_id
GROUP BY cp.championship_id, cp.championship_name, cp.driver_id, cp.driver;

-- 8. Test the fix
SELECT 'FIXED - Championship Results for Rally Stereas Elladas 2025:' as test;
SELECT 
    championship_name,
    class,
    driver,
    championship_position
FROM championship_overall_classification
WHERE rally_name = 'Rally Stereas Elladas 2025'
ORDER BY championship_name, championship_position;

SELECT 'Championship filtering fixed!' as result;
