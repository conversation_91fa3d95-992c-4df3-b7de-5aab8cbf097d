import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import pool from './server/config/db.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function runChampionshipUpdate() {
  const client = await pool.connect();
  
  try {
    console.log('🏆 Starting Championship Views Update...');
    console.log('=====================================');
    
    // Read the SQL file
    const sqlPath = path.join(__dirname, 'ChampionshipViewsUpdate.sql');
    const sql = fs.readFileSync(sqlPath, 'utf8');
    
    // Execute the update
    console.log('📝 Executing championship views update...');
    await client.query('BEGIN');
    
    // Split the SQL into individual statements and execute them
    const statements = sql.split(';').filter(stmt => stmt.trim().length > 0);
    
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i].trim();
      if (statement) {
        try {
          await client.query(statement);
          if (statement.toLowerCase().includes('create view')) {
            const viewName = statement.match(/create view (\w+)/i)?.[1];
            console.log(`✅ Created view: ${viewName}`);
          }
        } catch (error) {
          // Some statements might fail if objects don't exist, that's OK
          if (!error.message.includes('does not exist')) {
            console.log(`⚠️  Warning: ${error.message}`);
          }
        }
      }
    }
    
    await client.query('COMMIT');
    
    console.log('\n🔍 Verifying installation...');
    
    // Test the views
    const viewTests = [
      'championship_overall_classification',
      'championship_points',
      'championship_standings', 
      'championship_class_standings'
    ];
    
    for (const viewName of viewTests) {
      try {
        const result = await client.query(`SELECT COUNT(*) as count FROM ${viewName}`);
        console.log(`✅ ${viewName}: ${result.rows[0].count} rows`);
      } catch (error) {
        console.log(`❌ ${viewName}: Failed - ${error.message}`);
      }
    }
    
    // Show sample data
    console.log('\n📊 Championship Data Summary:');
    try {
      const result = await client.query(`
        SELECT 
          championship_name,
          COUNT(DISTINCT rally_id) as rallies,
          COUNT(DISTINCT driver_id) as drivers,
          MAX(coefficient) as max_coefficient
        FROM championship_points 
        GROUP BY championship_id, championship_name
        ORDER BY championship_name
        LIMIT 10
      `);
      
      if (result.rows.length > 0) {
        console.log('Available championships:');
        result.rows.forEach(row => {
          console.log(`  📈 ${row.championship_name}: ${row.rallies} rallies, ${row.drivers} drivers (coeff: ${row.max_coefficient})`);
        });
      } else {
        console.log('  ℹ️  No championship data found yet.');
        console.log('     Add rallies to championships using championship_events table.');
      }
    } catch (error) {
      console.log(`  ⚠️  Could not fetch sample data: ${error.message}`);
    }
    
    console.log('\n🎉 Championship Views Update Completed Successfully!');
    console.log('=====================================');
    console.log('Next steps:');
    console.log('1. Test the new API endpoints');
    console.log('2. Update your frontend to use championship selectors');
    console.log('3. Add rallies to championships via championship_events table');
    
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('❌ Update failed:', error);
    throw error;
  } finally {
    client.release();
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runChampionshipUpdate()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error('💥 Update failed:', error);
      process.exit(1);
    });
}

export default runChampionshipUpdate;
