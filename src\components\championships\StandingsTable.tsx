import React from 'react';
import { Link } from 'react-router-dom';
import { Trophy, ChevronUp } from 'lucide-react';
import { Standings, Driver } from '../../types';

interface StandingsTableProps {
  standings: Standings[];
  drivers: Driver[];
}

const StandingsTable: React.FC<StandingsTableProps> = ({ standings, drivers }) => {
  const findDriver = (driverId: string) => {
    return drivers.find(driver => driver.id === driverId);
  };

  // Helper to get ordinal suffix (1st, 2nd, 3rd, 4th, etc.)
  const getOrdinalSuffix = (position: number): string => {
    if (!position) return '';

    const j = position % 10;
    const k = position % 100;

    if (j === 1 && k !== 11) {
      return 'st';
    }
    if (j === 2 && k !== 12) {
      return 'nd';
    }
    if (j === 3 && k !== 13) {
      return 'rd';
    }
    return 'th';
  };

  const sortedStandings = [...standings].sort((a, b) => a.position - b.position);

  return (
    <div className="overflow-x-auto">
      <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
        <thead className="bg-gray-50 dark:bg-gray-800">
          <tr>
            <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              Pos
            </th>
            <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              Driver
            </th>
            <th scope="col" className="px-3 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              Wins
            </th>
            <th scope="col" className="px-3 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              Points
            </th>
          </tr>
        </thead>
        <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
          {sortedStandings.slice(0, 5).map(standing => {
            const driver = findDriver(standing.driverId);
            if (!driver) return null;

            return (
              <tr key={standing.driverId} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                <td className="px-3 py-3 whitespace-nowrap">
                  <div className="flex items-center">
                    <span className={`text-sm font-medium ${
                      standing.position === 1
                        ? 'text-yellow-500'
                        : standing.position === 2
                        ? 'text-gray-400'
                        : standing.position === 3
                        ? 'text-amber-700'
                        : 'text-gray-900 dark:text-white'
                    }`}>
                      {standing.position === 1 && <Trophy className="w-4 h-4 inline mr-0.5" />}
                      {standing.position}{getOrdinalSuffix(standing.position)}
                    </span>
                  </div>
                </td>
                <td className="px-3 py-3 whitespace-nowrap">
                  <Link to={`/drivers/${driver.id}`} className="flex items-center">
                    <div className="flex-shrink-0 h-8 w-8 relative">
                      <img
                        className="h-8 w-8 rounded-full object-cover"
                        src={driver.image || `https://ui-avatars.com/api/?name=${driver.name}&background=random`}
                        alt={driver.name}
                      />
                      <img
                        src={driver.flagImage}
                        alt={driver.nationality}
                        className="absolute -bottom-1 -right-1 w-4 h-3 rounded-sm border border-white dark:border-gray-800"
                      />
                    </div>
                    <div className="ml-3">
                      <div className="text-sm font-medium text-gray-900 dark:text-white">
                        {driver.name}
                      </div>
                    </div>
                  </Link>
                </td>
                <td className="px-3 py-3 whitespace-nowrap text-center">
                  <span className="text-sm text-gray-700 dark:text-gray-300">{standing.wins}</span>
                </td>
                <td className="px-3 py-3 whitespace-nowrap text-right font-semibold text-gray-900 dark:text-white">
                  {standing.points}
                </td>
              </tr>
            );
          })}
        </tbody>
      </table>
    </div>
  );
};

export default StandingsTable;