-- Migration: Add entry activity tracking to results table
-- This allows tracking when entries become active/inactive during a rally

-- Step 1: Add is_active column to results table
ALTER TABLE results ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT TRUE;

-- Step 2: Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_results_is_active ON results(is_active);
CREATE INDEX IF NOT EXISTS idx_results_entry_active ON results(entry_id, is_active);

-- Step 3: Create entry status history table
CREATE TABLE IF NOT EXISTS entry_status_history (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  entry_id UUID NOT NULL REFERENCES entries(id) ON DELETE CASCADE,
  rally_id UUID NOT NULL REFERENCES rallies(id) ON DELETE CASCADE,
  stage_id UUID REFERENCES stages(id) ON DELETE CASCADE,
  old_status VARCHAR(20),
  new_status VARCHAR(20) NOT NULL,
  changed_at TIMESTAMPTZ DEFAULT NOW(),
  changed_by UUID REFERENCES users(id),
  reason TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Step 4: Add indexes for entry status history
CREATE INDEX IF NOT EXISTS idx_entry_status_history_entry_id ON entry_status_history(entry_id);
CREATE INDEX IF NOT EXISTS idx_entry_status_history_rally_id ON entry_status_history(rally_id);
CREATE INDEX IF NOT EXISTS idx_entry_status_history_stage_id ON entry_status_history(stage_id);
CREATE INDEX IF NOT EXISTS idx_entry_status_history_changed_at ON entry_status_history(changed_at);

-- Step 5: Update existing results to mark them as active based on entry status
UPDATE results 
SET is_active = (
  SELECT CASE 
    WHEN e.status IN ('retired', 'dns', 'dnf', 'dsq') THEN FALSE
    ELSE TRUE
  END
  FROM entries e 
  WHERE e.id = results.entry_id
)
WHERE is_active IS NULL OR is_active = TRUE;
