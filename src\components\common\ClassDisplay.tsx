import React from 'react';

interface ClassDisplayProps {
  classString: string;
  separator?: string;
  maxDisplay?: number;
  showBadges?: boolean;
  className?: string;
}

const ClassDisplay: React.FC<ClassDisplayProps> = ({
  classString,
  separator = ',',
  maxDisplay = 3,
  showBadges = true,
  className = ''
}) => {
  if (!classString || classString.trim() === '') {
    return <span className={`text-gray-400 italic ${className}`}>No class</span>;
  }

  // Split and clean the classes
  const classes = classString
    .split(separator)
    .map(cls => cls.trim())
    .filter(cls => cls.length > 0);

  if (classes.length === 0) {
    return <span className={`text-gray-400 italic ${className}`}>No class</span>;
  }

  // If showing as badges
  if (showBadges) {
    const displayClasses = classes.slice(0, maxDisplay);
    const remainingCount = classes.length - maxDisplay;

    return (
      <div className={`flex flex-wrap gap-1 ${className}`}>
        {displayClasses.map((cls, index) => (
          <span
            key={index}
            className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
          >
            {cls}
          </span>
        ))}
        {remainingCount > 0 && (
          <span
            className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-300"
            title={`+${remainingCount} more: ${classes.slice(maxDisplay).join(', ')}`}
          >
            +{remainingCount}
          </span>
        )}
      </div>
    );
  }

  // If showing as text
  const displayText = classes.length > maxDisplay 
    ? `${classes.slice(0, maxDisplay).join(', ')} (+${classes.length - maxDisplay} more)`
    : classes.join(', ');

  return (
    <span 
      className={className}
      title={classes.length > maxDisplay ? classes.join(', ') : undefined}
    >
      {displayText}
    </span>
  );
};

// Hook for splitting classes
export const useClassSplit = (classString: string, separator: string = ',') => {
  return React.useMemo(() => {
    if (!classString || classString.trim() === '') {
      return [];
    }
    return classString
      .split(separator)
      .map(cls => cls.trim())
      .filter(cls => cls.length > 0);
  }, [classString, separator]);
};

// Utility function for checking if entry belongs to a specific class
export const entryHasClass = (entryClass: string, targetClass: string, separator: string = ',') => {
  if (!entryClass || !targetClass) return false;
  
  const classes = entryClass
    .split(separator)
    .map(cls => cls.trim().toLowerCase());
  
  return classes.includes(targetClass.toLowerCase());
};

// Component for class filter dropdown
interface ClassFilterProps {
  rallyId: string;
  selectedClass: string;
  onClassChange: (className: string) => void;
  separator?: string;
}

export const ClassFilter: React.FC<ClassFilterProps> = ({
  rallyId,
  selectedClass,
  onClassChange,
  separator = ','
}) => {
  const [classes, setClasses] = React.useState<Array<{class_name: string, entry_count: number}>>([]);
  const [loading, setLoading] = React.useState(false);

  React.useEffect(() => {
    if (!rallyId) return;

    const fetchClasses = async () => {
      setLoading(true);
      try {
        const response = await fetch(`/api/entry-classes/rally/${rallyId}?separator=${encodeURIComponent(separator)}`);
        if (response.ok) {
          const data = await response.json();
          setClasses(data);
        }
      } catch (error) {
        console.error('Failed to fetch classes:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchClasses();
  }, [rallyId, separator]);

  return (
    <select
      value={selectedClass}
      onChange={(e) => onClassChange(e.target.value)}
      disabled={loading}
      className="px-3 py-2 rounded border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
    >
      <option value="">All Classes</option>
      {classes.map(cls => (
        <option key={cls.class_name} value={cls.class_name}>
          {cls.class_name} ({cls.entry_count})
        </option>
      ))}
    </select>
  );
};

export default ClassDisplay;
