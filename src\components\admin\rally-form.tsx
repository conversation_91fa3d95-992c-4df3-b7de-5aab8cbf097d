import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { getCountryCode } from '../../utils/countryUtils';
import ChampionshipSelector from './championship-selector';
import { Championship, RallyChampionship } from '../../types';

interface RallyFormProps {
  initialData?: any;
  championships: Championship[];
  onSubmit?: (formData: any) => Promise<void>;
  onDelete?: () => Promise<void>;
}

const RallyForm: React.FC<RallyFormProps> = ({
  initialData,
  championships,
  onSubmit,
  onDelete
}) => {
  const [form, setForm] = useState({
    name: '',
    country: '',
    start_date: '',
    end_date: '',
    surface: '',
    status: '',
    logo_url: '',
    banner_url: ''
  });
  const [selectedChampionships, setSelectedChampionships] = useState<RallyChampionship[]>([]);
  const [flagCode, setFlagCode] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const navigate = useNavigate();

  useEffect(() => {
    if (initialData) {
      setForm({
        name: initialData.name || '',
        country: initialData.country || '',
        start_date: initialData.start_date ? initialData.start_date.slice(0, 10) : '',
        end_date: initialData.end_date ? initialData.end_date.slice(0, 10) : '',
        surface: initialData.surface || '',
        status: initialData.status || '',
        logo_url: initialData.logo_url || '',
        banner_url: initialData.banner_url || ''
      });

      // Set championships from initialData
      if (initialData.championships && Array.isArray(initialData.championships)) {
        setSelectedChampionships(initialData.championships);
      }
    }
  }, [initialData]);

  // Update flag code when country changes
  useEffect(() => {
    setFlagCode(getCountryCode(form.country));
  }, [form.country]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const validateForm = () => {
    const errors: Record<string, string> = {};

    if (!form.name.trim()) errors.name = 'Rally name is required';
    if (!form.country.trim()) errors.country = 'Country is required';
    if (!form.start_date) errors.start_date = 'Start date is required';
    if (!form.end_date) errors.end_date = 'End date is required';
    if (!form.surface) errors.surface = 'Surface is required';
    if (!form.status) errors.status = 'Status is required';

    // Validate end date is after start date
    if (form.start_date && form.end_date && new Date(form.end_date) < new Date(form.start_date)) {
      errors.end_date = 'End date must be after start date';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    setLoading(true);
    setError('');

    try {
      // Format dates to ISO string to ensure proper timestamp format
      const formattedData = {
        ...form,
        start_date: form.start_date ? new Date(form.start_date).toISOString() : null,
        end_date: form.end_date ? new Date(form.end_date).toISOString() : null
      };

      if (onSubmit) {
        await onSubmit({ ...formattedData, championships: selectedChampionships });
      } else {
        // Default submission logic if no onSubmit provided
        const url = initialData ? `/api/rallies/${initialData.id}` : '/api/rallies';
        const method = initialData ? 'PUT' : 'POST';

        const response = await fetch(url, {
          method,
          headers: { 'Content-Type': 'application/json' },
          credentials: 'include',
          body: JSON.stringify(formattedData)
        });

        const responseData = await response.json();

        if (!response.ok) {
          throw new Error(responseData.message || `Failed to ${initialData ? 'update' : 'add'} rally`);
        }

        // Update rally championships if we have any selected
        if (selectedChampionships.length > 0) {
          const championshipsResponse = await fetch(`/api/rally-championships/rally/${responseData.id}/championships`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            credentials: 'include',
            body: JSON.stringify({ championships: selectedChampionships })
          });

          if (!championshipsResponse.ok) {
            const errorData = await championshipsResponse.json();
            // Don't throw error here, just warn - rally was created successfully
            setError(`Rally saved but failed to update championships: ${errorData.message}`);
          }
        }

        navigate('/admin/rallies');
      }
    } catch (err: any) {
      setError(err.message);
      window.scrollTo(0, 0); // Scroll to top to show error
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!initialData || !onDelete) return;

    if (!window.confirm('Are you sure you want to delete this rally?')) {
      return;
    }

    try {
      await onDelete();
    } catch (err: any) {
      setError(err.message);
    }
  };

  const handleCancel = () => {
    navigate('/admin/rallies');
  };

  // Surface options - use lowercase to match database expectations
  const surfaceOptions = [
    { value: 'tarmac', label: 'Tarmac' },
    { value: 'gravel', label: 'Gravel' },
    { value: 'snow', label: 'Snow' },
    { value: 'mixed', label: 'Mixed' },
  ];

  // Status options must match the rally_status ENUM in the database
  // CREATE TYPE rally_status AS ENUM ('upcoming', 'running', 'finished');
  const statusOptions = [
    { value: 'upcoming', label: 'Upcoming' },
    { value: 'running', label: 'Running' },
    { value: 'finished', label: 'Finished' },
  ];

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      {error && (
        <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Rally Name
            </label>
            <input
              name="name"
              value={form.name}
              onChange={handleChange}
              required
              placeholder="Rally Name"
              className={`w-full px-3 py-2 rounded border ${
                validationErrors.name ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
              } dark:bg-gray-700 dark:text-white`}
            />
            {validationErrors.name && (
              <p className="mt-1 text-sm text-red-500">{validationErrors.name}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Country
            </label>
            <div className="relative">
              <input
                name="country"
                value={form.country}
                onChange={handleChange}
                required
                placeholder="Country"
                className={`w-full px-3 py-2 rounded border ${
                  validationErrors.country ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                } dark:bg-gray-700 dark:text-white ${flagCode ? 'pl-10' : ''}`}
              />
              {flagCode && (
                <span
                  className={`fi fi-${flagCode} absolute left-3 top-1/2 transform -translate-y-1/2`}
                  style={{ width: '20px', height: '15px' }}
                ></span>
              )}
              {validationErrors.country && (
                <p className="mt-1 text-sm text-red-500">{validationErrors.country}</p>
              )}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Start Date
            </label>
            <input
              name="start_date"
              value={form.start_date}
              onChange={handleChange}
              type="date"
              required
              className={`w-full px-3 py-2 rounded border ${
                validationErrors.start_date ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
              } dark:bg-gray-700 dark:text-white`}
            />
            {validationErrors.start_date && (
              <p className="mt-1 text-sm text-red-500">{validationErrors.start_date}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              End Date
            </label>
            <input
              name="end_date"
              value={form.end_date}
              onChange={handleChange}
              type="date"
              required
              className={`w-full px-3 py-2 rounded border ${
                validationErrors.end_date ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
              } dark:bg-gray-700 dark:text-white`}
            />
            {validationErrors.end_date && (
              <p className="mt-1 text-sm text-red-500">{validationErrors.end_date}</p>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Surface
            </label>
            <select
              name="surface"
              value={form.surface}
              onChange={handleChange}
              required
              className={`w-full px-3 py-2 rounded border ${
                validationErrors.surface ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
              } dark:bg-gray-700 dark:text-white`}
            >
              <option value="">Select Surface</option>
              {surfaceOptions.map(option => (
                <option key={option.value} value={option.value}>{option.label}</option>
              ))}
            </select>
            {validationErrors.surface && (
              <p className="mt-1 text-sm text-red-500">{validationErrors.surface}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Status
            </label>
            <select
              name="status"
              value={form.status}
              onChange={handleChange}
              required
              className={`w-full px-3 py-2 rounded border ${
                validationErrors.status ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
              } dark:bg-gray-700 dark:text-white`}
            >
              <option value="">Select Status</option>
              {statusOptions.map(option => (
                <option key={option.value} value={option.value}>{option.label}</option>
              ))}
            </select>
            {validationErrors.status && (
              <p className="mt-1 text-sm text-red-500">{validationErrors.status}</p>
            )}
          </div>


        </div>

        {/* Championship Selector */}
        <ChampionshipSelector
          availableChampionships={championships}
          selectedChampionships={selectedChampionships}
          onChange={setSelectedChampionships}
        />

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Logo URL
            </label>
            <input
              name="logo_url"
              value={form.logo_url}
              onChange={handleChange}
              placeholder="Logo URL"
              className="w-full px-3 py-2 rounded border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Banner URL
            </label>
            <input
              name="banner_url"
              value={form.banner_url}
              onChange={handleChange}
              placeholder="Banner URL"
              className="w-full px-3 py-2 rounded border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
            />
          </div>
        </div>

        <div className="flex justify-between pt-4">
          <div>
            {initialData && onDelete && (
              <button
                type="button"
                onClick={handleDelete}
                className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
              >
                Delete
              </button>
            )}
          </div>
          <div className="flex space-x-3">
            <button
              type="button"
              onClick={handleCancel}
              className="px-4 py-2 border border-gray-300 rounded text-gray-700 bg-white hover:bg-gray-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:hover:bg-gray-600"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors disabled:opacity-50"
            >
              {loading ? (initialData ? 'Saving...' : 'Adding...') : (initialData ? 'Save Changes' : 'Add Rally')}
            </button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default RallyForm;
