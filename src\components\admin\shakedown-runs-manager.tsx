import React, { useState, useEffect } from 'react';
import { Clock, Plus, Trash, RefreshCw, Search } from 'lucide-react';
import { formatTime } from '../../utils/timeUtils.js';

interface Entry {
  id: string;
  number: number;
  driver_first_name?: string;
  driver_last_name?: string;
  codriver_first_name?: string;
  codriver_last_name?: string;
  car?: string;
}

interface ShakedownRun {
  id: string;
  shakedown_id: string;
  entry_id: string;
  run_number: number;
  time: number | null;
  timestamp: string;
  car_number?: number;
  car?: string;
  driver_first_name?: string;
  driver_last_name?: string;
  codriver_first_name?: string;
  codriver_last_name?: string;
}

interface ShakedownRunsManagerProps {
  shakedownId: string;
}

const ShakedownRunsManager: React.FC<ShakedownRunsManagerProps> = ({ shakedownId }) => {
  const [runs, setRuns] = useState<ShakedownRun[]>([]);
  const [entries, setEntries] = useState<Entry[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [formVisible, setFormVisible] = useState(false);
  const [formData, setFormData] = useState({
    entry_id: '',
    run_number: '1',
    time: ''
  });

  useEffect(() => {
    fetchRuns();
    fetchEntries();
  }, [shakedownId]);

  const fetchRuns = async () => {
    setLoading(true);
    try {
      const res = await fetch(`/api/shakedowns/${shakedownId}/runs`, { credentials: 'include' });
      if (!res.ok) throw new Error('Failed to fetch shakedown runs');
      const data = await res.json();
      setRuns(Array.isArray(data) ? data : []);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch shakedown runs');
    } finally {
      setLoading(false);
    }
  };

  const fetchEntries = async () => {
    try {
      // First get the rally ID from the shakedown
      const shakedownRes = await fetch(`/api/shakedowns/${shakedownId}`, { credentials: 'include' });
      if (!shakedownRes.ok) throw new Error('Failed to fetch shakedown details');
      const shakedownData = await shakedownRes.json();

      // Then fetch entries for that rally
      const entriesRes = await fetch(`/api/entries?rally_id=${shakedownData.rally_id}`, { credentials: 'include' });
      if (!entriesRes.ok) throw new Error('Failed to fetch entries');
      const entriesData = await entriesRes.json();

      setEntries(Array.isArray(entriesData) ? entriesData : entriesData.entries || []);
    } catch (err: any) {
      console.error('Failed to fetch entries:', err.message);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      // Convert time from MM:SS.ss format to seconds
      let timeInSeconds = null;
      if (formData.time) {
        const [minutes, seconds] = formData.time.split(':');
        // Ensure we have two decimal places
        const secondsValue = parseFloat(seconds).toFixed(2);
        timeInSeconds = parseInt(minutes) * 60 + parseFloat(secondsValue);
      }

      const payload = {
        shakedown_id: shakedownId,
        entry_id: formData.entry_id,
        run_number: parseInt(formData.run_number),
        time: timeInSeconds
      };

      const res = await fetch('/api/shakedowns/runs', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify(payload)
      });

      if (!res.ok) {
        const errorData = await res.json();
        throw new Error(errorData.message || 'Failed to add shakedown run');
      }

      // Reset form and refresh data
      setFormData({
        entry_id: '',
        run_number: '1',
        time: ''
      });
      setFormVisible(false);
      fetchRuns();
    } catch (err: any) {
      setError(err.message || 'Failed to add shakedown run');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: string) => {
    if (!window.confirm('Are you sure you want to delete this run?')) {
      return;
    }

    setLoading(true);
    try {
      const res = await fetch(`/api/shakedowns/runs/${id}`, {
        method: 'DELETE',
        credentials: 'include'
      });

      if (!res.ok) {
        const errorData = await res.json();
        throw new Error(errorData.message || 'Failed to delete shakedown run');
      }

      fetchRuns();
    } catch (err: any) {
      setError(err.message || 'Failed to delete shakedown run');
    } finally {
      setLoading(false);
    }
  };

  const filteredRuns = runs.filter(run => {
    const searchLower = searchTerm.toLowerCase();
    const driverName = `${run.driver_first_name} ${run.driver_last_name}`.toLowerCase();
    const codriverName = `${run.codriver_first_name} ${run.codriver_last_name}`.toLowerCase();
    const carNumber = run.car_number?.toString() || '';

    return driverName.includes(searchLower) ||
           codriverName.includes(searchLower) ||
           carNumber.includes(searchLower);
  });

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white flex items-center">
          <Clock className="mr-2 h-5 w-5 text-blue-600" />
          Shakedown Runs
        </h2>

        <div className="flex space-x-2">
          <button
            onClick={() => fetchRuns()}
            className="p-2 text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400"
            title="Refresh"
          >
            <RefreshCw size={20} />
          </button>

          <button
            onClick={() => setFormVisible(!formVisible)}
            className="bg-blue-600 text-white px-3 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center"
          >
            <Plus size={18} className="mr-1" />
            Add Run
          </button>
        </div>
      </div>

      {error && (
        <div className="mb-4 p-3 bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-200 rounded-lg">
          {error}
        </div>
      )}

      {formVisible && (
        <div className="mb-6 p-4 border border-gray-200 dark:border-gray-700 rounded-lg bg-gray-50 dark:bg-gray-800">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Add Shakedown Run</h3>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Car/Entry <span className="text-red-500">*</span>
                </label>
                <select
                  name="entry_id"
                  value={formData.entry_id}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                >
                  <option value="">Select Entry</option>
                  {entries.map(entry => (
                    <option key={entry.id} value={entry.id}>
                      #{entry.number} - {entry.driver_first_name} {entry.driver_last_name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Run Number <span className="text-red-500">*</span>
                </label>
                <input
                  type="number"
                  name="run_number"
                  value={formData.run_number}
                  onChange={handleInputChange}
                  min="1"
                  required
                  className="w-full px-3 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Time (MM:SS.ss)
                </label>
                <input
                  type="text"
                  name="time"
                  value={formData.time}
                  onChange={handleInputChange}
                  placeholder="e.g. 2:45.20"
                  pattern="[0-9]+:[0-9]+(\.[0-9]{1,2})?"
                  title="Format: MM:SS.ss (e.g. 2:45.20)"
                  className="w-full px-3 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Leave empty for no time (e.g. DNF)
                </p>
              </div>
            </div>

            <div className="flex justify-end space-x-3 pt-2">
              <button
                type="button"
                onClick={() => setFormVisible(false)}
                className="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600"
              >
                Cancel
              </button>

              <button
                type="submit"
                disabled={loading}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
              >
                {loading ? 'Adding...' : 'Add Run'}
              </button>
            </div>
          </form>
        </div>
      )}

      <div className="mb-4">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
          <input
            type="text"
            placeholder="Search by driver or car number..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          />
        </div>
      </div>

      {loading && !runs.length ? (
        <div className="flex justify-center items-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      ) : (
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Car #
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Driver
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Run
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Time
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Timestamp
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {filteredRuns.length === 0 ? (
                <tr>
                  <td colSpan={6} className="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                    No shakedown runs found
                  </td>
                </tr>
              ) : (
                filteredRuns.map(run => (
                  <tr key={run.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                      {run.car_number}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      <div>
                        <div>{run.driver_first_name} {run.driver_last_name}</div>
                        <div className="text-xs text-gray-400 dark:text-gray-500">{run.car}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      Run {run.run_number}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                      {run.time !== null ? formatTime(run.time) : 'No time'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {new Date(run.timestamp).toLocaleString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button
                        onClick={() => handleDelete(run.id)}
                        className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                      >
                        <Trash size={18} />
                      </button>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default ShakedownRunsManager;
