-- Migration: Add nominal_time and super_rally flags to results table
-- These flags track stage-specific conditions for entries

-- Add nominal_time column to track when entries receive theoretical/nominal times
ALTER TABLE results ADD COLUMN IF NOT EXISTS nominal_time BOOLEAN DEFAULT FALSE;

-- Add super_rally column to track when entries are under Super Rally rules
ALTER TABLE results ADD COLUMN IF NOT EXISTS super_rally BOOLEAN DEFAULT FALSE;

-- Add indexes for performance on queries filtering by these flags
CREATE INDEX IF NOT EXISTS idx_results_nominal_time ON results(nominal_time);
CREATE INDEX IF NOT EXISTS idx_results_super_rally ON results(super_rally);
CREATE INDEX IF NOT EXISTS idx_results_flags ON results(nominal_time, super_rally);

-- Add comments to document the new columns
COMMENT ON COLUMN results.nominal_time IS 'True if this result represents a nominal/theoretical time (e.g., when driver did not complete the stage)';
COMMENT ON COLUMN results.super_rally IS 'True if the entry was competing under Super Rally rules for this stage';
