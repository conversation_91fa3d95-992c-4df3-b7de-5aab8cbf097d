-- Seed some sample championships for testing EWRC import
-- These championships match the ones commonly found in Greek rally data

-- First, add a unique constraint to prevent duplicates (if it doesn't exist)
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints
    WHERE table_name = 'championships'
    AND constraint_name = 'unique_championship_name_year'
  ) THEN
    ALTER TABLE championships ADD CONSTRAINT unique_championship_name_year UNIQUE (name, year);
  END IF;
END $$;

-- Now insert championships with proper conflict handling
INSERT INTO championships (name, year, type, description) VALUES
('Greece', 2025, 'national', 'Greek National Rally Championship 2025'),
('Historic (GR)', 2025, 'national', 'Greek Historic Rally Championship 2025'),
('Rally3 (GR)', 2025, 'national', 'Greek Rally3 Championship 2025'),
('Historic Gravel Cup (GR)', 2025, 'local', 'Greek Historic Gravel Cup 2025'),
('WRC', 2025, 'international', 'World Rally Championship 2025'),
('ERC', 2025, 'international', 'European Rally Championship 2025')
ON CONFLICT (name, year) DO NOTHING;

-- Show the inserted championships
SELECT * FROM championships ORDER BY type, name;
