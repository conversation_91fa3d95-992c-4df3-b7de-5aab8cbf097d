import express from 'express';
import pool from '../config/db.js';
import { verifyToken, verifyAdmin } from '../middleware/auth.js';

const router = express.Router();

// Get all news (with author name) - PUBLIC ROUTE
router.get('/', async (req, res) => {
  try {
    const result = await pool.query(`
      SELECT n.*, u.name AS author_name
      FROM news n
      JOIN users u ON n.author_id = u.id
      ORDER BY n.created_at DESC
    `);
    res.json(result.rows);
  } catch (error) {
    res.status(500).json({ message: 'Failed to fetch news' });
  }
});

// Get news by id (with author name) - PUBLIC ROUTE
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const result = await pool.query(`
      SELECT n.*, u.name AS author_name
      FROM news n
      JOIN users u ON n.author_id = u.id
      WHERE n.id = $1
    `, [id]);
    if (result.rows.length === 0) return res.status(404).json({ message: 'Not found' });
    res.json(result.rows[0]);
  } catch (error) {
    res.status(500).json({ message: 'Failed to fetch news' });
  }
});

// Add news
router.post('/', verifyToken, verifyAdmin, async (req, res) => {
  try {
    // Log the incoming request body for debugging
    console.log('POST /api/news body:', req.body);
    const { title, content, excerpt, image_url } = req.body;
    if (!title || !content || !excerpt || !image_url) {
      return res.status(400).json({ message: 'Missing required field(s): title, content, excerpt, image_url are all required.' });
    }
    const author_id = req.user.id;
    const result = await pool.query(
      `INSERT INTO news (title, content, excerpt, author_id, image_url) VALUES ($1, $2, $3, $4, $5) RETURNING *`,
      [title, content, excerpt, author_id, image_url]
    );
    res.status(201).json(result.rows[0]);
  } catch (error) {
    console.error('Error in POST /api/news:', error);
    res.status(500).json({ message: 'Failed to add news' });
  }
});

// Update news
router.put('/:id', verifyToken, verifyAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { title, content, excerpt, image_url } = req.body;
    const updated_at = new Date();
    const result = await pool.query(
      `UPDATE news SET title=$1, content=$2, excerpt=$3, image_url=$4, updated_at=$5 WHERE id=$6 RETURNING *`,
      [title, content, excerpt, image_url, updated_at, id]
    );
    if (result.rows.length === 0) return res.status(404).json({ message: 'Not found' });
    res.json(result.rows[0]);
  } catch (error) {
    res.status(500).json({ message: 'Failed to update news' });
  }
});

// Delete news
router.delete('/:id', verifyToken, verifyAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const result = await pool.query('DELETE FROM news WHERE id = $1 RETURNING *', [id]);
    if (result.rows.length === 0) return res.status(404).json({ message: 'Not found' });
    res.json({ message: 'News deleted', news: result.rows[0] });
  } catch (error) {
    res.status(500).json({ message: 'Failed to delete news' });
  }
});

export default router;
