import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, Link } from 'react-router-dom';
import { useRallyContext } from '../context/RallyContext';
import {
  Calendar, MapPin, Clock, Users, Award, Flag, AlertTriangle,
  ChevronRight, Timer, BarChart, Route
} from 'lucide-react';
import { Rally, Stage, Entry, Result, ChampionshipOverallClassification, ChampionshipRally } from '../types';
import { getCountryCode } from '../utils/countryUtils.js';
import { getImageUrl, DEFAULT_RALLY_BANNER } from '../utils/imageUtils';
import { formatTime, formatPenalty, calculateTotalTime } from '../utils/timeUtils.js';
import { ShakedownResults } from '../components/rallies/ShakedownResults';
import { ItineraryDisplay } from '../components/rallies/ItineraryDisplay';
import ClassDisplay from '../components/common/ClassDisplay';
import ChampionshipSelector from '../components/championships/ChampionshipSelector';
import ChampionshipResultsTable from '../components/championships/ChampionshipResultsTable';

const RallyDetailsPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const {
    rallies,
    stages,
    entries,
    results,
    loading,
    fetchStages,
    fetchEntries,
    fetchResults
  } = useRallyContext();
  const [activeTab, setActiveTab] = useState<'results' | 'stages' | 'entries' | 'itinerary' | 'shakedown'>('results');
  const [selectedStage, setSelectedStage] = useState<string | null>(null);
  const [rallyStages, setRallyStages] = useState<Stage[]>([]);
  const [rallyEntries, setRallyEntries] = useState<Entry[]>([]);
  const [rallyResults, setRallyResults] = useState<Result[]>([]);
  const [loadingData, setLoadingData] = useState<boolean>(true);

  // Championship-related state
  const [selectedChampionshipId, setSelectedChampionshipId] = useState<string | null>(null);
  const [championshipResults, setChampionshipResults] = useState<ChampionshipOverallClassification[]>([]);
  const [championshipName, setChampionshipName] = useState<string>('');
  const [loadingChampionshipResults, setLoadingChampionshipResults] = useState<boolean>(false);

  const rally = rallies.find(r => r.id === id);

  // Fetch championship results
  const fetchChampionshipResults = async (championshipId: string | null) => {
    if (!id) return;

    setLoadingChampionshipResults(true);
    try {
      const url = championshipId
        ? `/api/championship-results/rallies/${id}/results?championshipId=${championshipId}`
        : `/api/championship-results/rallies/${id}/results`;

      const response = await fetch(url);
      if (!response.ok) throw new Error('Failed to fetch championship results');

      const data = await response.json();
      setChampionshipResults(data);

      // Set championship name if specific championship is selected
      if (championshipId && data.length > 0) {
        setChampionshipName(data[0].championship_name || '');
      } else {
        setChampionshipName('');
      }
    } catch (error) {
      console.error('Error fetching championship results:', error);
      setChampionshipResults([]);
      setChampionshipName('');
    } finally {
      setLoadingChampionshipResults(false);
    }
  };

  // Handle championship selection change
  const handleChampionshipChange = (championshipId: string | null) => {
    setSelectedChampionshipId(championshipId);
    fetchChampionshipResults(championshipId);
  };

  // Fetch championship results when component loads
  useEffect(() => {
    if (id && !selectedChampionshipId) {
      fetchChampionshipResults(null);
    }
  }, [id]);

  // Fetch rally-specific data when the rally is found
  useEffect(() => {
    const loadRallyData = async () => {
      if (rally) {
        setLoadingData(true);

        // Only fetch data if we don't already have it
        const fetchPromises = [];

        if (stages.length === 0) {
          fetchPromises.push(fetchStages());
        }

        if (entries.length === 0) {
          fetchPromises.push(fetchEntries());
        }

        if (results.length === 0) {
          fetchPromises.push(fetchResults());
        }

        if (fetchPromises.length > 0) {
          await Promise.all(fetchPromises);
        }

        setLoadingData(false);
      }
    };

    loadRallyData();
    // We're intentionally only running this when the rally changes
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [rally]);

  // Filter stages, entries, and results for this specific rally
  useEffect(() => {
    if (rally && !loadingData) {
      setRallyStages(stages.filter(stage => stage.rally_id === rally.id));
      setRallyEntries(entries.filter(entry => entry.rally_id === rally.id));
      setRallyResults(results.filter(result => result.rally_id === rally.id));
    }
  }, [rally, stages, entries, results, loadingData]);

  if (loading || loadingData) {
    return (
      <div className="flex justify-center items-center min-h-screen pt-16">
        <div className="inline-block animate-spin rounded-full h-8 w-8 border-4 border-gray-300 border-t-red-600"></div>
        <p className="ml-2">Loading rally details...</p>
      </div>
    );
  }

  if (!rally) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen pt-16 px-4">
        <div className="text-center">
          <AlertTriangle className="w-12 h-12 text-red-600 mb-4 mx-auto" />
          <h2 className="text-2xl font-bold mb-2">Rally Not Found</h2>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            The rally you're looking for doesn't exist or has been removed.
          </p>
          <Link
            to="/rallies"
            className="bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded-lg flex items-center justify-center w-40 mx-auto"
          >
            <Flag className="w-4 h-4 mr-2" />
            All Rallies
          </Link>
        </div>
      </div>
    );
  }

  // Format functions
  const formatDate = (dateString: string) => {
    const options: Intl.DateTimeFormatOptions = { day: 'numeric', month: 'short', year: 'numeric' };
    return new Date(dateString).toLocaleDateString('en-GB', options);
  };



  // Helper to get ordinal suffix (1st, 2nd, 3rd, 4th, etc.)
  const getOrdinalSuffix = (position: number): string => {
    if (!position) return '';

    const j = position % 10;
    const k = position % 100;

    if (j === 1 && k !== 11) {
      return 'st';
    }
    if (j === 2 && k !== 12) {
      return 'nd';
    }
    if (j === 3 && k !== 13) {
      return 'rd';
    }
    return 'th';
  };

  // Group stages by day for itinerary
  const getStagesByDay = () => {
    if (!rallyStages.length) return [];

    const stagesByDay: { date: string; stages: Stage[] }[] = [];

    rallyStages.forEach(stage => {
      const stageDate = new Date(stage.start_time).toISOString().split('T')[0];

      const existingDay = stagesByDay.find(day => day.date === stageDate);
      if (existingDay) {
        existingDay.stages.push(stage);
      } else {
        stagesByDay.push({
          date: stageDate,
          stages: [stage]
        });
      }
    });

    // Sort days chronologically
    stagesByDay.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

    // Sort stages within each day by number
    stagesByDay.forEach(day => {
      day.stages.sort((a, b) => a.number - b.number);
    });

    return stagesByDay;
  };

  // Get current stage for running rallies
  const currentStage = rally.status === 'running' && rallyStages.length > 0
    ? rallyStages.find(stage => stage.status === 'running')
    : null;

  // Calculate total times from stage results (like before) since database view might be filtering incorrectly
  const entriesWithPosition = rallyEntries.map(entry => {
    // Calculate position based on total time from results
    const entryResults = rallyResults.filter(result => result.entry_id === entry.id);

    // Only calculate total time if there are valid results (excluding DNF results with time = -1)
    let totalTime = 0;
    if (entryResults.length > 0) {
      // Filter out DNF results and make sure all times are valid numbers
      const validResults = entryResults.filter(result =>
        result.time !== undefined &&
        result.time !== null &&
        result.time !== -1 &&
        !isNaN(Number(result.time))
      );

      // Sum up all valid times
      totalTime = validResults.reduce((sum, result) => {
        const time = typeof result.time === 'string' ? parseFloat(result.time) : result.time;
        return sum + (time || 0);
      }, 0);
    }

    // Keep original status but track if they have results
    const hasResults = entryResults.length > 0 && totalTime > 0;



    return {
      ...entry,
      // Use calculated total time instead of database value
      totalTime: hasResults ? totalTime : undefined,
      dbTotalTime: entry.total_time, // Keep database value for comparison
      dbPosition: entry.position, // Keep database position for reference
      hasResults
    };
  });

  // Get the total number of stages for this rally
  const totalStages = rallyStages.length;

  // Separate entries into active and retired
  const validStatuses = ['upcoming', 'running', 'finished'];
  const activeEntries = entriesWithPosition.filter(entry => {
    // Must have valid status
    const hasValidStatus = validStatuses.includes(entry.status) ||
      (entry.hasResults && !['retired', 'dns', 'dnf', 'dsq'].includes(entry.status));

    if (!hasValidStatus) return false;

    // Must have completed ALL stages to be ranked
    if (entry.hasResults) {
      const entryResults = rallyResults.filter(result => result.entry_id === entry.id);
      const validResults = entryResults.filter(result =>
        result.time !== undefined &&
        result.time !== null &&
        result.time !== -1 &&
        !isNaN(Number(result.time))
      );

      // Only include entries that completed all stages
      const completedAllStages = validResults.length === totalStages;
      if (!completedAllStages) {
        console.log(`Entry #${entry.number} (${entry.driver_first_name} ${entry.driver_last_name}) excluded: completed ${validResults.length}/${totalStages} stages`);
      }
      return completedAllStages;
    }

    return true;
  });

  // Get retired entries (those with retired status)
  const retiredEntries = entriesWithPosition.filter(entry =>
    ['retired', 'dns', 'dnf', 'dsq'].includes(entry.status)
  );

  // Sort active entries by total time and assign correct positions
  const sortedActiveEntries = [...activeEntries]
    .sort((a, b) => {
      // Sort by total time (fastest first), nulls last
      if (a.totalTime === null || a.totalTime === undefined) return 1;
      if (b.totalTime === null || b.totalTime === undefined) return -1;
      if (a.totalTime !== b.totalTime) return a.totalTime - b.totalTime;
      return a.number - b.number;
    });

  // Assign correct positions to active entries
  let currentPosition = 1;
  let entriesProcessed = 0;

  const entriesWithCorrectPositions = sortedActiveEntries.map((entry, index) => {
    if (entry.totalTime !== null && entry.totalTime !== undefined) {
      // Check if this entry has the same time as the previous entry
      let prevEntry = null;
      for (let i = index - 1; i >= 0; i--) {
        if (sortedActiveEntries[i].totalTime !== null && sortedActiveEntries[i].totalTime !== undefined) {
          prevEntry = sortedActiveEntries[i];
          break;
        }
      }

      if (prevEntry && Math.abs((entry.totalTime || 0) - (prevEntry.totalTime || 0)) < 0.001) {
        // Same time as previous entry, assign same position
        entry.position = prevEntry.position;
      } else {
        // Different time, assign next available position
        entry.position = entriesProcessed + 1;
      }
      entriesProcessed++;
    } else {
      entry.position = undefined;
    }
    return entry;
  });



  // Sort retired entries by entry number for consistent display
  const sortedRetiredEntries = [...retiredEntries]
    .sort((a, b) => a.number - b.number);

  // Combine active and retired entries for display
  const sortedEntries = [...entriesWithCorrectPositions, ...sortedRetiredEntries];

  // Note: Positions are now correctly calculated for frontend display
  // Retired entries don't get positions in overall classification

  // Stage selection handler
  const handleStageSelect = (stageId: string) => {
    setSelectedStage(selectedStage === stageId ? null : stageId);
    setActiveTab('results');
  };

  // Get selected stage
  const activeStage = rallyStages.length > 0
    ? (selectedStage
        ? rallyStages.find(stage => stage.id === selectedStage)
        : rallyStages[rallyStages.length - 1])
    : null;

  // Get results for selected stage
  const getStageResults = (stageId: string) => {
    if (!rallyEntries.length) return [];

    // Get all results for this stage
    const stageResults = rallyResults.filter(result => result.stage_id === stageId);



    // Find the fastest time for this stage (excluding DNF results with time = -1)
    const validResults = stageResults.filter(result => result.time !== -1 && result.time > 0);
    const fastestResult = validResults.length > 0
      ? validResults.reduce((fastest: Result | null, current: Result) => {
          // Convert to numbers to ensure proper comparison
          const fastestTime = fastest ? (typeof fastest.time === 'string' ? parseFloat(fastest.time) : fastest.time) : Infinity;
          const currentTime = typeof current.time === 'string' ? parseFloat(current.time) : current.time;

          return currentTime < fastestTime ? current : fastest;
        }, null)
      : null;



    // Map entries to their results
    let entriesWithResults = rallyEntries.map(entry => {
      const result = stageResults.find(r => r.entry_id === entry.id);

      // Calculate time difference
      let diff;
      // Calculate diff only for valid results (not DNF)
      if (result && fastestResult && result.time !== -1 && result.time > 0) {
        try {
          // Make sure we're working with numbers
          const resultTime = typeof result.time === 'string' ? parseFloat(result.time) : result.time;
          const fastestTime = typeof fastestResult.time === 'string' ? parseFloat(fastestResult.time) : fastestResult.time;

          // Check for valid numbers
          if (resultTime === undefined || isNaN(resultTime) || fastestTime === undefined || isNaN(fastestTime)) {
            diff = undefined;
          } else {
            // In rally timing, the time difference is always positive (slower time - faster time)
            const rawDiff = resultTime - fastestTime;
            diff = parseFloat(Math.max(0, rawDiff).toFixed(2));

            // If this IS the fastest result, set diff to 0 explicitly
            if (Math.abs(rawDiff) < 0.001) {
              diff = 0;
            }
          }
        } catch (error) {
          console.error('Error calculating time difference:', error);
          diff = undefined;
        }
      }

      return {
        entry,
        result,
        // We'll calculate position after sorting
        position: undefined as number | undefined,
        // Calculate time difference from fastest (in seconds)
        diff,
        // Pass through the entry status
        status: entry.status
      };
    });

    // Sort by time (fastest first) and put DNF/no results at the end
    entriesWithResults = entriesWithResults
      .sort((a, b) => {
        // Handle DNF results (time = -1) - put them at the end
        const aIsDNF = a.result && a.result.time === -1;
        const bIsDNF = b.result && b.result.time === -1;
        const aHasValidResult = a.result && a.result.time !== -1 && a.result.time > 0;
        const bHasValidResult = b.result && b.result.time !== -1 && b.result.time > 0;

        // First priority: Valid results come first
        if (aHasValidResult && !bHasValidResult) return -1;
        if (!aHasValidResult && bHasValidResult) return 1;

        // If both have valid results, sort by time (fastest first)
        if (aHasValidResult && bHasValidResult) {
          return a.result.time - b.result.time;
        }

        // Second priority: No results come before DNF
        const aHasNoResult = !a.result;
        const bHasNoResult = !b.result;

        if (aHasNoResult && bIsDNF) return -1;
        if (aIsDNF && bHasNoResult) return 1;

        // If both are DNF, sort by entry number
        if (aIsDNF && bIsDNF) {
          return parseInt(a.entry.number.toString()) - parseInt(b.entry.number.toString());
        }

        // If both have no results, sort by entry number
        if (aHasNoResult && bHasNoResult) {
          return parseInt(a.entry.number.toString()) - parseInt(b.entry.number.toString());
        }

        // Default: sort by entry number
        return parseInt(a.entry.number.toString()) - parseInt(b.entry.number.toString());
      });

    // Assign positions based on sorted order (only for valid results)
    let currentPosition = 1;
    let entriesProcessed = 0;

    entriesWithResults.forEach((item, index) => {
      // Assign positions only to entries with valid results (not DNF, not no result)
      if (item.result && item.result.time !== -1 && item.result.time > 0) {
        const currentTime = typeof item.result.time === 'string' ? parseFloat(item.result.time) : item.result.time;

        // Look for the previous entry with a valid result to compare times
        let prevValidEntry = null;
        for (let i = index - 1; i >= 0; i--) {
          if (entriesWithResults[i].result &&
              entriesWithResults[i].result.time !== -1 &&
              entriesWithResults[i].result.time > 0 &&
              entriesWithResults[i].position) {
            prevValidEntry = entriesWithResults[i];
            break;
          }
        }

        if (prevValidEntry) {
          const prevTime = typeof prevValidEntry.result.time === 'string'
            ? parseFloat(prevValidEntry.result.time)
            : prevValidEntry.result.time;

          // Compare with a small tolerance to account for floating point precision
          if (Math.abs(currentTime - prevTime) < 0.001) {
            // Same time as previous entry, assign same position
            item.position = prevValidEntry.position;
          } else {
            // Different time, assign next available position
            item.position = entriesProcessed + 1;
          }
        } else {
          // This is the first valid result
          item.position = 1;
        }
        entriesProcessed++;
      } else {
        // For entries with DNF or no result, don't assign a position
        item.position = undefined;
      }
    });



    return entriesWithResults;
  };

  return (
    <div className="pt-16 pb-12 bg-gray-50 dark:bg-gray-900">
      {/* Rally Header */}
      {/* Rally banner image */}
      <div
        className="bg-cover bg-center h-64 relative"
        style={{ backgroundImage: `url('${getImageUrl(rally.banner_url, DEFAULT_RALLY_BANNER)}')` }}
      >
        {/* Status badge at the top */}
        <div className="absolute top-4 right-4 z-20">
          {rally.status === 'upcoming' && (
            <div className="bg-blue-600 text-white px-4 py-2 rounded-full flex items-center">
              <Calendar className="w-4 h-4 mr-2" />
              <span className="font-medium">Upcoming</span>
            </div>
          )}
          {rally.status === 'running' && (
            <div className="bg-red-600 text-white px-4 py-2 rounded-full flex items-center">
              <Clock className="w-4 h-4 mr-2 animate-pulse" />
              <span className="font-medium">Running</span>
            </div>
          )}
          {rally.status === 'finished' && (
            <div className="bg-green-600 text-white px-4 py-2 rounded-full flex items-center">
              <Flag className="w-4 h-4 mr-2" />
              <span className="font-medium">Finished</span>
            </div>
          )}
        </div>

        <div className="absolute inset-0 bg-gradient-to-t from-gray-900 to-transparent"></div>
        <div className="container mx-auto px-4 h-full flex items-end pb-6 relative z-10">
          <div className="flex items-center">
            <div
              className="w-10 h-7 rounded border border-white/30 mr-4 bg-gray-700 flex items-center justify-center text-xs text-white overflow-hidden"
            >
              {rally.country && (
                <span
                  className={`fi fi-${getCountryCode(rally.country)}`}
                  style={{ width: '100%', height: '100%', objectFit: 'cover' }}
                ></span>
              )}
            </div>
            <div>
              <h1 className="text-white text-3xl font-bold">{rally.name}</h1>
              <div className="flex items-center text-gray-200 mt-1">
                <Calendar className="w-4 h-4 mr-1" />
                <span className="text-sm mr-3">
                  {formatDate(rally.start_date)} - {formatDate(rally.end_date)}
                </span>
                <MapPin className="w-4 h-4 mr-1" />
                <span className="text-sm flex items-center">
                  {rally.country && (
                    <span
                      className={`fi fi-${getCountryCode(rally.country)} mr-1.5`}
                      style={{ width: '14px', height: '10px' }}
                    ></span>
                  )}
                  {rally.country}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Rally Info Bar */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 shadow-sm">
        <div className="container mx-auto px-4">
          <div className="flex flex-wrap items-center py-4 text-sm">
            <div className="mr-6 mb-2 md:mb-0">
              <span className="text-gray-600 dark:text-gray-400 mr-1">Surface:</span>
              <span className={`px-2 py-1 text-xs rounded-full ${
                rally.surface === 'gravel'
                  ? 'bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-200'
                  : rally.surface === 'tarmac'
                  ? 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
                  : rally.surface === 'snow'
                  ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                  : 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
              }`}>
                {rally.surface.charAt(0).toUpperCase() + rally.surface.slice(1)}
              </span>
            </div>

            <div className="mr-6 mb-2 md:mb-0 flex items-center">
              <Flag className="w-4 h-4 text-gray-600 dark:text-gray-400 mr-1" />
              <span className="text-gray-600 dark:text-gray-400 mr-1">Stages:</span>
              <span className="font-medium text-gray-900 dark:text-white">{rallyStages.length}</span>
            </div>

            <div className="mr-6 mb-2 md:mb-0 flex items-center">
              <Users className="w-4 h-4 text-gray-600 dark:text-gray-400 mr-1" />
              <span className="text-gray-600 dark:text-gray-400 mr-1">Entries:</span>
              <span className="font-medium text-gray-900 dark:text-white">{rallyEntries.length}</span>
            </div>

            <div className="mr-6 mb-2 md:mb-0 flex items-center">
              <Route className="w-4 h-4 text-gray-600 dark:text-gray-400 mr-1" />
              <span className="text-gray-600 dark:text-gray-400 mr-1">Total:</span>
              <span className="font-medium text-gray-900 dark:text-white">
                {rallyStages.reduce((sum: number, stage) => sum + parseFloat(stage.length.toString()), 0).toFixed(2)} km
              </span>
            </div>

            {currentStage && (
              <div className="flex items-center text-red-600 dark:text-red-400 ml-auto">
                <Timer className="w-4 h-4 mr-1" />
                <span className="font-medium">SS{currentStage.number}: {currentStage.name}</span>
                <span className="ml-2 text-xs bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 px-2 py-0.5 rounded-full">
                  Running Stage
                </span>
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-6">
        {/* Tabs */}
        <div className="flex border-b border-gray-200 dark:border-gray-700 mb-6">
          <button
            className={`pb-3 px-4 font-medium text-sm border-b-2 ${
              activeTab === 'results'
                ? 'border-red-600 text-red-600 dark:text-red-500'
                : 'border-transparent text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
            }`}
            onClick={() => setActiveTab('results')}
          >
            <BarChart className="w-4 h-4 inline mr-1" />
            Results
          </button>
          <button
            className={`pb-3 px-4 font-medium text-sm border-b-2 ${
              activeTab === 'stages'
                ? 'border-red-600 text-red-600 dark:text-red-500'
                : 'border-transparent text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
            }`}
            onClick={() => setActiveTab('stages')}
          >
            <Flag className="w-4 h-4 inline mr-1" />
            Stages
          </button>
          <button
            className={`pb-3 px-4 font-medium text-sm border-b-2 ${
              activeTab === 'itinerary'
                ? 'border-red-600 text-red-600 dark:text-red-500'
                : 'border-transparent text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
            }`}
            onClick={() => setActiveTab('itinerary')}
          >
            <Route className="w-4 h-4 inline mr-1" />
            Itinerary
          </button>
          <button
            className={`pb-3 px-4 font-medium text-sm border-b-2 ${
              activeTab === 'entries'
                ? 'border-red-600 text-red-600 dark:text-red-500'
                : 'border-transparent text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
            }`}
            onClick={() => setActiveTab('entries')}
          >
            <Users className="w-4 h-4 inline mr-1" />
            Entries
          </button>
          <button
            className={`pb-3 px-4 font-medium text-sm border-b-2 ${
              activeTab === 'shakedown'
                ? 'border-red-600 text-red-600 dark:text-red-500'
                : 'border-transparent text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
            }`}
            onClick={() => setActiveTab('shakedown')}
          >
            <Clock className="w-4 h-4 inline mr-1" />
            Shakedown
          </button>
        </div>

        {/* Tab Content */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Sidebar */}
          <div className="lg:col-span-1 order-2 lg:order-1">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
              <div className="px-4 py-3 bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600">
                <h2 className="font-medium text-gray-900 dark:text-white flex items-center">
                  <Flag className="w-4 h-4 mr-1" />
                  Stages
                </h2>
              </div>
              <div className="divide-y divide-gray-200 dark:divide-gray-700">
                {rallyStages.map((stage) => (
                  <button
                    key={stage.id}
                    onClick={() => handleStageSelect(stage.id)}
                    className={`w-full px-4 py-3 flex items-center justify-between text-left hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${
                      selectedStage === stage.id ? 'bg-gray-50 dark:bg-gray-700' : ''
                    }`}
                  >
                    <div>
                      <div className="font-medium text-gray-900 dark:text-white flex items-center">
                        <span className="w-8">SS{stage.number}</span>
                        {stage.status === 'running' && (
                          <span className="ml-2 animate-pulse text-red-600">•</span>
                        )}
                        {stage.status === 'finished' && (
                          <span className="ml-2 bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400 px-1.5 py-0.5 rounded-full text-xs flex items-center">
                            <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                            </svg>
                          </span>
                        )}
                        {stage.status === 'upcoming' && (
                          <span className="ml-2 bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 px-1.5 py-0.5 rounded-full text-xs flex items-center">
                            <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                            </svg>
                          </span>
                        )}
                        {stage.status === 'canceled' && (
                          <span className="ml-2 bg-red-100 dark:bg-red-900 text-red-600 dark:text-red-400 px-1.5 py-0.5 rounded-full text-xs flex items-center">
                            <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                            </svg>
                          </span>
                        )}
                      </div>
                      <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">{stage.name}</div>
                      <div className="text-xs text-gray-500 dark:text-gray-500 mt-0.5 flex items-center">
                        <span>{stage.length} km</span>
                        {stage.is_super_special && (
                          <span className="ml-2 bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 px-1.5 py-0.5 rounded text-xs">
                            SSS
                          </span>
                        )}
                        {stage.is_power_stage && (
                          <span className="ml-2 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-1.5 py-0.5 rounded text-xs">
                            Power
                          </span>
                        )}
                      </div>
                    </div>
                    <ChevronRight className={`w-4 h-4 text-gray-400 transition-transform ${
                      selectedStage === stage.id ? 'transform rotate-90' : ''
                    }`} />
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3 order-1 lg:order-2">
            {activeTab === 'results' && (
              <div className="space-y-6">
                {/* Championship Selector - Only show for overall results */}
                {!selectedStage && id && (
                  <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
                    <ChampionshipSelector
                      rallyId={id}
                      selectedChampionshipId={selectedChampionshipId}
                      onChampionshipChange={handleChampionshipChange}
                      className="max-w-md"
                    />
                  </div>
                )}

                <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
                  <div className="px-6 py-4 bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600">
                    <div className="flex justify-between items-center">
                      <h2 className="font-medium text-lg text-gray-900 dark:text-white">
                        {selectedStage && activeStage ? (
                          <>SS{activeStage.number}: {activeStage.name}</>
                        ) : selectedChampionshipId && championshipName ? (
                          <>{championshipName} Results</>
                        ) : (
                          <>Overall Results</>
                        )}
                      </h2>
                    {activeStage?.status === 'running' && (
                      <span className="bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 px-3 py-1.5 rounded-full text-sm font-medium flex items-center shadow-sm">
                        <Clock className="w-4 h-4 mr-1.5 animate-pulse" />
                        Running
                      </span>
                    )}
                    {activeStage?.status === 'finished' && (
                      <span className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-3 py-1.5 rounded-full text-sm font-medium flex items-center shadow-sm">
                        <svg className="w-4 h-4 mr-1.5" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                        Finished
                      </span>
                    )}
                    {activeStage?.status === 'upcoming' && (
                      <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-3 py-1.5 rounded-full text-sm font-medium flex items-center shadow-sm">
                        <svg className="w-4 h-4 mr-1.5" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                        </svg>
                        Upcoming
                      </span>
                    )}
                    {activeStage?.status === 'canceled' && (
                      <span className="bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 px-3 py-1.5 rounded-full text-sm font-medium flex items-center shadow-sm">
                        <svg className="w-4 h-4 mr-1.5" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                        </svg>
                        Canceled
                      </span>
                    )}
                  </div>
                  {activeStage && (
                    <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                      {activeStage.length} km • {activeStage.surface.charAt(0).toUpperCase() + activeStage.surface.slice(1)}
                    </div>
                  )}
                </div>

                {/* Show canceled message for canceled stages */}
                {activeStage?.status === 'canceled' ? (
                  <div className="px-6 py-16 text-center">
                    <div className="mx-auto w-20 h-20 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center mb-6 shadow-lg">
                      <svg className="w-10 h-10 text-red-600 dark:text-red-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                      Stage Canceled
                    </h3>
                    <p className="text-gray-600 dark:text-gray-400 mb-6 max-w-md mx-auto leading-relaxed">
                      <span className="font-medium">SS{activeStage.number}: {activeStage.name}</span> has been canceled and no results are available.
                    </p>
                    <div className="inline-flex items-center px-4 py-2 bg-gray-50 dark:bg-gray-700 rounded-full text-sm text-gray-600 dark:text-gray-400">
                      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      {activeStage.length} km • {activeStage.surface.charAt(0).toUpperCase() + activeStage.surface.slice(1)} surface
                    </div>
                  </div>
                ) : selectedStage ? (
                  // Stage-specific results (existing logic)
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead className="bg-gray-50 dark:bg-gray-800">
                      <tr>
                        <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider w-12">
                          Pos
                        </th>
                        <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider w-12">
                          #
                        </th>
                        <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Driver / Codriver
                        </th>
                        <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider hidden md:table-cell">
                          Car
                        </th>
                        <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider hidden lg:table-cell">
                          Class
                        </th>
                        <th scope="col" className="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Time
                        </th>
                        <th scope="col" className="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Diff
                        </th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                      {getStageResults(selectedStage).map((item: any) => (
                          <tr key={item.entry.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                            <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-700 dark:text-gray-300">
                              {(() => {
                                // If the entry has a result for this stage
                                if (item.result) {
                                  // If it's a DNF result (time = -1), show DNF
                                  if (item.result.time === -1) {
                                    return <span className="text-red-600 dark:text-red-400">DNF</span>;
                                  }

                                  // If it's a valid result, show position
                                  if (item.position) {
                                    // Check if this entry has the same time as the previous entry with a valid result
                                    const allResults = getStageResults(selectedStage);
                                    const currentIndex = allResults.findIndex(i => i.entry.id === item.entry.id);

                                    // Find the previous entry with a valid result
                                    let prevValidEntry = null;
                                    for (let i = currentIndex - 1; i >= 0; i--) {
                                      if (allResults[i].result && allResults[i].result.time !== -1 && allResults[i].position) {
                                        prevValidEntry = allResults[i];
                                        break;
                                      }
                                    }

                                    const hasSameTimeAsPrevious = prevValidEntry &&
                                      Math.abs(prevValidEntry.result.time - item.result.time) < 0.001;

                                    // Add equals sign for tied positions
                                    return hasSameTimeAsPrevious ? '=' + item.position : item.position;
                                  }
                                  return '-';
                                }

                                // If no result but entry has invalid status, show the status code
                                const invalidStatuses = ['retired', 'dns', 'dnf', 'dsq'];
                                const isInvalidStatus = invalidStatuses.includes(item.status);

                                if (isInvalidStatus) {
                                  return <span className="text-red-600 dark:text-red-400">{item.status.substring(0, 3).toUpperCase()}</span>;
                                } else {
                                  return '-';
                                }
                              })()}
                            </td>
                            <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                              {item.entry.number}
                            </td>
                            <td className="px-4 py-3 whitespace-nowrap">
                              <div>
                                <div className="text-sm font-medium text-gray-900 dark:text-white flex items-center">
                                  {item.entry.driver_nationality && (
                                    <span
                                      className={`fi fi-${getCountryCode(item.entry.driver_nationality)} mr-2`}
                                      style={{ width: '16px', height: '12px' }}
                                    ></span>
                                  )}
                                  {item.entry.driver_first_name} {item.entry.driver_last_name}
                                  {item.result?.super_rally && (
                                    <span className="ml-1 text-xs text-blue-600 dark:text-blue-400 font-medium">[SR]</span>
                                  )}
                                </div>
                                <div className="text-sm text-gray-600 dark:text-gray-400 flex items-center">
                                  {item.entry.codriver_nationality && (
                                    <span
                                      className={`fi fi-${getCountryCode(item.entry.codriver_nationality)} mr-2`}
                                      style={{ width: '16px', height: '12px' }}
                                    ></span>
                                  )}
                                  {item.entry.codriver_first_name} {item.entry.codriver_last_name}
                                </div>
                              </div>
                            </td>
                            <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400 hidden md:table-cell">
                              {item.entry.car}
                            </td>
                            <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400 hidden lg:table-cell">
                              <ClassDisplay classString={item.entry.class} showBadges={true} maxDisplay={2} />
                            </td>
                            <td className="px-4 py-3 whitespace-nowrap text-sm text-right font-medium text-gray-900 dark:text-white">
                              {(() => {
                                // If the entry has a result for this stage
                                if (item.result) {
                                  // If it's a DNF result (time = -1), show DNF
                                  if (item.result.time === -1) {
                                    return <span className="text-red-600 dark:text-red-400">DNF</span>;
                                  }

                                  // If it's a valid time, format and display it
                                  if (item.result.time && item.result.time > 0) {
                                    // Validate and convert penalty time
                                    const penaltyTime = item.result.penalty_time ? parseFloat(item.result.penalty_time) : 0;
                                    const stageTime = parseFloat(item.result.time);
                                    const hasPenalty = penaltyTime > 0 && !isNaN(penaltyTime);
                                    const totalTime = hasPenalty ? calculateTotalTime(stageTime, penaltyTime) : stageTime;



                                    return (
                                      <div className="flex flex-col">
                                        <span>
                                          {formatTime(totalTime)}
                                          {item.result.nominal_time && (
                                            <span className="ml-1 text-xs text-orange-600 dark:text-orange-400 font-medium">[N]</span>
                                          )}
                                        </span>
                                        {hasPenalty && (
                                          <div className="text-xs text-red-600 dark:text-red-400">
                                            {formatTime(stageTime)} {formatPenalty(penaltyTime)}
                                            {item.result.penalty_reason && (
                                              <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                                {item.result.penalty_reason}
                                              </div>
                                            )}
                                          </div>
                                        )}
                                      </div>
                                    );
                                  }
                                }

                                // If no result but entry has invalid status, show the status
                                const invalidStatuses = ['retired', 'dns', 'dnf', 'dsq'];
                                const isInvalidStatus = invalidStatuses.includes(item.status);

                                if (isInvalidStatus) {
                                  // For retired/DNS/DNF/DSQ entries with no time, show their status
                                  return <span className="text-red-600 dark:text-red-400">{item.status.toUpperCase()}</span>;
                                } else {
                                  return '-';
                                }
                              })()}
                            </td>
                            <td className="px-4 py-3 whitespace-nowrap text-sm text-right text-gray-600 dark:text-gray-400">
                              {(() => {
                                // If the entry has a result for this stage
                                if (item.result) {
                                  // If it's a DNF result, don't show time difference
                                  if (item.result.time === -1) {
                                    return '-';
                                  }

                                  // Only show diff for valid times if not the fastest time (diff > 0)
                                  if (item.result.time && item.result.time > 0 && item.diff !== undefined && item.diff > 0) {
                                    return (
                                      <div>
                                        <div>
                                          {/* Difference from leader */}
                                          +{item.diff < 60
                                            ? item.diff.toFixed(2) // Just show seconds if less than a minute
                                            : formatTime(item.diff)} {/* Use formatTime for larger differences */}
                                        </div>
                                        <div className="text-xs text-gray-500 dark:text-gray-500 mt-1">
                                          {/* Difference from previous entry */}
                                          {(() => {
                                            try {
                                              const index = getStageResults(selectedStage).findIndex(i => i.entry.id === item.entry.id);
                                              if (index > 0) {
                                                const prevResult = getStageResults(selectedStage)[index - 1].result;
                                                if (prevResult && item.result) {
                                                  // Make sure we're working with numbers
                                                  const prevTime = typeof prevResult.time === 'string' ? parseFloat(prevResult.time) : prevResult.time;
                                                  const currentTime = typeof item.result.time === 'string' ? parseFloat(item.result.time) : item.result.time;

                                                  // Check for valid numbers
                                                  if (prevTime === undefined || isNaN(prevTime) || currentTime === undefined || isNaN(currentTime)) {
                                                    return '';
                                                  }

                                                  const diffFromPrev = parseFloat((currentTime - prevTime).toFixed(2));

                                                  // If times are the same (within tolerance), show "="
                                                  if (Math.abs(diffFromPrev) < 0.001) {
                                                    return '=';
                                                  }

                                                  // Otherwise show the difference
                                                  return '+' + (diffFromPrev < 60
                                                    ? diffFromPrev.toFixed(2)
                                                    : formatTime(diffFromPrev));
                                                }
                                              }
                                            } catch (error) {
                                              console.error('Error calculating difference from previous entry in stage results:', error);
                                            }
                                            return '';
                                          })()}
                                        </div>
                                      </div>
                                    );
                                  } else {
                                    // This is the fastest time or tied for fastest
                                    return '-';
                                  }
                                } else {
                                  return '-';
                                }
                              })()}
                            </td>
                          </tr>
                        ))}
                    </tbody>
                  </table>
                  </div>
                ) : (
                  // Overall results - show championship-aware results if championship is selected
                  selectedChampionshipId && championshipResults.length > 0 ? (
                    <div>
                      {loadingChampionshipResults ? (
                        <div className="flex justify-center items-center py-8">
                          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                          <span className="ml-2 text-gray-600 dark:text-gray-400">Loading championship results...</span>
                        </div>
                      ) : (
                        <ChampionshipResultsTable
                          results={championshipResults}
                          championshipName={championshipName}
                          showChampionshipInfo={false}
                        />
                      )}
                    </div>
                  ) : (
                    // Default overall results table (existing logic)
                    <div className="overflow-x-auto">
                      <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead className="bg-gray-50 dark:bg-gray-800">
                          <tr>
                            <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider w-12">
                              Pos
                            </th>
                            <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider w-12">
                              #
                            </th>
                            <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                              Driver / Codriver
                            </th>
                            <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider hidden md:table-cell">
                              Car
                            </th>
                            <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider hidden lg:table-cell">
                              Class
                            </th>
                            <th scope="col" className="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                              Time
                            </th>
                            <th scope="col" className="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                              Diff
                            </th>
                          </tr>
                        </thead>
                        <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                          {sortedEntries.map((entry: any, index: number) => (
                            <tr key={entry.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                              <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-700 dark:text-gray-300">
                                {entry.totalTime
                                  ? (() => {
                                      const index = sortedEntries.findIndex(e => e.id === entry.id);
                                      const hasSameTimeAsPrevious = index > 0 &&
                                        sortedEntries[index - 1].totalTime !== undefined &&
                                        entry.totalTime !== undefined &&
                                        Math.abs((sortedEntries[index - 1].totalTime as number) - (entry.totalTime as number)) < 0.001;
                                      return hasSameTimeAsPrevious ? '=' + entry.position : entry.position;
                                    })()
                                  : (entry.status === 'retired' || entry.status === 'dnf' || entry.status === 'dns' || entry.status === 'dsq'
                                    ? entry.status.substring(0, 3).toUpperCase()
                                    : '-')}
                              </td>
                              <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                                {entry.number}
                              </td>
                              <td className="px-4 py-3 whitespace-nowrap">
                                <div>
                                  <div className="text-sm font-medium text-gray-900 dark:text-white flex items-center">
                                    {entry.driver_nationality && (
                                      <span
                                        className={`fi fi-${getCountryCode(entry.driver_nationality)} mr-2`}
                                        style={{ width: '16px', height: '12px' }}
                                      ></span>
                                    )}
                                    {entry.driver_first_name} {entry.driver_last_name}
                                  </div>
                                  <div className="text-sm text-gray-600 dark:text-gray-400 flex items-center">
                                    {entry.codriver_nationality && (
                                      <span
                                        className={`fi fi-${getCountryCode(entry.codriver_nationality)} mr-2`}
                                        style={{ width: '16px', height: '12px' }}
                                      ></span>
                                    )}
                                    {entry.codriver_first_name} {entry.codriver_last_name}
                                  </div>
                                </div>
                              </td>
                              <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400 hidden md:table-cell">
                                {entry.car}
                              </td>
                              <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400 hidden lg:table-cell">
                                <ClassDisplay classString={entry.class} showBadges={true} maxDisplay={2} />
                              </td>
                              <td className="px-4 py-3 whitespace-nowrap text-sm text-right font-medium text-gray-900 dark:text-white">
                                {entry.totalTime
                                  ? formatTime(entry.totalTime)
                                  : (entry.status === 'retired' || entry.status === 'dnf' || entry.status === 'dns' || entry.status === 'dsq'
                                    ? entry.status.toUpperCase()
                                    : '-')}
                              </td>
                              <td className="px-4 py-3 whitespace-nowrap text-sm text-right text-gray-600 dark:text-gray-400">
                                {entry.position === 1 || !entry.totalTime || !sortedEntries[0].totalTime
                                  ? '-'
                                  : (() => {
                                      try {
                                        const leader = sortedEntries.find(e => e.position === 1);
                                        if (!leader || !leader.totalTime || !entry.totalTime) return '-';
                                        const entryTime = typeof entry.totalTime === 'string' ? parseFloat(entry.totalTime) : entry.totalTime;
                                        const leaderTime = typeof leader.totalTime === 'string' ? parseFloat(leader.totalTime) : leader.totalTime;
                                        if (isNaN(entryTime) || isNaN(leaderTime)) return '-';
                                        const timeDiff = entryTime - leaderTime;
                                        return '+' + (timeDiff < 60 ? timeDiff.toFixed(2) : formatTime(timeDiff));
                                      } catch (error) {
                                        return '-';
                                      }
                                    })()}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  )
                )}
                </div>
              </div>
            )}

            {activeTab === 'stages' && (
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
                <div className="px-6 py-4 bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600">
                  <h2 className="font-medium text-lg text-gray-900 dark:text-white">
                    Stages Overview
                  </h2>
                  <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    Total of {rallyStages.length} stages, {rallyStages.reduce((sum: number, stage) => sum + parseFloat(stage.length.toString()), 0).toFixed(2)} km
                  </div>
                </div>

                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead className="bg-gray-50 dark:bg-gray-800">
                      <tr>
                        <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider w-12">
                          #
                        </th>
                        <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Name
                        </th>
                        <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Distance
                        </th>
                        <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider hidden md:table-cell">
                          Surface
                        </th>
                        <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider hidden md:table-cell">
                          Start Time
                        </th>
                        <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Status
                        </th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                      {rallyStages.map((stage) => {
                        // Get winner for this stage if completed (excluding DNF results)
                        const stageResults = rallyResults.filter(result =>
                          result.stage_id === stage.id &&
                          result.time !== -1 &&
                          result.time > 0 &&
                          !isNaN(Number(result.time))
                        );
                        const stageWinner = stage.status === 'finished' && stageResults.length > 0
                          ? stageResults
                              .map(result => {
                                const entry = rallyEntries.find(e => e.id === result.entry_id);
                                return { result, entry };
                              })
                              .filter(item => item.entry && item.result.time > 0)
                              .sort((a, b) => a.result.time - b.result.time)[0]
                          : null;

                        return (
                          <tr
                            key={stage.id}
                            className="hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer"
                            onClick={() => handleStageSelect(stage.id)}
                          >
                            <td className="px-4 py-3 whitespace-nowrap">
                              <div className="text-sm font-medium text-gray-900 dark:text-white">
                                SS{stage.number}
                              </div>
                            </td>
                            <td className="px-4 py-3 whitespace-nowrap">
                              <div className="text-sm text-gray-900 dark:text-white flex items-center">
                                {stage.name}
                                {stage.is_super_special && (
                                  <span className="ml-2 bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 px-1.5 py-0.5 rounded text-xs">
                                    SSS
                                  </span>
                                )}
                                {stage.is_power_stage && (
                                  <span className="ml-2 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-1.5 py-0.5 rounded text-xs">
                                    Power
                                  </span>
                                )}
                              </div>
                              {stageWinner && stageWinner.entry && stageWinner.result.time > 0 && (
                                <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                                  <span className="text-yellow-600 dark:text-yellow-400">
                                    <Award className="w-3 h-3 inline mr-0.5" />
                                    {stageWinner.entry.driver_first_name} {stageWinner.entry.driver_last_name}
                                  </span>
                                  {' '} • {formatTime(stageWinner.result.time)}
                                </div>
                              )}
                            </td>
                            <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400">
                              {stage.length} km
                            </td>
                            <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400 hidden md:table-cell">
                              <span className={`px-2 py-0.5 rounded-full text-xs ${
                                stage.surface === 'gravel'
                                  ? 'bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-200'
                                  : stage.surface === 'tarmac'
                                  ? 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
                                  : stage.surface === 'snow'
                                  ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                                  : 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
                              }`}>
                                {stage.surface.charAt(0).toUpperCase() + stage.surface.slice(1)}
                              </span>
                            </td>
                            <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400 hidden md:table-cell">
                              {new Date(stage.start_time).toLocaleTimeString('en-GB', {
                                hour: '2-digit',
                                minute: '2-digit',
                                day: 'numeric',
                                month: 'short'
                              })}
                            </td>
                            <td className="px-4 py-3 whitespace-nowrap">
                              {stage.status === 'upcoming' && (
                                <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded-full text-xs flex items-center">
                                  <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                                  </svg>
                                  Upcoming
                                </span>
                              )}
                              {stage.status === 'running' && (
                                <span className="bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 px-2 py-1 rounded-full text-xs flex items-center">
                                  <Clock className="w-3 h-3 mr-1 animate-pulse" />
                                  Running
                                </span>
                              )}
                              {stage.status === 'finished' && (
                                <span className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-2 py-1 rounded-full text-xs flex items-center">
                                  <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                  </svg>
                                  Finished
                                </span>
                              )}
                              {stage.status === 'canceled' && (
                                <span className="bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 px-2 py-1 rounded-full text-xs flex items-center">
                                  <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                                  </svg>
                                  Canceled
                                </span>
                              )}
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
              </div>
            )}

            {activeTab === 'itinerary' && (
              <ItineraryDisplay rallyId={id || ''} />
            )}

            {activeTab === 'shakedown' && (
              <ShakedownResults rallyId={id || ''} />
            )}

            {activeTab === 'entries' && (
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
                <div className="px-6 py-4 bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600">
                  <h2 className="font-medium text-lg text-gray-900 dark:text-white">
                    Rally Entries
                  </h2>
                  <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    Total of {rallyEntries.length} entries
                  </div>
                </div>

                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead className="bg-gray-50 dark:bg-gray-800">
                      <tr>
                        <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider w-12">
                          #
                        </th>
                        <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Driver / Codriver
                        </th>
                        <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider hidden md:table-cell">
                          Team
                        </th>
                        <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Car
                        </th>
                        <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider hidden lg:table-cell">
                          Class
                        </th>
                        <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Status
                        </th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                      {sortedEntries.map((entry) => (
                        <tr key={entry.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                          <td className="px-4 py-3 whitespace-nowrap">
                            <div className="text-sm font-medium text-gray-900 dark:text-white">
                              {entry.number}
                            </div>
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap">
                            <div>
                              <div className="text-sm font-medium text-gray-900 dark:text-white flex items-center">
                                {entry.driver_nationality && (
                                  <span
                                    className={`fi fi-${getCountryCode(entry.driver_nationality)} mr-2`}
                                    style={{ width: '16px', height: '12px' }}
                                  ></span>
                                )}
                                <Link to={`/drivers/${entry.driver_id}`} className="hover:text-red-600">
                                  {entry.driver_first_name} {entry.driver_last_name}
                                </Link>
                              </div>
                              <div className="text-sm text-gray-600 dark:text-gray-400 flex items-center">
                                {entry.codriver_nationality && (
                                  <span
                                    className={`fi fi-${getCountryCode(entry.codriver_nationality)} mr-2`}
                                    style={{ width: '16px', height: '12px' }}
                                  ></span>
                                )}
                                {entry.codriver_first_name} {entry.codriver_last_name}
                              </div>
                            </div>
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400 hidden md:table-cell">
                            {entry.team_name || '-'}
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400">
                            {entry.car}
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400 hidden lg:table-cell">
                            <ClassDisplay classString={entry.class} showBadges={true} maxDisplay={2} />
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap">
                            {entry.status === 'entered' && (
                              <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded-full text-xs">
                                Entered
                              </span>
                            )}
                            {entry.status === 'running' && (
                              <div className="flex items-center">
                                <span className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-2 py-1 rounded-full text-xs">
                                  Running
                                </span>
                                {entry.position && (
                                  <span className="ml-2 text-xs text-gray-600 dark:text-gray-400">
                                    Position: {entry.position}
                                  </span>
                                )}
                              </div>
                            )}
                            {entry.status === 'finished' && (
                              <div className="flex items-center">
                                <span className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-2 py-1 rounded-full text-xs">
                                  Finished
                                </span>
                                {entry.position && (
                                  <span className="ml-2 text-xs text-gray-600 dark:text-gray-400">
                                    Position: {entry.position}
                                  </span>
                                )}
                              </div>
                            )}
                            {entry.status === 'retired' && (
                              <span className="bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 px-2 py-1 rounded-full text-xs">
                                Retired
                              </span>
                            )}
                            {entry.status === 'dnf' && (
                              <span className="bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 px-2 py-1 rounded-full text-xs">
                                DNF
                              </span>
                            )}
                            {entry.status === 'dns' && (
                              <span className="bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 px-2 py-1 rounded-full text-xs">
                                DNS
                              </span>
                            )}
                            {entry.status === 'dsq' && (
                              <span className="bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 px-2 py-1 rounded-full text-xs">
                                DSQ
                              </span>
                            )}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default RallyDetailsPage;