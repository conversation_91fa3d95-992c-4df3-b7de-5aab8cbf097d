-- Examples of how to split multiple classes stored in a single field

-- Example 1: Split comma-separated classes
-- If classes are stored like "RC1, RC2, WRC"
SELECT 
  e.id,
  e.number,
  pd.first_name || ' ' || pd.last_name AS driver,
  TRIM(unnest(string_to_array(e.class, ','))) AS individual_class
FROM entries e
JOIN persons pd ON pd.id = e.driver_id
WHERE e.rally_id = 'your-rally-id';

-- Example 2: Split space-separated classes  
-- If classes are stored like "RC1 RC2 WRC"
SELECT 
  e.id,
  e.number,
  pd.first_name || ' ' || pd.last_name AS driver,
  TRIM(unnest(string_to_array(e.class, ' '))) AS individual_class
FROM entries e
JOIN persons pd ON pd.id = e.driver_id
WHERE e.rally_id = 'your-rally-id';

-- Example 3: Updated class_classification view to handle multiple classes
DROP VIEW IF EXISTS class_classification;

CREATE VIEW class_classification AS
SELECT
  e.rally_id,
  TRIM(unnest(string_to_array(e.class, ','))) AS class,
  e.id AS entry_id,
  pd.first_name || ' ' || pd.last_name AS driver,
  SUM(r.time + COALESCE(p.time, 0)) AS total_time,
  RANK() OVER (
    PARTITION BY e.rally_id, TRIM(unnest(string_to_array(e.class, ','))) 
    ORDER BY SUM(r.time + COALESCE(p.time, 0))
  ) AS class_position
FROM entries e
JOIN results r ON r.entry_id = e.id
JOIN persons pd ON pd.id = e.driver_id
LEFT JOIN penalties p ON p.entry_id = e.id AND p.stage_id = r.stage_id
WHERE e.status NOT IN ('retired', 'dnf', 'dsq')
GROUP BY e.rally_id, e.id, pd.first_name, pd.last_name, e.class;

-- Example 4: Get entries for a specific class
SELECT DISTINCT
  e.id,
  e.number,
  pd.first_name || ' ' || pd.last_name AS driver,
  e.class AS all_classes
FROM entries e
JOIN persons pd ON pd.id = e.driver_id
WHERE e.rally_id = 'your-rally-id'
  AND 'RC1' = ANY(string_to_array(e.class, ','))
ORDER BY e.number;

-- Example 5: Count entries per individual class
SELECT 
  e.rally_id,
  TRIM(unnest(string_to_array(e.class, ','))) AS individual_class,
  COUNT(*) AS entry_count
FROM entries e
WHERE e.rally_id = 'your-rally-id'
GROUP BY e.rally_id, TRIM(unnest(string_to_array(e.class, ',')))
ORDER BY individual_class;
