import express from 'express';
import pool from '../config/db.js';
import { verifyToken, verifyAdmin } from '../middleware/auth.js';

const router = express.Router();

// Get entry status history for a specific entry - PUBLIC ROUTE
router.get('/entry/:entryId', async (req, res) => {
  try {
    const { entryId } = req.params;

    const result = await pool.query(`
      SELECT
        esh.*,
        s.name AS stage_name,
        s.number AS stage_number,
        r.name AS rally_name,
        u.username AS changed_by_username
      FROM entry_status_history esh
      LEFT JOIN stages s ON esh.stage_id = s.id
      JOIN rallies r ON esh.rally_id = r.id
      LEFT JOIN users u ON esh.changed_by = u.id
      WHERE esh.entry_id = $1
      ORDER BY esh.changed_at DESC
    `, [entryId]);

    res.json(result.rows);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Failed to fetch entry status history' });
  }
});

// Get entry status history for a specific rally - PUBLIC ROUTE
router.get('/rally/:rallyId', async (req, res) => {
  try {
    const { rallyId } = req.params;

    const result = await pool.query(`
      SELECT
        esh.*,
        e.number AS entry_number,
        pd.first_name AS driver_first_name,
        pd.last_name AS driver_last_name,
        pcd.first_name AS codriver_first_name,
        pcd.last_name AS codriver_last_name,
        s.name AS stage_name,
        s.number AS stage_number,
        u.username AS changed_by_username
      FROM entry_status_history esh
      JOIN entries e ON esh.entry_id = e.id
      JOIN persons pd ON e.driver_id = pd.id
      JOIN persons pcd ON e.codriver_id = pcd.id
      LEFT JOIN stages s ON esh.stage_id = s.id
      LEFT JOIN users u ON esh.changed_by = u.id
      WHERE esh.rally_id = $1
      ORDER BY esh.changed_at DESC
    `, [rallyId]);

    // Format the results to include driver and codriver names
    const formattedResults = result.rows.map(row => ({
      ...row,
      driver_name: `${row.driver_first_name} ${row.driver_last_name}`,
      codriver_name: `${row.codriver_first_name} ${row.codriver_last_name}`
    }));

    res.json(formattedResults);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Failed to fetch rally status history' });
  }
});

// Manually add entry status change - ADMIN ROUTE
router.post('/', verifyToken, verifyAdmin, async (req, res) => {
  try {
    const { entry_id, rally_id, stage_id, old_status, new_status, reason } = req.body;
    const changed_by = req.user.id;

    const result = await pool.query(`
      INSERT INTO entry_status_history (entry_id, rally_id, stage_id, old_status, new_status, changed_by, reason)
      VALUES ($1, $2, $3, $4, $5, $6, $7)
      RETURNING *
    `, [entry_id, rally_id, stage_id, old_status, new_status, changed_by, reason]);

    res.status(201).json(result.rows[0]);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Failed to add status history entry' });
  }
});

// Get active entries for a rally - PUBLIC ROUTE
router.get('/active/:rallyId', async (req, res) => {
  try {
    const { rallyId } = req.params;

    const result = await pool.query(`
      SELECT * FROM active_entries WHERE rally_id = $1
      ORDER BY number ASC
    `, [rallyId]);

    res.json(result.rows);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Failed to fetch active entries' });
  }
});

// Get retired entries for a rally - PUBLIC ROUTE
router.get('/retired/:rallyId', async (req, res) => {
  try {
    const { rallyId } = req.params;

    const result = await pool.query(`
      SELECT * FROM retired_entries WHERE rally_id = $1
      ORDER BY number ASC
    `, [rallyId]);

    res.json(result.rows);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Failed to fetch retired entries' });
  }
});

// Get entry activity statistics for a rally - PUBLIC ROUTE
router.get('/stats/:rallyId', async (req, res) => {
  try {
    const { rallyId } = req.params;

    const result = await pool.query(`
      SELECT
        COUNT(*) as total_entries,
        COUNT(CASE WHEN status NOT IN ('retired', 'dns', 'dnf', 'dsq') THEN 1 END) as active_entries,
        COUNT(CASE WHEN status = 'retired' THEN 1 END) as retired_entries,
        COUNT(CASE WHEN status = 'dns' THEN 1 END) as dns_entries,
        COUNT(CASE WHEN status = 'dnf' THEN 1 END) as dnf_entries,
        COUNT(CASE WHEN status = 'dsq' THEN 1 END) as dsq_entries
      FROM entries
      WHERE rally_id = $1
    `, [rallyId]);

    res.json(result.rows[0]);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Failed to fetch entry statistics' });
  }
});

export default router;
