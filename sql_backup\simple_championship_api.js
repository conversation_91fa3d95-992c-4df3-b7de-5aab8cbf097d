// Simple Championship API - Handle filtering in JavaScript instead of complex SQL

// Championship filtering functions
const championshipFilters = {
  // Greece Championship: All C-classes
  'greece': (entry) => {
    return entry.class && (
      entry.class.includes('C1') || entry.class.includes('C2') || 
      entry.class.includes('C3') || entry.class.includes('C4') || 
      entry.class.includes('C5') || entry.class.includes('C6')
    );
  },

  // Historic Championship: Only numeric classes 2, 3, 4
  'historic': (entry) => {
    return entry.class && (
      entry.class === '2' || entry.class === '3' || entry.class === '4'
    );
  },

  // Historic Gravel Cup: Same as Historic
  'historic-gravel': (entry) => {
    return championshipFilters.historic(entry);
  },

  // Rally3 Championship: C2 cars that are Rally3
  'rally3': (entry) => {
    return entry.class && entry.class.includes('C2') && 
           entry.car && (
             entry.car.toLowerCase().includes('rally3') ||
             entry.car.toLowerCase().includes('clio') ||
             entry.car.toLowerCase().includes('208')
           );
  }
};

// Simple points calculation
const calculatePoints = (position) => {
  const pointsTable = {
    1: 25, 2: 18, 3: 15, 4: 12, 5: 10,
    6: 8, 7: 6, 8: 4, 9: 2, 10: 1
  };
  return pointsTable[position] || 0;
};

// API endpoint for championship results
app.get('/api/championship-results/:championshipId/:rallyId', async (req, res) => {
  try {
    const { championshipId, rallyId } = req.params;
    
    // Get all entries for the rally
    const allEntries = await db.query(`
      SELECT 
        e.id, e.number, e.class, e.car,
        CONCAT(pd.first_name, ' ', pd.last_name) as driver,
        pd.nationality as driver_nationality,
        CONCAT(pc.first_name, ' ', pc.last_name) as codriver,
        pc.nationality as codriver_nationality,
        oc.position, oc.total_time, oc.time_diff_leader
      FROM entries e
      JOIN persons pd ON e.driver_id = pd.id
      JOIN persons pc ON e.codriver_id = pc.id
      JOIN overall_classification oc ON e.id = oc.entry_id
      WHERE e.rally_id = $1 AND e.status IN ('finished', 'retired', 'dnf')
      ORDER BY oc.position
    `, [rallyId]);

    // Map championship ID to filter function
    const championshipMap = {
      '55a003a9-66ff-4a37-b11d-2e14df10bae3': 'greece',
      'cc1e9147-fdb3-4483-aad3-9e27795eff17': 'historic',
      'aa934657-d4f5-49bb-80d5-b0be90e72b4c': 'historic-gravel',
      '895f159e-f147-439b-b5ab-04972033a7bb': 'rally3'
    };

    const filterKey = championshipMap[championshipId];
    if (!filterKey || !championshipFilters[filterKey]) {
      return res.status(400).json({ error: 'Invalid championship' });
    }

    // Filter entries for this championship
    const filteredEntries = allEntries.rows
      .filter(championshipFilters[filterKey])
      .map((entry, index) => ({
        ...entry,
        championship_position: index + 1,
        points: calculatePoints(index + 1)
      }));

    res.json(filteredEntries);
  } catch (error) {
    console.error('Championship results error:', error);
    res.status(500).json({ error: 'Failed to get championship results' });
  }
});

// API endpoint for championship standings
app.get('/api/championship-standings/:championshipId', async (req, res) => {
  try {
    const { championshipId } = req.params;
    
    // Get all rallies for this championship
    const rallies = await db.query(`
      SELECT r.id, r.name, r.start_date
      FROM rallies r
      JOIN championship_events ce ON r.id = ce.rally_id
      WHERE ce.championship_id = $1
      ORDER BY r.start_date
    `, [championshipId]);

    const standings = {};

    // Process each rally
    for (const rally of rallies.rows) {
      // Get all entries for this rally
      const allEntries = await db.query(`
        SELECT 
          e.id, e.number, e.class, e.car,
          CONCAT(pd.first_name, ' ', pd.last_name) as driver,
          pd.nationality as driver_nationality,
          oc.position
        FROM entries e
        JOIN persons pd ON e.driver_id = pd.id
        JOIN overall_classification oc ON e.id = oc.entry_id
        WHERE e.rally_id = $1 AND e.status IN ('finished', 'retired', 'dnf')
        ORDER BY oc.position
      `, [rally.id]);

      // Filter and calculate points
      const championshipMap = {
        '55a003a9-66ff-4a37-b11d-2e14df10bae3': 'greece',
        'cc1e9147-fdb3-4483-aad3-9e27795eff17': 'historic',
        'aa934657-d4f5-49bb-80d5-b0be90e72b4c': 'historic-gravel',
        '895f159e-f147-439b-b5ab-04972033a7bb': 'rally3'
      };

      const filterKey = championshipMap[championshipId];
      const filteredEntries = allEntries.rows.filter(championshipFilters[filterKey]);

      filteredEntries.forEach((entry, index) => {
        const driver = entry.driver;
        if (!standings[driver]) {
          standings[driver] = {
            driver,
            driver_nationality: entry.driver_nationality,
            total_points: 0,
            rallies: []
          };
        }
        
        const points = calculatePoints(index + 1);
        standings[driver].total_points += points;
        standings[driver].rallies.push({
          rally_name: rally.name,
          position: index + 1,
          points
        });
      });
    }

    // Convert to array and sort
    const standingsArray = Object.values(standings)
      .sort((a, b) => b.total_points - a.total_points)
      .map((driver, index) => ({
        ...driver,
        championship_position: index + 1
      }));

    res.json(standingsArray);
  } catch (error) {
    console.error('Championship standings error:', error);
    res.status(500).json({ error: 'Failed to get championship standings' });
  }
});

module.exports = { championshipFilters, calculatePoints };
