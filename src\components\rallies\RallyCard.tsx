import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Calendar, Flag, MapPin, Clock } from 'lucide-react';
import { Rally } from '../../types';
import { getCountryCode } from '../../utils/countryUtils';
import { getImageUrl, DEFAULT_RALLY_BANNER } from '../../utils/imageUtils';

interface RallyCardProps {
  rally: Rally;
}

const RallyCard: React.FC<RallyCardProps> = ({ rally }) => {
  const formatDate = (dateString: string) => {
    const options: Intl.DateTimeFormatOptions = { day: 'numeric', month: 'short' };
    return new Date(dateString).toLocaleDateString('en-GB', options);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'running':
        return (
          <span className="m-4 bg-red-600 text-white px-3 py-1 rounded-full text-xs font-semibold flex items-center">
            <Clock className="w-3 h-3 mr-1 animate-pulse" />
            Running
          </span>
        );
      case 'upcoming':
        return (
          <span className="m-4 bg-blue-600 text-white px-3 py-1 rounded-full text-xs font-semibold">
            Upcoming
          </span>
        );
      case 'finished':
        return (
          <span className="m-4 bg-green-600 text-white px-3 py-1 rounded-full text-xs font-semibold">
            Finished
          </span>
        );
      default:
        return null;
    }
  };

  return (
    <Link to={`/rallies/${rally.id}`} className="group">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden transition-transform duration-300 hover:shadow-lg hover:-translate-y-1 relative">
        <div className="h-48 relative overflow-hidden">
          {/* Status badge positioned on top of the image */}
          <div className="absolute top-0 right-0 z-20">
            {getStatusBadge(rally.status)}
          </div>

          <img
            src={getImageUrl(rally.banner_url, DEFAULT_RALLY_BANNER)}
            alt={rally.name}
            className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-gray-900 to-transparent"></div>

          <div className="absolute bottom-0 left-0 right-0 p-4 flex items-center">
            <div
              className="w-8 h-6 rounded mr-2 border border-white/30 bg-gray-700 flex items-center justify-center text-xs overflow-hidden"
            >
              {rally.country && (
                <span
                  className={`fi fi-${getCountryCode(rally.country)}`}
                  style={{ width: '100%', height: '100%', objectFit: 'cover' }}
                ></span>
              )}
            </div>
            <h3 className="text-white font-bold text-lg line-clamp-1">{rally.name}</h3>
          </div>
        </div>

        <div className="p-4">
          <div className="flex items-center text-gray-600 dark:text-gray-300 mb-2">
            <Calendar className="w-4 h-4 mr-2" />
            <span className="text-sm">
              {formatDate(rally.start_date)} - {formatDate(rally.end_date)}
            </span>
          </div>

          <div className="flex items-center text-gray-600 dark:text-gray-300 mb-4">
            <MapPin className="w-4 h-4 mr-2" />
            <span className="text-sm flex items-center">
              {rally.country && (
                <span
                  className={`fi fi-${getCountryCode(rally.country)} mr-1.5`}
                  style={{ width: '14px', height: '10px' }}
                ></span>
              )}
              {rally.country}
            </span>
          </div>

          <div className="flex items-center">
            <span className={`px-2 py-1 text-xs rounded-full ${
              rally.surface === 'gravel'
                ? 'bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-200'
                : rally.surface === 'tarmac'
                ? 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
                : rally.surface === 'snow'
                ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                : 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
            }`}>
              {rally.surface.charAt(0).toUpperCase() + rally.surface.slice(1)}
            </span>

            {/* Stage count would go here if available in the Rally type */}
          </div>
        </div>
      </div>
    </Link>
  );
};

export default RallyCard;