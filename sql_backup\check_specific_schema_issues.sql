-- Check for specific issues I noticed in the schema vs actual usage

-- ============================================================================
-- ISSUE 1: Check if results table has is_active column (from memories)
-- ============================================================================
SELECT 'CHECKING RESULTS TABLE FOR is_active COLUMN' as section;
SELECT 
  column_name,
  data_type,
  is_nullable,
  column_default
FROM information_schema.columns 
WHERE table_name = 'results' 
  AND column_name = 'is_active';

-- If no results, the column doesn't exist
SELECT CASE 
  WHEN EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'results' AND column_name = 'is_active'
  ) 
  THEN 'is_active column EXISTS in results table'
  ELSE 'is_active column MISSING from results table (but used in code)'
END as status;

-- ============================================================================
-- ISSUE 2: Check entry_status enum values vs what's actually used
-- ============================================================================
SELECT 'CHECKING ENTRY STATUS VALUES' as section;
-- Current enum values
SELECT 'Current enum values:' as info, string_agg(enumlabel, ', ' ORDER BY enumsortorder) as values
FROM pg_enum e 
JOIN pg_type t ON e.enumtypid = t.oid 
WHERE t.typname = 'entry_status';

-- Actually used values in entries table
SELECT 'Actually used values:' as info, string_agg(DISTINCT status, ', ') as values
FROM entries;

-- ============================================================================
-- ISSUE 3: Check if championship_events table has unique constraint
-- ============================================================================
SELECT 'CHECKING CHAMPIONSHIP_EVENTS CONSTRAINTS' as section;
SELECT 
  tc.constraint_name,
  tc.constraint_type,
  string_agg(kcu.column_name, ', ' ORDER BY kcu.ordinal_position) as columns
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu 
  ON tc.constraint_name = kcu.constraint_name
WHERE tc.table_name = 'championship_events'
  AND tc.constraint_type IN ('UNIQUE', 'PRIMARY KEY')
GROUP BY tc.constraint_name, tc.constraint_type
ORDER BY tc.constraint_type;

-- ============================================================================
-- ISSUE 4: Check if stages table has all needed columns for EWRC import
-- ============================================================================
SELECT 'CHECKING STAGES TABLE STRUCTURE' as section;
SELECT 
  column_name,
  data_type,
  is_nullable,
  column_default
FROM information_schema.columns 
WHERE table_name = 'stages' 
ORDER BY ordinal_position;

-- Check for specific columns used in EWRC import
SELECT 'CHECKING SPECIFIC STAGE COLUMNS' as section;
SELECT 
  CASE WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'stages' AND column_name = 'length') 
    THEN 'length column EXISTS' 
    ELSE 'length column MISSING' END as length_status,
  CASE WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'stages' AND column_name = 'start_time') 
    THEN 'start_time column EXISTS' 
    ELSE 'start_time column MISSING' END as start_time_status,
  CASE WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'stages' AND column_name = 'surface') 
    THEN 'surface column EXISTS' 
    ELSE 'surface column MISSING' END as surface_status;

-- ============================================================================
-- ISSUE 5: Check if there are any views that might be broken
-- ============================================================================
SELECT 'CHECKING VIEW DEPENDENCIES' as section;
-- Try to query each view to see if it works
SELECT 'Testing overall_classification view...' as test;
SELECT COUNT(*) as record_count FROM overall_classification LIMIT 1;

-- ============================================================================
-- ISSUE 6: Check data consistency
-- ============================================================================
SELECT 'DATA CONSISTENCY CHECKS' as section;

-- Check for orphaned entries (entries without valid driver/codriver)
SELECT 'Orphaned entries check:' as check_type, COUNT(*) as count
FROM entries e
LEFT JOIN persons pd ON pd.id = e.driver_id
LEFT JOIN persons pc ON pc.id = e.codriver_id
WHERE pd.id IS NULL OR pc.id IS NULL;

-- Check for entries without corresponding driver/codriver records
SELECT 'Missing driver records:' as check_type, COUNT(*) as count
FROM entries e
LEFT JOIN drivers d ON d.id = e.driver_id
WHERE d.id IS NULL;

SELECT 'Missing codriver records:' as check_type, COUNT(*) as count
FROM entries e
LEFT JOIN codrivers c ON c.id = e.codriver_id
WHERE c.id IS NULL;
