import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { ChevronRight, Calendar, User } from 'lucide-react';

interface News {
  id: string;
  title: string;
  content: string;
  excerpt: string;
  image_url: string;
  author_id: string;
  author_name?: string;
  created_at?: string;
  updated_at?: string;
}

const NewsPage: React.FC = () => {
  const [news, setNews] = useState<News[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    fetchNews();
  }, []);

  const fetchNews = async () => {
    setLoading(true);
    try {
      const res = await fetch('/api/news');
      if (!res.ok) throw new Error('Failed to fetch news');
      const data = await res.json();
      setNews(Array.isArray(data) ? data : data.news || []);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch news');
    } finally {
      setLoading(false);
    }
  };

  // Helper function to format date
  const formatDate = (dateString?: string) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Latest News</h1>
        <p className="text-gray-600 dark:text-gray-400">Stay updated with the latest rally news and events</p>
      </div>

      {loading ? (
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-4 border-gray-300 border-t-red-600"></div>
        </div>
      ) : error ? (
        <div className="bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-200 p-4 rounded-lg mb-6">
          {error}
        </div>
      ) : news.length === 0 ? (
        <div className="bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 p-8 rounded-lg text-center">
          <p className="text-lg">No news articles found.</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {news.map((article) => (
            <div key={article.id} className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden flex flex-col h-full">
              <div className="relative h-48 overflow-hidden">
                <img
                  src={article.image_url || 'https://via.placeholder.com/600x400?text=No+Image'}
                  alt={article.title}
                  className="w-full h-full object-cover transition-transform duration-500 hover:scale-105"
                  onError={(e) => {
                    (e.target as HTMLImageElement).src = 'https://via.placeholder.com/600x400?text=No+Image';
                  }}
                />
              </div>
              <div className="p-5 flex-grow flex flex-col">
                <div className="flex items-center text-sm text-gray-500 dark:text-gray-400 mb-3 space-x-4">
                  <div className="flex items-center">
                    <Calendar className="w-4 h-4 mr-1" />
                    <span>{formatDate(article.created_at)}</span>
                  </div>
                  {article.author_name && (
                    <div className="flex items-center">
                      <User className="w-4 h-4 mr-1" />
                      <span>{article.author_name}</span>
                    </div>
                  )}
                </div>
                <h2 className="text-xl font-bold mb-2 text-gray-900 dark:text-white">
                  {article.title}
                </h2>
                <p className="text-gray-600 dark:text-gray-300 mb-4 flex-grow">
                  {article.excerpt}
                </p>
                <Link
                  to={`/news/${article.id}`}
                  className="text-red-600 hover:text-red-700 flex items-center font-medium mt-auto"
                >
                  Read More <ChevronRight className="w-4 h-4 ml-1" />
                </Link>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default NewsPage;
