-- Check if the migration has been applied
-- This script checks the current database structure

-- Check if championship_id column still exists in rallies table
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'rallies' 
AND column_name = 'championship_id';

-- Check championship_events table structure
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'championship_events'
ORDER BY ordinal_position;

-- Check constraints on championship_events table
SELECT constraint_name, constraint_type
FROM information_schema.table_constraints
WHERE table_name = 'championship_events';

-- Check existing data in championship_events table
SELECT COUNT(*) as total_championship_events FROM championship_events;

-- Sample data from championship_events
SELECT ce.*, c.name as championship_name, r.name as rally_name
FROM championship_events ce
JOIN championships c ON ce.championship_id = c.id
JOIN rallies r ON ce.rally_id = r.id
LIMIT 5;
