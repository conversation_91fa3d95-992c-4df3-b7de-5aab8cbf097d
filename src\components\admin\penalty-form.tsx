import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

interface Stage {
  id: string;
  rally_id: string;
  name: string;
  number: number;
}

interface Entry {
  id: string;
  rally_id: string;
  number: number;
  driver_first_name?: string;
  driver_last_name?: string;
  codriver_first_name?: string;
  codriver_last_name?: string;
}

interface PenaltyFormProps {
  initialData?: any;
  onSubmit?: (formData: any) => Promise<void>;
  onDelete?: () => Promise<void>;
  preselectedEntryId?: string;
  preselectedStageId?: string;
}

const PenaltyForm: React.FC<PenaltyFormProps> = ({ 
  initialData, 
  onSubmit,
  onDelete,
  preselectedEntryId,
  preselectedStageId
}) => {
  const [form, setForm] = useState({
    entry_id: '',
    stage_id: '',
    penalty_type: '',
    penalty_value: '',
    notes: ''
  });
  const [entries, setEntries] = useState<Entry[]>([]);
  const [stages, setStages] = useState<Stage[]>([]);
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const navigate = useNavigate();

  // Penalty type options
  const penaltyTypes = [
    { value: 'time', label: 'Time Penalty' },
    { value: 'road', label: 'Road Penalty' },
    { value: 'other', label: 'Other' },
  ];

  useEffect(() => {
    fetchEntries();
    fetchStages();
  }, []);

  useEffect(() => {
    if (initialData) {
      setForm({
        entry_id: initialData.entry_id || '',
        stage_id: initialData.stage_id || '',
        penalty_type: initialData.penalty_type || '',
        penalty_value: initialData.penalty_value ? String(initialData.penalty_value) : '',
        notes: initialData.notes || ''
      });
    } else {
      if (preselectedEntryId) {
        setForm(prev => ({ ...prev, entry_id: preselectedEntryId }));
      }
      if (preselectedStageId) {
        setForm(prev => ({ ...prev, stage_id: preselectedStageId }));
      }
    }
  }, [initialData, preselectedEntryId, preselectedStageId]);

  const fetchEntries = async () => {
    try {
      const res = await fetch('/api/entries', { credentials: 'include' });
      if (!res.ok) throw new Error('Failed to fetch entries');
      const data = await res.json();
      setEntries(Array.isArray(data) ? data : data.entries || []);
    } catch (err: any) {
      setError(err.message);
    }
  };

  const fetchStages = async () => {
    try {
      const res = await fetch('/api/stages', { credentials: 'include' });
      if (!res.ok) throw new Error('Failed to fetch stages');
      const data = await res.json();
      setStages(Array.isArray(data) ? data : data.stages || []);
    } catch (err: any) {
      setError(err.message);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const validateForm = () => {
    const errors: Record<string, string> = {};

    if (!form.entry_id) errors.entry_id = 'Entry is required';
    if (!form.stage_id) errors.stage_id = 'Stage is required';
    if (!form.penalty_type) errors.penalty_type = 'Penalty type is required';
    if (!form.penalty_value.trim()) errors.penalty_value = 'Penalty value is required';
    
    // Validate penalty_value is a number
    if (form.penalty_value && isNaN(Number(form.penalty_value))) {
      errors.penalty_value = 'Penalty value must be a number';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    setLoading(true);
    setError('');
    
    try {
      // Format the form data for submission
      const formattedData = {
        ...form,
        penalty_value: parseFloat(form.penalty_value)
      };

      if (onSubmit) {
        await onSubmit(formattedData);
      } else {
        // Default submission logic if no onSubmit provided
        const url = initialData ? `/api/penalties/${initialData.id}` : '/api/penalties';
        const method = initialData ? 'PUT' : 'POST';
        
        // Log the form data being sent
        console.log('Submitting penalty form data:', formattedData);
        
        const response = await fetch(url, {
          method,
          headers: { 'Content-Type': 'application/json' },
          credentials: 'include',
          body: JSON.stringify(formattedData)
        });

        // Log the response
        console.log('Response status:', response.status);
        const responseData = await response.json();
        console.log('Response data:', responseData);

        if (!response.ok) {
          throw new Error(responseData.message || responseData.error || `Failed to ${initialData ? 'update' : 'add'} penalty`);
        }

        navigate('/admin/penalties');
      }
    } catch (err: any) {
      setError(err.message);
      window.scrollTo(0, 0); // Scroll to top to show error
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!initialData || !onDelete) return;
    
    if (!window.confirm('Are you sure you want to delete this penalty?')) {
      return;
    }

    try {
      await onDelete();
    } catch (err: any) {
      setError(err.message);
    }
  };

  const handleCancel = () => {
    navigate('/admin/penalties');
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      {error && (
        <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          {error}
        </div>
      )}
      
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Entry
          </label>
          <select
            name="entry_id"
            value={form.entry_id}
            onChange={handleChange}
            required
            className={`w-full px-3 py-2 rounded border ${
              validationErrors.entry_id ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
            } dark:bg-gray-700 dark:text-white`}
          >
            <option value="">Select Entry</option>
            {entries.map(entry => (
              <option key={entry.id} value={entry.id}>
                #{entry.number} - {entry.driver_first_name} {entry.driver_last_name} / {entry.codriver_first_name} {entry.codriver_last_name}
              </option>
            ))}
          </select>
          {validationErrors.entry_id && (
            <p className="mt-1 text-sm text-red-500">{validationErrors.entry_id}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Stage
          </label>
          <select
            name="stage_id"
            value={form.stage_id}
            onChange={handleChange}
            required
            className={`w-full px-3 py-2 rounded border ${
              validationErrors.stage_id ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
            } dark:bg-gray-700 dark:text-white`}
          >
            <option value="">Select Stage</option>
            {stages.map(stage => (
              <option key={stage.id} value={stage.id}>
                SS{stage.number} - {stage.name}
              </option>
            ))}
          </select>
          {validationErrors.stage_id && (
            <p className="mt-1 text-sm text-red-500">{validationErrors.stage_id}</p>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Penalty Type
            </label>
            <select
              name="penalty_type"
              value={form.penalty_type}
              onChange={handleChange}
              required
              className={`w-full px-3 py-2 rounded border ${
                validationErrors.penalty_type ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
              } dark:bg-gray-700 dark:text-white`}
            >
              <option value="">Select Penalty Type</option>
              {penaltyTypes.map(type => (
                <option key={type.value} value={type.value}>{type.label}</option>
              ))}
            </select>
            {validationErrors.penalty_type && (
              <p className="mt-1 text-sm text-red-500">{validationErrors.penalty_type}</p>
            )}
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Penalty Value (seconds)
            </label>
            <input
              name="penalty_value"
              value={form.penalty_value}
              onChange={handleChange}
              required
              type="number"
              step="0.1"
              min="0"
              placeholder="Penalty Value"
              className={`w-full px-3 py-2 rounded border ${
                validationErrors.penalty_value ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
              } dark:bg-gray-700 dark:text-white`}
            />
            {validationErrors.penalty_value && (
              <p className="mt-1 text-sm text-red-500">{validationErrors.penalty_value}</p>
            )}
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Notes
          </label>
          <textarea
            name="notes"
            value={form.notes}
            onChange={handleChange}
            rows={3}
            placeholder="Additional notes about the penalty"
            className="w-full px-3 py-2 rounded border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
          />
        </div>

        <div className="flex justify-between pt-4">
          <div>
            {initialData && onDelete && (
              <button
                type="button"
                onClick={handleDelete}
                className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
              >
                Delete
              </button>
            )}
          </div>
          <div className="flex space-x-3">
            <button
              type="button"
              onClick={handleCancel}
              className="px-4 py-2 border border-gray-300 rounded text-gray-700 bg-white hover:bg-gray-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:hover:bg-gray-600"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors disabled:opacity-50"
            >
              {loading ? (initialData ? 'Saving...' : 'Adding...') : (initialData ? 'Save Changes' : 'Add Penalty')}
            </button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default PenaltyForm;
