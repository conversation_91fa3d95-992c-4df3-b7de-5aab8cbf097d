import React, { useState } from "react";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { Flag, Menu as MenuIcon, X as XIcon, User as UserIcon } from "lucide-react";
import { useAuth } from '../../context/AuthContext';

const navLinks = [
  { name: "Rallies", href: "/rallies" },
  { name: "Championships", href: "/championships" },
  { name: "Drivers", href: "/drivers" },
  { name: "Teams", href: "/teams" },
  { name: "News", href: "/news" },
];

const Navbar: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const { user, signOut } = useAuth();
  const [dropdownOpen, setDropdownOpen] = useState(false);
  let dropdownTimeout: NodeJS.Timeout;

  const handleDropdownEnter = () => {
    clearTimeout(dropdownTimeout);
    setDropdownOpen(true);
  };
  const handleDropdownLeave = () => {
    dropdownTimeout = setTimeout(() => setDropdownOpen(false), 150);
  };
  const handleLogout = async () => {
    await signOut();
    setDropdownOpen(false);
  };

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-white/95 dark:bg-gray-900/95 backdrop-blur supports-[backdrop-filter]:bg-white/60 dark:supports-[backdrop-filter]:bg-gray-900/60 shadow-sm">
      <div className="container flex h-16 items-center justify-between mx-auto px-4">
        <div className="flex items-center gap-8">
          <Link to="/" className="flex items-center space-x-2">
            <img
              src="/images/logo2.png"
              alt="StageTime.gr Logo"
              className="h-10 w-30"
            />
          </Link>
          <nav className="hidden md:flex gap-6">
            {navLinks.map((link) => (
              <Link
                key={link.href}
                to={link.href}
                className={`text-sm font-medium transition-colors hover:text-red-600 relative py-1 ${
                  location.pathname === link.href
                    ? "text-red-600 after:absolute after:bottom-0 after:left-0 after:w-full after:h-0.5 after:bg-red-600"
                    : "text-gray-500 dark:text-gray-300"
                }`}
              >
                {link.name}
              </Link>
            ))}
          </nav>
        </div>
        <div className="flex items-center gap-4 relative">
          {user ? (
            <div className="relative">
              <button
                className="bg-red-600 text-white px-4 py-2 rounded-full text-sm font-medium hover:bg-red-700 transition-colors hidden md:inline-flex items-center"
                onMouseEnter={handleDropdownEnter}
                onMouseLeave={handleDropdownLeave}
                onClick={() => navigate('/admin')}
              >
                Admin Dashboard
              </button>
              {dropdownOpen && (
                <div
                  className="absolute right-0 mt-2 w-40 bg-white dark:bg-gray-800 rounded shadow-lg z-50 border border-gray-200 dark:border-gray-700"
                  onMouseEnter={handleDropdownEnter}
                  onMouseLeave={handleDropdownLeave}
                >
                  <button
                    onClick={handleLogout}
                    className="block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
                  >
                    Logout
                  </button>
                </div>
              )}
            </div>
          ) : null}
          <button
            className="md:hidden bg-transparent border-none p-2"
            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            aria-label="Toggle menu"
          >
            {mobileMenuOpen ? <XIcon className="h-6 w-6" /> : <MenuIcon className="h-6 w-6" />}
          </button>
        </div>
      </div>
      {/* Mobile menu */}
      {mobileMenuOpen && (
        <div className="md:hidden p-4 border-t bg-white dark:bg-gray-900">
          <div className="flex justify-center mb-4">
            <img
              src="/images/logo.png"
              alt="StageTime.gr Logo"
              className="h-8 w-auto"
            />
          </div>
          <nav className="flex flex-col space-y-4">
            {navLinks.map((link) => (
              <Link
                key={link.href}
                to={link.href}
                className={`text-sm font-medium transition-colors hover:text-red-600 p-2 rounded-md ${
                  location.pathname === link.href ? "text-red-600 bg-red-50 dark:bg-red-900/10" : "text-gray-500 dark:text-gray-300"
                }`}
                onClick={() => setMobileMenuOpen(false)}
              >
                {link.name}
              </Link>
            ))}
          </nav>
        </div>
      )}
    </header>
  );
};

export default Navbar;