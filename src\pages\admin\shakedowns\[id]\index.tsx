import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Clock } from 'lucide-react';
import { useAuth } from '../../../../context/AuthContext';
import ShakedownForm from '../../../../components/admin/shakedown-form';
import ShakedownRunsManager from '../../../../components/admin/shakedown-runs-manager';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../../../components/ui/tabs';

const EditShakedownPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const { user } = useAuth();
  const navigate = useNavigate();
  const [shakedown, setShakedown] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    fetchShakedown();
  }, [id]);

  const fetchShakedown = async () => {
    if (!id) return;
    
    setLoading(true);
    try {
      const response = await fetch(`/api/shakedowns/${id}`, {
        credentials: 'include'
      });
      
      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('Shakedown not found');
        }
        throw new Error('Failed to fetch shakedown');
      }
      
      const data = await response.json();
      setShakedown(data);
    } catch (err: any) {
      console.error('Error fetching shakedown:', err);
      setError(err.message || 'An error occurred while fetching the shakedown');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (formData: any) => {
    if (!id) return;
    
    try {
      const response = await fetch(`/api/shakedowns/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify(formData)
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to update shakedown');
      }
      
      // Refresh the shakedown data
      fetchShakedown();
    } catch (error) {
      console.error('Error updating shakedown:', error);
      throw error;
    }
  };

  const handleDelete = async () => {
    if (!id) return;
    
    try {
      const response = await fetch(`/api/shakedowns/${id}`, {
        method: 'DELETE',
        credentials: 'include'
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to delete shakedown');
      }
      
      navigate('/admin/shakedowns');
    } catch (error) {
      console.error('Error deleting shakedown:', error);
      throw error;
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-100 dark:bg-gray-900 flex justify-center items-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-100 dark:bg-gray-900 flex justify-center items-center">
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-bold text-red-600 mb-4">Error</h2>
          <p className="text-gray-700 dark:text-gray-300">{error}</p>
          <button
            onClick={() => navigate('/admin/shakedowns')}
            className="mt-4 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
          >
            Back to Shakedowns
          </button>
        </div>
      </div>
    );
  }

  if (!shakedown) {
    return (
      <div className="min-h-screen bg-gray-100 dark:bg-gray-900 flex justify-center items-center">
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4">Shakedown Not Found</h2>
          <p className="text-gray-700 dark:text-gray-300">The shakedown you're looking for doesn't exist or has been removed.</p>
          <button
            onClick={() => navigate('/admin/shakedowns')}
            className="mt-4 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
          >
            Back to Shakedowns
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100 dark:bg-gray-900">
      <div className="bg-white dark:bg-gray-800 shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <Clock className="h-8 w-8 text-red-600" />
              <span className="ml-2 text-xl font-bold text-gray-900 dark:text-white">
                Edit Shakedown
              </span>
            </div>
            <div className="flex items-center">
              <span className="text-gray-600 dark:text-gray-300 mr-4">{user?.email}</span>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">{shakedown.name}</h1>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
            Edit shakedown details or manage runs
          </p>
        </div>

        <Tabs defaultValue="details" className="w-full">
          <TabsList className="mb-6">
            <TabsTrigger value="details">Shakedown Details</TabsTrigger>
            <TabsTrigger value="runs">Shakedown Runs</TabsTrigger>
          </TabsList>

          <TabsContent value="details">
            <ShakedownForm
              initialData={shakedown}
              onSubmit={handleSubmit}
              onDelete={handleDelete}
            />
          </TabsContent>

          <TabsContent value="runs">
            <ShakedownRunsManager shakedownId={id || ''} />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default EditShakedownPage;
