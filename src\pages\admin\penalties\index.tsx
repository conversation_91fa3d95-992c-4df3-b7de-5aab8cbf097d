import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { AlertTriangle, Edit, Search, RefreshCw } from 'lucide-react';

interface Penalty {
  id: string;
  entry_id: string;
  stage_id: string;
  penalty_type: string;
  penalty_value: number;
  notes?: string;
  created_at?: string;
  updated_at?: string;
  rally_id?: string;
  rally_name?: string;
  stage_name?: string;
  entry_number?: number;
  driver_name?: string;
}

interface Rally {
  id: string;
  name: string;
}

interface Stage {
  id: string;
  rally_id: string;
  name: string;
  number: number;
}

const PenaltiesManagementPage: React.FC = () => {
  const [penalties, setPenalties] = useState<Penalty[]>([]);
  const [rallies, setRallies] = useState<Rally[]>([]);
  const [stages, setStages] = useState<Stage[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedRally, setSelectedRally] = useState<string>('');
  const [selectedStage, setSelectedStage] = useState<string>('');

  useEffect(() => {
    fetchPenalties();
    fetchRallies();
    fetchStages();
  }, []);

  const fetchRallies = async () => {
    try {
      const res = await fetch('/api/rallies', { credentials: 'include' });
      if (!res.ok) throw new Error('Failed to fetch rallies');
      const data = await res.json();
      setRallies(Array.isArray(data) ? data : data.rallies || []);
    } catch (err: any) {
      console.error('Failed to fetch rallies:', err.message);
    }
  };

  const fetchStages = async () => {
    try {
      const res = await fetch('/api/stages', { credentials: 'include' });
      if (!res.ok) throw new Error('Failed to fetch stages');
      const data = await res.json();
      setStages(Array.isArray(data) ? data : data.stages || []);
    } catch (err: any) {
      console.error('Failed to fetch stages:', err.message);
    }
  };

  const fetchPenalties = async () => {
    setLoading(true);
    try {
      const res = await fetch('/api/penalties', { credentials: 'include' });
      if (!res.ok) throw new Error('Failed to fetch penalties');
      const data = await res.json();
      setPenalties(Array.isArray(data) ? data : data.penalties || []);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch penalties');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: string) => {
    if (!window.confirm('Delete this penalty?')) return;
    try {
      const res = await fetch(`/api/penalties/${id}`, {
        method: 'DELETE',
        credentials: 'include',
      });
      if (!res.ok) throw new Error('Failed to delete');
      setPenalties(penalties.filter(p => p.id !== id));
    } catch (err: any) {
      setError(err.message || 'Delete failed');
    }
  };

  // Filter penalties based on search term and selected rally/stage
  const filteredPenalties = penalties.filter(penalty => {
    // Get related objects
    const rally = rallies.find(r => r.id === penalty.rally_id);
    const stage = stages.find(s => s.id === penalty.stage_id);

    // Search term matching
    const searchLower = searchTerm.toLowerCase();
    const matchesSearch = searchTerm === '' || 
      (rally && rally.name.toLowerCase().includes(searchLower)) ||
      (stage && stage.name.toLowerCase().includes(searchLower)) ||
      (penalty.entry_number && penalty.entry_number.toString().includes(searchLower)) ||
      (penalty.driver_name && penalty.driver_name.toLowerCase().includes(searchLower)) ||
      (penalty.notes && penalty.notes.toLowerCase().includes(searchLower)) ||
      penalty.penalty_type.toLowerCase().includes(searchLower);

    // Filter by selected rally
    const matchesRally = !selectedRally || penalty.rally_id === selectedRally;

    // Filter by selected stage
    const matchesStage = !selectedStage || penalty.stage_id === selectedStage;

    return matchesSearch && matchesRally && matchesStage;
  });

  // Helper function to format penalty type
  const formatPenaltyType = (type: string): string => {
    switch (type) {
      case 'time':
        return 'Time Penalty';
      case 'road':
        return 'Road Penalty';
      case 'other':
        return 'Other';
      default:
        return type;
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Penalties Management</h1>
        <Link
          to="/admin/penalties/add"
          className="bg-red-600 text-white px-4 py-2 rounded-lg flex items-center hover:bg-red-700 transition-colors"
        >
          <AlertTriangle className="w-4 h-4 mr-2" />
          Add Penalty
        </Link>
      </div>

      <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
        <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex flex-col md:flex-row justify-between items-center space-y-2 md:space-y-0">
          <div className="relative w-full md:w-64">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search penalties..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 w-full rounded border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
            />
          </div>
          
          <div className="flex items-center space-x-2">
            <select
              value={selectedRally}
              onChange={(e) => {
                setSelectedRally(e.target.value);
                // Reset stage selection if rally changes
                if (selectedStage) {
                  const stageStillValid = stages.some(
                    s => s.id === selectedStage && s.rally_id === e.target.value
                  );
                  if (!stageStillValid) {
                    setSelectedStage('');
                  }
                }
              }}
              className="px-3 py-2 rounded border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
            >
              <option value="">All Rallies</option>
              {rallies.map(rally => (
                <option key={rally.id} value={rally.id}>{rally.name}</option>
              ))}
            </select>

            <select
              value={selectedStage}
              onChange={(e) => setSelectedStage(e.target.value)}
              className="px-3 py-2 rounded border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
            >
              <option value="">All Stages</option>
              {stages
                .filter(stage => !selectedRally || stage.rally_id === selectedRally)
                .map(stage => (
                  <option key={stage.id} value={stage.id}>SS{stage.number} - {stage.name}</option>
                ))
              }
            </select>

            <button
              onClick={() => {
                fetchPenalties();
                fetchRallies();
                fetchStages();
              }}
              className="flex items-center text-gray-700 dark:text-gray-300 hover:text-red-600 dark:hover:text-red-400"
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Refresh
            </button>
          </div>
        </div>

        {error && (
          <div className="p-4 bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-200">
            {error}
          </div>
        )}

        {loading ? (
          <div className="p-8 text-center">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-4 border-gray-300 border-t-red-600"></div>
            <p className="mt-2 text-gray-600 dark:text-gray-400">Loading penalties...</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Rally
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Stage
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Entry
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Type
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Value
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Notes
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {filteredPenalties.length === 0 ? (
                  <tr>
                    <td colSpan={7} className="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                      No penalties found
                    </td>
                  </tr>
                ) : (
                  filteredPenalties.map((penalty) => {
                    const rally = rallies.find(r => r.id === penalty.rally_id);
                    const stage = stages.find(s => s.id === penalty.stage_id);
                    
                    return (
                      <tr key={penalty.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                          {rally ? rally.name : penalty.rally_name || penalty.rally_id}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                          {stage ? `SS${stage.number} - ${stage.name}` : penalty.stage_name || penalty.stage_id}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                          #{penalty.entry_number || '?'} {penalty.driver_name ? `- ${penalty.driver_name}` : ''}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                          {formatPenaltyType(penalty.penalty_type)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                          {penalty.penalty_value} sec
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400 max-w-xs truncate">
                          {penalty.notes || '-'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <div className="flex justify-end space-x-3">
                            <Link
                              to={`/admin/penalties/${penalty.id}`}
                              className="text-blue-600 hover:text-blue-900 dark:hover:text-blue-400 flex items-center"
                            >
                              <Edit className="w-4 h-4 mr-1" />
                              Edit
                            </Link>
                            <button
                              onClick={() => handleDelete(penalty.id)}
                              className="text-red-600 hover:text-red-900 dark:hover:text-red-400 flex items-center"
                            >
                              Delete
                            </button>
                          </div>
                        </td>
                      </tr>
                    );
                  })
                )}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
};

export default PenaltiesManagementPage;
