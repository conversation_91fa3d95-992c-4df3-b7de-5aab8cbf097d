import express from 'express';
import pool from '../config/db.js';
import { verifyToken, verifyAdmin } from '../middleware/auth.js';

const router = express.Router();

// Create team
router.post('/', verifyToken, verifyAdmin, async (req, res) => {
  try {
    const { name, country, logo_url } = req.body;
    const result = await pool.query(
      `INSERT INTO teams (name, country, logo_url) VALUES ($1, $2, $3) RETURNING *`,
      [name, country, logo_url]
    );
    res.status(201).json(result.rows[0]);
  } catch (error) {
    res.status(500).json({ message: 'Failed to add team' });
  }
});

// Get all teams - PUBLIC ROUTE
router.get('/', async (req, res) => {
  try {
    const result = await pool.query('SELECT * FROM teams ORDER BY name ASC');
    res.json(result.rows);
  } catch (error) {
    res.status(500).json({ message: 'Failed to fetch teams' });
  }
});

// Get single team - PUBLIC ROUTE
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const result = await pool.query('SELECT * FROM teams WHERE id = $1', [id]);
    if (result.rows.length === 0) return res.status(404).json({ message: 'Team not found' });
    res.json(result.rows[0]);
  } catch (error) {
    res.status(500).json({ message: 'Failed to fetch team' });
  }
});

// Update team
router.put('/:id', verifyToken, verifyAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { name, country, logo_url } = req.body;
    const result = await pool.query(
      `UPDATE teams SET name=$1, country=$2, logo_url=$3, updated_at=NOW() WHERE id=$4 RETURNING *`,
      [name, country, logo_url, id]
    );
    if (result.rows.length === 0) return res.status(404).json({ message: 'Team not found' });
    res.json(result.rows[0]);
  } catch (error) {
    res.status(500).json({ message: 'Failed to update team' });
  }
});

// Delete team
router.delete('/:id', verifyToken, verifyAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const result = await pool.query('DELETE FROM teams WHERE id = $1 RETURNING *', [id]);
    if (result.rows.length === 0) return res.status(404).json({ message: 'Team not found' });
    res.json({ message: 'Team deleted', team: result.rows[0] });
  } catch (error) {
    res.status(500).json({ message: 'Failed to delete team' });
  }
});

export default router;
