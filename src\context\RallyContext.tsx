import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { Rally, Driver, Championship, Stage, Entry, Team, Result, PowerStagePoints } from '../types';
import { getCountryCode } from '../utils/countryUtils';

interface RallyContextType {
  rallies: Rally[];
  drivers: Driver[];
  championships: Championship[];
  stages: Stage[];
  entries: Entry[];
  teams: Team[];
  results: Result[];
  powerStagePoints: PowerStagePoints[];
  loading: boolean;
  error: string | null;
  fetchRallies: () => Promise<void>;
  fetchDrivers: () => Promise<void>;
  fetchChampionships: () => Promise<void>;
  fetchStages: () => Promise<void>;
  fetchEntries: () => Promise<void>;
  fetchTeams: () => Promise<void>;
  fetchResults: () => Promise<void>;
  fetchPowerStagePoints: () => Promise<void>;
}

const RallyContext = createContext<RallyContextType | undefined>(undefined);

export const useRallyContext = () => {
  const context = useContext(RallyContext);
  if (context === undefined) {
    throw new Error('useRallyContext must be used within a RallyProvider');
  }
  return context;
};

interface RallyProviderProps {
  children: ReactNode;
}

export const RallyProvider: React.FC<RallyProviderProps> = ({ children }) => {
  const [rallies, setRallies] = useState<Rally[]>([]);
  const [drivers, setDrivers] = useState<Driver[]>([]);
  const [championships, setChampionships] = useState<Championship[]>([]);
  const [stages, setStages] = useState<Stage[]>([]);
  const [entries, setEntries] = useState<Entry[]>([]);
  const [teams, setTeams] = useState<Team[]>([]);
  const [results, setResults] = useState<Result[]>([]);
  const [powerStagePoints, setPowerStagePoints] = useState<PowerStagePoints[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const fetchRallies = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/rallies');
      if (!response.ok) throw new Error('Failed to fetch rallies');
      const data = await response.json();
      setRallies(Array.isArray(data) ? data : data.rallies || []);
      setLoading(false);
    } catch (err) {
      setError('Failed to fetch rallies');
      setLoading(false);
    }
  };

  const fetchDrivers = async () => {
    try {
      setLoading(true);

      // Fetch drivers and driver stats in parallel
      const [driversResponse, statsResponse] = await Promise.all([
        fetch('/api/drivers'),
        fetch('/api/driverStats')
      ]);

      if (!driversResponse.ok) throw new Error('Failed to fetch drivers');
      if (!statsResponse.ok) throw new Error('Failed to fetch driver statistics');

      const driversData = await driversResponse.json();
      const statsData = await statsResponse.json();

      // Create a map of driver stats by driver_id for quick lookup
      const statsMap = statsData.reduce((map: Record<string, any>, stat: any) => {
        map[stat.driver_id] = stat;
        return map;
      }, {});

      // Transform driver data to match frontend expectations
      const transformedDrivers = (Array.isArray(driversData) ? driversData : driversData.drivers || []).map(driver => {
        // Get country code for flag using the utility function
        const countryCode = getCountryCode(driver.nationality);

        // Get stats for this driver
        const driverStats = statsMap[driver.id] || {
          rallies_participated: 0,
          wins: 0,
          podiums: 0
        };

        return {
          ...driver,
          // Combine first_name and last_name into name
          name: `${driver.first_name} ${driver.last_name}`,
          // Add stats from the API
          totalWins: driverStats.wins || 0,
          totalRallies: driverStats.rallies_participated || 0,
          totalPodiums: driverStats.podiums || 0,
          careerPoints: 0, // We'll calculate this later if needed
          // Add flag class and image URL
          flagCode: countryCode,
          flagImage: countryCode ? `https://cdn.jsdelivr.net/gh/lipis/flag-icons@7.0.0/flags/4x3/${countryCode}.svg` : ''
        };
      });

      setDrivers(transformedDrivers);
      setLoading(false);
    } catch (err) {
      setError('Failed to fetch drivers');
      setLoading(false);
    }
  };

  const fetchChampionships = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/championships');
      if (!response.ok) throw new Error('Failed to fetch championships');
      const data = await response.json();
      setChampionships(Array.isArray(data) ? data : data.championships || []);
      setLoading(false);
    } catch (err) {
      setError('Failed to fetch championships');
      setLoading(false);
    }
  };

  const fetchStages = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/stages');
      if (!response.ok) throw new Error('Failed to fetch stages');
      const data = await response.json();
      setStages(Array.isArray(data) ? data : data.stages || []);
      setLoading(false);
    } catch (err) {
      setError('Failed to fetch stages');
      setLoading(false);
    }
  };

  const fetchEntries = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/entries');
      if (!response.ok) throw new Error('Failed to fetch entries');
      const data = await response.json();
      setEntries(Array.isArray(data) ? data : data.entries || []);
      setLoading(false);
    } catch (err) {
      setError('Failed to fetch entries');
      setLoading(false);
    }
  };

  const fetchTeams = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/teams');
      if (!response.ok) throw new Error('Failed to fetch teams');
      const data = await response.json();
      setTeams(Array.isArray(data) ? data : data.teams || []);
      setLoading(false);
    } catch (err) {
      setError('Failed to fetch teams');
      setLoading(false);
    }
  };

  const fetchResults = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/stageResults');
      if (!response.ok) throw new Error('Failed to fetch results');
      const data = await response.json();
      setResults(Array.isArray(data) ? data : data.results || []);
      setLoading(false);
    } catch (err) {
      setError('Failed to fetch results');
      setLoading(false);
    }
  };

  const fetchPowerStagePoints = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/powerStagePoints');
      if (!response.ok) throw new Error('Failed to fetch power stage points');
      const data = await response.json();
      setPowerStagePoints(Array.isArray(data) ? data : data.powerStagePoints || []);
      setLoading(false);
    } catch (err) {
      setError('Failed to fetch power stage points');
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchRallies();
    fetchDrivers();
    fetchChampionships();
    fetchStages();
    fetchEntries();
    fetchTeams();
    // Fetch results and power stage points only when needed
    // fetchResults();
    // fetchPowerStagePoints();
  }, []);

  return (
    <RallyContext.Provider
      value={{
        rallies,
        drivers,
        championships,
        stages,
        entries,
        teams,
        results,
        powerStagePoints,
        loading,
        error,
        fetchRallies,
        fetchDrivers,
        fetchChampionships,
        fetchStages,
        fetchEntries,
        fetchTeams,
        fetchResults,
        fetchPowerStagePoints,
      }}
    >
      {children}
    </RallyContext.Provider>
  );
};