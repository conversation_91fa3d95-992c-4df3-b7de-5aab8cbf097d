import express from 'express';
import pool from '../config/db.js';
import { verifyToken, verifyAdmin } from '../middleware/auth.js';

const router = express.Router();

// Create stage
router.post('/', verifyToken, verifyAdmin, async (req, res) => {
  try {
    const {
      rally_id,
      number,
      name,
      length,
      surface,
      start_time,
      is_super_special,
      is_power_stage,
      status,
      leg_number,
      day_number
    } = req.body;

    const result = await pool.query(
      `INSERT INTO stages (
        rally_id, number, name, length, surface, start_time,
        is_super_special, is_power_stage, status, leg_number, day_number
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11) RETURNING *`,
      [
        rally_id, number, name, length, surface, start_time,
        is_super_special, is_power_stage, status.toLowerCase(), leg_number, day_number
      ]
    );
    res.status(201).json(result.rows[0]);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Failed to add stage' });
  }
});

// Get all stages (with rally name) - PUBLIC ROUTE
router.get('/', async (req, res) => {
  try {
    const { rally_id } = req.query;

    let query = `
      SELECT s.*, r.name AS rally_name
      FROM stages s
      JOIN rallies r ON s.rally_id = r.id
    `;

    const queryParams = [];

    if (rally_id) {
      query += ` WHERE s.rally_id = $1`;
      queryParams.push(rally_id);
    }

    query += ` ORDER BY s.number ASC`;

    const result = await pool.query(query, queryParams);
    res.json(result.rows);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Failed to fetch stages' });
  }
});

// Get single stage (with rally name) - PUBLIC ROUTE
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const result = await pool.query(`
      SELECT s.*, r.name AS rally_name
      FROM stages s
      JOIN rallies r ON s.rally_id = r.id
      WHERE s.id = $1
    `, [id]);
    if (result.rows.length === 0) return res.status(404).json({ message: 'Stage not found' });
    res.json(result.rows[0]);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Failed to fetch stage' });
  }
});

// Update stage
router.put('/:id', verifyToken, verifyAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const {
      rally_id,
      number,
      name,
      length,
      surface,
      start_time,
      is_super_special,
      is_power_stage,
      status,
      leg_number,
      day_number
    } = req.body;

    const result = await pool.query(
      `UPDATE stages SET
        rally_id=$1,
        number=$2,
        name=$3,
        length=$4,
        surface=$5,
        start_time=$6,
        is_super_special=$7,
        is_power_stage=$8,
        status=$9,
        leg_number=$10,
        day_number=$11,
        updated_at=NOW()
      WHERE id=$12 RETURNING *`,
      [
        rally_id,
        number,
        name,
        length,
        surface,
        start_time,
        is_super_special,
        is_power_stage,
        status.toLowerCase(),
        leg_number,
        day_number,
        id
      ]
    );
    if (result.rows.length === 0) return res.status(404).json({ message: 'Stage not found' });
    res.json(result.rows[0]);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Failed to update stage' });
  }
});

// Delete stage
router.delete('/:id', verifyToken, verifyAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const result = await pool.query('DELETE FROM stages WHERE id = $1 RETURNING *', [id]);
    if (result.rows.length === 0) return res.status(404).json({ message: 'Stage not found' });
    res.json({ message: 'Stage deleted', stage: result.rows[0] });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Failed to delete stage' });
  }
});

export default router;
