import React from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { useRallyContext } from '../context/RallyContext';
import {
  Calendar, MapPin, Trophy, AlertTriangle, Award,
  Flag, Car, ChevronLeft, Activity
} from 'lucide-react';
import { Rally } from '../types';

const DriverProfilePage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const { drivers, rallies, entries, teams, loading } = useRallyContext();

  const driver = drivers.find(d => d.id === id);

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen pt-16">
        <div className="inline-block animate-spin rounded-full h-8 w-8 border-4 border-gray-300 border-t-red-600"></div>
        <p className="ml-2">Loading driver profile...</p>
      </div>
    );
  }

  if (!driver) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen pt-16 px-4">
        <div className="text-center">
          <AlertTriangle className="w-12 h-12 text-red-600 mb-4 mx-auto" />
          <h2 className="text-2xl font-bold mb-2">Driver Not Found</h2>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            The driver you're looking for doesn't exist or has been removed.
          </p>
          <Link
            to="/drivers"
            className="bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded-lg flex items-center justify-center w-40 mx-auto"
          >
            <ChevronLeft className="w-4 h-4 mr-2" />
            All Drivers
          </Link>
        </div>
      </div>
    );
  }

  // Find entries for this driver
  const driverEntries = entries.filter(entry => entry.driver_id === driver.id);

  // Find rallies where this driver participated
  const driverRallies = rallies.filter(rally =>
    driverEntries.some(entry => entry.rally_id === rally.id)
  );

  // Helper function to find entry for a specific rally
  const findEntryForRally = (rallyId: string) => {
    return driverEntries.find(entry => entry.rally_id === rallyId);
  };

  // Find teams the driver has participated with
  const getDriverTeams = () => {
    // Get unique team IDs from driver's entries (excluding null/undefined)
    const teamIds = [...new Set(driverEntries
      .filter(entry => entry.team_id)
      .map(entry => entry.team_id)
    )];

    // Map team IDs to team objects and add participation info
    return teamIds.map(teamId => {
      const team = teams.find(t => t.id === teamId);
      if (!team) return null;

      // Find rallies where driver participated with this team
      const teamEntries = driverEntries.filter(entry => entry.team_id === teamId);
      const teamRallies = rallies.filter(rally =>
        teamEntries.some(entry => entry.rally_id === rally.id)
      );

      // Get date range for participation
      const rallyDates = teamRallies
        .map(rally => new Date(rally.start_date))
        .sort((a, b) => a.getTime() - b.getTime());

      const firstRally = rallyDates[0];
      const lastRally = rallyDates[rallyDates.length - 1];

      // Format participation period
      const startYear = firstRally?.getFullYear();
      const endYear = lastRally?.getFullYear();
      const isOngoing = teamRallies.some(rally => rally.status === 'upcoming' || rally.status === 'running');

      let period = '';
      if (startYear && endYear) {
        if (startYear === endYear) {
          period = `${startYear}`;
        } else {
          period = `${startYear} - ${isOngoing ? 'Present' : endYear}`;
        }
      }

      return {
        ...team,
        participationPeriod: period,
        rallyCount: teamRallies.length
      };
    }).filter(Boolean); // Remove null entries
  };

  const driverTeams = getDriverTeams();

  // Format functions
  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return 'N/A';
    const options: Intl.DateTimeFormatOptions = { day: 'numeric', month: 'long', year: 'numeric' };
    return new Date(dateString).toLocaleDateString('en-GB', options);
  };

  // Get driver age
  const getAge = (birthdate: string | undefined) => {
    if (!birthdate) return 'N/A';
    const today = new Date();
    const birthDate = new Date(birthdate);
    let age = today.getFullYear() - birthDate.getFullYear();
    const m = today.getMonth() - birthDate.getMonth();
    if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age;
  };

  // Helper to get result position
  const getPositionInRally = (rally: Rally) => {
    const entry = findEntryForRally(rally.id);
    return entry?.position || 'N/A';
  };

  // Helper to get ordinal suffix (1st, 2nd, 3rd, 4th, etc.)
  const getOrdinalSuffix = (position: number): string => {
    if (!position) return '';

    const j = position % 10;
    const k = position % 100;

    if (j === 1 && k !== 11) {
      return 'st';
    }
    if (j === 2 && k !== 12) {
      return 'nd';
    }
    if (j === 3 && k !== 13) {
      return 'rd';
    }
    return 'th';
  };

  return (
    <div className="pt-16 pb-12 bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        <Link
          to="/drivers"
          className="inline-flex items-center text-gray-600 dark:text-gray-400 hover:text-red-600 dark:hover:text-red-400 mb-6"
        >
          <ChevronLeft className="w-4 h-4 mr-1" />
          Back to Drivers
        </Link>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Driver Info Card */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
            <div className="h-80 relative">
              <img
                src={driver.image || `https://ui-avatars.com/api/?name=${driver.name}&background=random&size=400`}
                alt={driver.name}
                className="w-full h-full object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-gray-900 to-transparent"></div>

              <div className="absolute bottom-0 left-0 right-0 p-6">
                <div className="flex items-center mb-2">
                  <img
                    src={driver.flagImage}
                    alt={driver.nationality}
                    className="w-8 h-6 rounded mr-3 border border-white/30"
                  />
                  <h1 className="text-white text-3xl font-bold">{driver.name}</h1>
                </div>
                {driver.birthdate && (
                  <div className="flex items-center text-gray-200 text-sm">
                    <Calendar className="w-4 h-4 mr-1" />
                    <span>Born {formatDate(driver.birthdate)} ({getAge(driver.birthdate)} years old)</span>
                  </div>
                )}
              </div>
            </div>

            <div className="p-6">
              <div className="grid grid-cols-3 gap-4 text-center mb-6">
                <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                  <div className="text-3xl font-bold text-gray-900 dark:text-white">{driver.totalRallies}</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Rallies</div>
                </div>
                <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                  <div className="text-3xl font-bold text-yellow-600 dark:text-yellow-400">{driver.totalWins}</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Wins</div>
                </div>
                <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                  <div className="text-3xl font-bold text-gray-900 dark:text-white">{driver.totalPodiums}</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Podiums</div>
                </div>
              </div>

              <div className="mb-6">
                <h3 className="text-lg font-semibold mb-2 text-gray-900 dark:text-white flex items-center">
                  <Trophy className="w-5 h-5 mr-2 text-yellow-500" />
                  Career Points
                </h3>
                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Total Points</span>
                    <span className="text-xl font-bold text-gray-900 dark:text-white">{driver.careerPoints}</span>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-semibold mb-2 text-gray-900 dark:text-white flex items-center">
                  <Car className="w-5 h-5 mr-2" />
                  Teams
                </h3>
                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                  {driverTeams.length > 0 ? (
                    <ul className="space-y-3">
                      {driverTeams.map(team => (
                        <li key={team.id} className="flex items-center text-gray-900 dark:text-white">
                          {team.logo_url ? (
                            <img
                              src={team.logo_url}
                              alt={team.name}
                              className="w-8 h-8 object-contain mr-3 rounded"
                              onError={(e) => {
                                // Hide image if it fails to load
                                e.currentTarget.style.display = 'none';
                              }}
                            />
                          ) : (
                            <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded mr-3 flex items-center justify-center">
                              <Car className="w-4 h-4 text-gray-500 dark:text-gray-400" />
                            </div>
                          )}
                          <div>
                            <div className="font-medium">{team.name}</div>
                            <div className="text-xs text-gray-600 dark:text-gray-400">
                              {team.participationPeriod} • {team.rallyCount} {team.rallyCount === 1 ? 'rally' : 'rallies'}
                            </div>
                          </div>
                        </li>
                      ))}
                    </ul>
                  ) : (
                    <div className="text-center text-gray-600 dark:text-gray-400 py-4">
                      <Car className="w-8 h-8 mx-auto mb-2 opacity-50" />
                      <p>No team participation found</p>
                      <p className="text-sm">This driver has competed as a private entry</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Career Stats & Recent Rallies */}
          <div className="lg:col-span-2 space-y-6">
            {/* Career Performance Graph */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <h2 className="font-semibold text-gray-900 dark:text-white text-lg flex items-center">
                  <Activity className="w-5 h-5 mr-2 text-blue-500" />
                  Performance History
                </h2>
              </div>
              <div className="p-6">
                <div className="aspect-[16/9] bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                  <div className="text-center text-gray-600 dark:text-gray-400">
                    <Activity className="w-12 h-12 mx-auto mb-2 opacity-50" />
                    <p>Performance graph will be displayed here</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Recent Rallies */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <h2 className="font-semibold text-gray-900 dark:text-white text-lg flex items-center">
                  <Flag className="w-5 h-5 mr-2 text-red-500" />
                  Recent Rallies
                </h2>
              </div>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <thead className="bg-gray-50 dark:bg-gray-800">
                    <tr>
                      <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Rally
                      </th>
                      <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Date
                      </th>
                      <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider hidden md:table-cell">
                        Car
                      </th>
                      <th scope="col" className="px-4 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Position
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                    {driverRallies.length > 0 ? (
                      driverRallies.map(rally => {
                        const entry = findEntryForRally(rally.id);
                        if (!entry) return null;

                        return (
                          <tr key={rally.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                            <td className="px-4 py-3 whitespace-nowrap">
                              <Link to={`/rallies/${rally.id}`} className="flex items-center group">
                                <span
                                  className={`fi fi-${rally.country?.toLowerCase()} mr-2`}
                                  style={{ width: '24px', height: '18px' }}
                                ></span>
                                <span className="text-gray-900 dark:text-white font-medium group-hover:text-red-600 dark:group-hover:text-red-400 transition-colors">
                                  {rally.name}
                                </span>
                              </Link>
                            </td>
                            <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400">
                              {formatDate(rally.start_date)}
                            </td>
                            <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400 hidden md:table-cell">
                              {entry.car || 'N/A'}
                            </td>
                            <td className="px-4 py-3 whitespace-nowrap text-center">
                              {entry.status === 'running' || entry.status === 'upcoming' ? (
                                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                  {rally.status === 'running' ? 'Live' : 'Entered'}
                                </span>
                              ) : entry.status === 'retired' || entry.status === 'dnf' || entry.status === 'dns' || entry.status === 'dsq' ? (
                                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                                  {entry.status.toUpperCase()}
                                </span>
                              ) : entry.position === 1 ? (
                                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                                  <Trophy className="w-3 h-3 mr-1" />
                                  1st
                                </span>
                              ) : entry.position === 2 ? (
                                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                                  2nd
                                </span>
                              ) : entry.position === 3 ? (
                                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-200">
                                  3rd
                                </span>
                              ) : (
                                <span className="text-gray-900 dark:text-white font-medium">
                                  {entry.position}{getOrdinalSuffix(entry.position)}
                                </span>
                              )}
                            </td>
                          </tr>
                        );
                      })
                    ) : (
                      <tr>
                        <td colSpan={4} className="px-4 py-6 text-center text-gray-600 dark:text-gray-400">
                          No rally data available for this driver
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </div>

            {/* Career Highlights */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <h2 className="font-semibold text-gray-900 dark:text-white text-lg flex items-center">
                  <Award className="w-5 h-5 mr-2 text-yellow-500" />
                  Career Highlights
                </h2>
              </div>
              <div className="p-6">
                <ul className="space-y-4">
                  <li className="flex items-start">
                    <span className="flex-shrink-0 flex items-center justify-center w-8 h-8 rounded-full bg-yellow-100 dark:bg-yellow-900 text-yellow-600 dark:text-yellow-400 mr-3">
                      <Trophy className="w-4 h-4" />
                    </span>
                    <div>
                      <h3 className="text-base font-medium text-gray-900 dark:text-white">WRC Champion 2019</h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        Secured first world title with Toyota Gazoo Racing
                      </p>
                    </div>
                  </li>
                  <li className="flex items-start">
                    <span className="flex-shrink-0 flex items-center justify-center w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 mr-3">
                      <Flag className="w-4 h-4" />
                    </span>
                    <div>
                      <h3 className="text-base font-medium text-gray-900 dark:text-white">Rally Finland Winner 2018, 2019</h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        Consecutive victories at one of the fastest rallies in the championship
                      </p>
                    </div>
                  </li>
                  <li className="flex items-start">
                    <span className="flex-shrink-0 flex items-center justify-center w-8 h-8 rounded-full bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 mr-3">
                      <Award className="w-4 h-4" />
                    </span>
                    <div>
                      <h3 className="text-base font-medium text-gray-900 dark:text-white">WRC 2 Champion 2016</h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        Dominated the WRC 2 category before moving up to the top class
                      </p>
                    </div>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DriverProfilePage;