import React, { useState, useEffect } from 'react';
import { useRallyContext } from '../context/RallyContext';
import DriverCard from '../components/drivers/DriverCard';
import { Filter, Search, Flag, Award, X, Users } from 'lucide-react';
import { Driver } from '../types';

const DriversPage: React.FC = () => {
  const { drivers, loading, error } = useRallyContext();
  const [filteredDrivers, setFilteredDrivers] = useState<Driver[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedNationality, setSelectedNationality] = useState<string | null>(null);
  const [sortBy, setSortBy] = useState<'name' | 'wins' | 'rallies'>('wins');
  const [showFilters, setShowFilters] = useState(false);

  const nationalities = Array.from(new Set(drivers.map(driver => driver.nationality))).sort();

  useEffect(() => {
    let result = [...drivers];

    // Apply nationality filter
    if (selectedNationality) {
      result = result.filter(driver => driver.nationality === selectedNationality);
    }

    // Apply search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      result = result.filter(
        driver => {
          // Check if name exists, otherwise use first_name and last_name
          if (driver.name) {
            return driver.name.toLowerCase().includes(query);
          } else {
            const fullName = `${driver.first_name} ${driver.last_name}`.toLowerCase();
            return fullName.includes(query);
          }
        }
      );
    }

    // Apply sorting
    if (sortBy === 'name') {
      result.sort((a, b) => {
        const nameA = a.name || `${a.first_name} ${a.last_name}`;
        const nameB = b.name || `${b.first_name} ${b.last_name}`;
        return nameA.localeCompare(nameB);
      });
    } else if (sortBy === 'wins') {
      result.sort((a, b) => (b.totalWins || 0) - (a.totalWins || 0));
    } else if (sortBy === 'rallies') {
      result.sort((a, b) => (b.totalRallies || 0) - (a.totalRallies || 0));
    }

    setFilteredDrivers(result);
  }, [drivers, selectedNationality, searchQuery, sortBy]);

  const clearFilters = () => {
    setSelectedNationality(null);
    setSearchQuery('');
  };

  const toggleFilters = () => {
    setShowFilters(!showFilters);
  };

  return (
    <div className="pt-16 pb-12 min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="bg-gray-800 text-white py-12">
        <div className="container mx-auto px-4">
          <h1 className="text-3xl font-bold mb-4">Drivers</h1>
          <p className="text-gray-300 max-w-2xl">
            Explore rally drivers from around the world. Filter by nationality, search for specific drivers, and view detailed statistics.
          </p>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        {/* Search and Filters */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 mb-8">
          <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
            <div className="relative flex-grow max-w-md">
              <Search className="absolute top-2.5 left-3 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search drivers..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 w-full border border-gray-300 dark:border-gray-700 rounded-lg py-2 px-4 focus:outline-none focus:ring-2 focus:ring-red-500 dark:bg-gray-700 dark:text-white"
              />
              {searchQuery && (
                <button
                  onClick={() => setSearchQuery('')}
                  className="absolute right-2 top-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200"
                >
                  <X className="h-5 w-5" />
                </button>
              )}
            </div>

            <div className="flex items-center space-x-2">
              <div>
                <label className="text-sm text-gray-600 dark:text-gray-400 mr-2">Sort by:</label>
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value as 'name' | 'wins' | 'rallies')}
                  className="border border-gray-300 dark:border-gray-700 rounded-lg py-1.5 px-3 focus:outline-none focus:ring-2 focus:ring-red-500 dark:bg-gray-700 dark:text-white text-sm"
                >
                  <option value="wins">Wins</option>
                  <option value="rallies">Rallies</option>
                  <option value="name">Name</option>
                </select>
              </div>

              <button
                onClick={toggleFilters}
                className="flex items-center space-x-1 bg-gray-100 dark:bg-gray-700 px-3 py-2 rounded-lg text-gray-700 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
              >
                <Filter className="h-4 w-4" />
                <span>Filters</span>
              </button>

              {selectedNationality && (
                <button
                  onClick={clearFilters}
                  className="flex items-center space-x-1 bg-red-100 dark:bg-red-900 px-3 py-2 rounded-lg text-red-700 dark:text-red-200 hover:bg-red-200 dark:hover:bg-red-800 transition-colors"
                >
                  <X className="h-4 w-4" />
                  <span>Clear</span>
                </button>
              )}
            </div>
          </div>

          {/* Expandable Filter Section */}
          {showFilters && (
            <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center">
                  <Flag className="h-4 w-4 mr-1" />
                  Filter by Nationality
                </label>
                <div className="flex flex-wrap gap-2">
                  {nationalities.map(nationality => (
                    <button
                      key={nationality}
                      onClick={() => setSelectedNationality(selectedNationality === nationality ? null : nationality)}
                      className={`px-3 py-1 rounded-full text-sm flex items-center ${
                        selectedNationality === nationality
                          ? 'bg-red-600 text-white'
                          : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                      }`}
                    >
                      <span
                        className={`fi fi-${nationality.toLowerCase()} mr-1.5`}
                        style={{ width: '16px', height: '12px' }}
                      ></span>
                      {nationality}
                    </button>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Active Filters Display */}
        {(selectedNationality || searchQuery) && (
          <div className="flex items-center mb-4 text-sm">
            <span className="text-gray-600 dark:text-gray-400 mr-2">Active filters:</span>
            <div className="flex flex-wrap gap-2">
              {selectedNationality && (
                <span className="bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 px-2 py-1 rounded-full flex items-center">
                  <span
                    className={`fi fi-${selectedNationality.toLowerCase()} mr-1.5`}
                    style={{ width: '16px', height: '12px' }}
                  ></span>
                  {selectedNationality}
                  <button onClick={() => setSelectedNationality(null)} className="ml-1 text-red-600 dark:text-red-300">
                    <X className="h-3 w-3" />
                  </button>
                </span>
              )}
              {searchQuery && (
                <span className="bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 px-2 py-1 rounded-full flex items-center">
                  "{searchQuery}"
                  <button onClick={() => setSearchQuery('')} className="ml-1 text-red-600 dark:text-red-300">
                    <X className="h-3 w-3" />
                  </button>
                </span>
              )}
            </div>
          </div>
        )}

        {/* Results Count */}
        <div className="mb-6">
          <p className="text-gray-600 dark:text-gray-400">
            Showing {filteredDrivers.length} {filteredDrivers.length === 1 ? 'driver' : 'drivers'}
          </p>
        </div>

        {/* Drivers Grid */}
        {loading ? (
          <div className="text-center py-12">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-4 border-gray-300 border-t-red-600"></div>
            <p className="mt-2 text-gray-700 dark:text-gray-300">Loading drivers...</p>
          </div>
        ) : error ? (
          <div className="text-center py-12 text-red-600">
            {error}
          </div>
        ) : filteredDrivers.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-700 dark:text-gray-300">No drivers found matching your criteria.</p>
            <button
              onClick={clearFilters}
              className="mt-4 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
            >
              Clear Filters
            </button>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {filteredDrivers.map(driver => (
              <DriverCard key={driver.id} driver={driver} />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default DriversPage;