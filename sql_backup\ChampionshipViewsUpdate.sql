-- Championship Views Update Script
-- Run this on your existing database to add championship-aware functionality
-- This script is safe to run multiple times

-- ============================================================================
-- STEP 1: Drop existing views that will be replaced
-- ============================================================================

DROP VIEW IF EXISTS championship_standings CASCADE;
DROP VIEW IF EXISTS championship_class_standings CASCADE;
DROP VIEW IF EXISTS championship_points CASCADE;
DROP VIEW IF EXISTS championship_overall_classification CASCADE;

-- ============================================================================
-- STEP 2: Add missing indexes for better performance
-- ============================================================================

-- Add composite index for championship_events if it doesn't exist
CREATE INDEX IF NOT EXISTS idx_championship_events_championship_rally 
ON championship_events(championship_id, rally_id);

-- ============================================================================
-- STEP 3: Create new championship-aware views
-- ============================================================================

-- View: championship_overall_classification
-- Shows rally results filtered by championship context
CREATE VIEW championship_overall_classification AS
SELECT
  ce.championship_id,
  c.name AS championship_name,
  e.rally_id,
  r.name AS rally_name,
  e.id AS entry_id,
  e.number,
  pd.first_name || ' ' || pd.last_name AS driver,
  pcd.first_name || ' ' || pcd.last_name AS codriver,
  e.class,
  e.car,
  SUM(res.time + COALESCE(p.time, 0)) AS total_time,
  RANK() OVER (
    PARTITION BY ce.championship_id, e.rally_id 
    ORDER BY SUM(res.time + COALESCE(p.time, 0))
  ) AS championship_position,
  SUM(res.time + COALESCE(p.time, 0)) - MIN(SUM(res.time + COALESCE(p.time, 0))) OVER (
    PARTITION BY ce.championship_id, e.rally_id
  ) AS time_diff,
  ce.coefficient
FROM championship_events ce
JOIN championships c ON c.id = ce.championship_id
JOIN rallies r ON r.id = ce.rally_id
JOIN entries e ON e.rally_id = ce.rally_id
JOIN results res ON res.entry_id = e.id
JOIN persons pd ON pd.id = e.driver_id
JOIN persons pcd ON pcd.id = e.codriver_id
LEFT JOIN penalties p ON p.entry_id = e.id AND p.stage_id = res.stage_id
WHERE e.status NOT IN ('retired', 'dnf', 'dsq')
GROUP BY ce.championship_id, c.name, e.rally_id, r.name, e.id, e.number, 
         pd.first_name, pd.last_name, pcd.first_name, pcd.last_name, e.class, e.car, ce.coefficient;

-- View: championship_points
-- Calculates points for each driver per rally per championship
CREATE VIEW championship_points AS
SELECT
  ce.championship_id,
  c.name AS championship_name,
  e.rally_id,
  r.name AS rally_name,
  e.driver_id,
  pd.first_name || ' ' || pd.last_name AS driver,
  e.id AS entry_id,
  e.number,
  e.class,
  coc.championship_position,
  coc.total_time,
  CASE 
    WHEN coc.championship_position = 1 THEN 25 * ce.coefficient
    WHEN coc.championship_position = 2 THEN 18 * ce.coefficient
    WHEN coc.championship_position = 3 THEN 15 * ce.coefficient
    WHEN coc.championship_position = 4 THEN 12 * ce.coefficient
    WHEN coc.championship_position = 5 THEN 10 * ce.coefficient
    WHEN coc.championship_position = 6 THEN 8 * ce.coefficient
    WHEN coc.championship_position = 7 THEN 6 * ce.coefficient
    WHEN coc.championship_position = 8 THEN 4 * ce.coefficient
    WHEN coc.championship_position = 9 THEN 2 * ce.coefficient
    WHEN coc.championship_position = 10 THEN 1 * ce.coefficient
    ELSE 0
  END AS rally_points,
  ce.coefficient
FROM championship_events ce
JOIN championships c ON c.id = ce.championship_id
JOIN rallies r ON r.id = ce.rally_id
JOIN championship_overall_classification coc ON coc.championship_id = ce.championship_id 
  AND coc.rally_id = ce.rally_id
JOIN entries e ON e.id = coc.entry_id
JOIN persons pd ON pd.id = e.driver_id;

-- View: championship_standings
-- Overall championship standings with rally points + power stage points
CREATE VIEW championship_standings AS
SELECT
  cp.championship_id,
  cp.championship_name,
  cp.driver_id,
  cp.driver,
  COUNT(DISTINCT cp.rally_id) AS rallies_completed,
  SUM(cp.rally_points) AS total_points,
  RANK() OVER (PARTITION BY cp.championship_id ORDER BY SUM(cp.rally_points) DESC) AS position,
  -- Add power stage points if they exist
  COALESCE(SUM(psp_total.power_stage_points), 0) AS total_power_stage_points,
  SUM(cp.rally_points) + COALESCE(SUM(psp_total.power_stage_points), 0) AS grand_total_points
FROM championship_points cp
LEFT JOIN (
  SELECT 
    ce.championship_id,
    e.driver_id,
    SUM(psp.points) AS power_stage_points
  FROM championship_events ce
  JOIN power_stage_points psp ON psp.rally_id = ce.rally_id
  JOIN entries e ON e.id = psp.entry_id
  GROUP BY ce.championship_id, e.driver_id
) psp_total ON psp_total.championship_id = cp.championship_id AND psp_total.driver_id = cp.driver_id
GROUP BY cp.championship_id, cp.championship_name, cp.driver_id, cp.driver;

-- View: championship_class_standings
-- Class-specific championship standings
CREATE VIEW championship_class_standings AS
SELECT
  cp.championship_id,
  cp.championship_name,
  cp.class,
  cp.driver_id,
  cp.driver,
  COUNT(DISTINCT cp.rally_id) AS rallies_completed,
  SUM(cp.rally_points) AS class_points,
  RANK() OVER (
    PARTITION BY cp.championship_id, cp.class 
    ORDER BY SUM(cp.rally_points) DESC
  ) AS class_position,
  -- Add power stage points if they exist
  COALESCE(SUM(psp_total.power_stage_points), 0) AS total_power_stage_points,
  SUM(cp.rally_points) + COALESCE(SUM(psp_total.power_stage_points), 0) AS grand_total_points
FROM championship_points cp
LEFT JOIN (
  SELECT 
    ce.championship_id,
    e.driver_id,
    e.class,
    SUM(psp.points) AS power_stage_points
  FROM championship_events ce
  JOIN power_stage_points psp ON psp.rally_id = ce.rally_id
  JOIN entries e ON e.id = psp.entry_id
  GROUP BY ce.championship_id, e.driver_id, e.class
) psp_total ON psp_total.championship_id = cp.championship_id 
  AND psp_total.driver_id = cp.driver_id 
  AND psp_total.class = cp.class
GROUP BY cp.championship_id, cp.championship_name, cp.class, cp.driver_id, cp.driver;

-- ============================================================================
-- STEP 4: Verification queries
-- ============================================================================

-- Check that all views were created successfully
SELECT 
  schemaname,
  viewname,
  definition IS NOT NULL as has_definition
FROM pg_views 
WHERE viewname IN (
  'championship_overall_classification',
  'championship_points', 
  'championship_standings',
  'championship_class_standings'
)
ORDER BY viewname;

-- Show row counts for each new view
SELECT 'championship_overall_classification' as view_name, count(*) as row_count 
FROM championship_overall_classification
UNION ALL
SELECT 'championship_points' as view_name, count(*) as row_count 
FROM championship_points
UNION ALL
SELECT 'championship_standings' as view_name, count(*) as row_count 
FROM championship_standings
UNION ALL
SELECT 'championship_class_standings' as view_name, count(*) as row_count 
FROM championship_class_standings;

-- Show sample championship data if available
SELECT 
  championship_name,
  COUNT(DISTINCT rally_id) as rallies,
  COUNT(DISTINCT driver_id) as drivers,
  MAX(coefficient) as max_coefficient
FROM championship_points 
GROUP BY championship_id, championship_name
ORDER BY championship_name;

-- ============================================================================
-- COMPLETION MESSAGE
-- ============================================================================

SELECT 'Championship views update completed successfully!' as status;
