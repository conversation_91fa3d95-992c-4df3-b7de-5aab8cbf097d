import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Trophy } from 'lucide-react';
import ChampionshipForm from '../../../../components/admin/championship-form';

interface Championship {
  id: string;
  name: string;
  year: number;
  type: string;
  description?: string;
  status?: string;
  created_at?: string;
  updated_at?: string;
}

const EditChampionshipPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [championship, setChampionship] = useState<Championship | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const navigate = useNavigate();

  useEffect(() => {
    if (id) {
      fetchChampionship();
    }
  }, [id]);

  const fetchChampionship = async () => {
    try {
      const res = await fetch(`/api/championships/${id}`, { credentials: 'include' });
      if (!res.ok) throw new Error('Failed to fetch championship');
      const data = await res.json();
      setChampionship(data);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (formData: any) => {
    console.log('Updating championship with data:', formData);
    
    const response = await fetch(`/api/championships/${id}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      credentials: 'include',
      body: JSON.stringify(formData)
    });

    // Log the response for debugging
    console.log('Response status:', response.status);
    
    if (!response.ok) {
      const data = await response.json();
      console.error('Error response:', data);
      throw new Error(data.message || data.error || 'Failed to update championship');
    }

    navigate('/admin/championships');
  };

  const handleDelete = async () => {
    const response = await fetch(`/api/championships/${id}`, {
      method: 'DELETE',
      credentials: 'include',
    });

    if (!response.ok) {
      const data = await response.json();
      throw new Error(data.message || data.error || 'Failed to delete championship');
    }

    navigate('/admin/championships');
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8 text-center">
        <div className="inline-block animate-spin rounded-full h-8 w-8 border-4 border-gray-300 border-t-red-600"></div>
        <p className="mt-2 text-gray-600 dark:text-gray-400">Loading championship...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="p-4 bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-200 rounded">
          {error}
        </div>
      </div>
    );
  }

  if (!championship) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="p-4 bg-yellow-100 dark:bg-yellow-900 text-yellow-700 dark:text-yellow-200 rounded">
          Championship not found
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center mb-6">
        <Trophy className="w-6 h-6 text-red-600 mr-2" />
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          Edit Championship: {championship.name}
        </h1>
      </div>

      <ChampionshipForm 
        initialData={championship} 
        onSubmit={handleSubmit}
        onDelete={handleDelete}
      />
    </div>
  );
};

export default EditChampionshipPage;
