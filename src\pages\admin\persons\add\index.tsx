import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { UserPlus } from 'lucide-react';
import PersonForm from '../../../../components/admin/person-form';
import { useAuth } from '../../../../context/AuthContext';

const AddPersonPage: React.FC = () => {
  const [error, setError] = useState('');
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const navigate = useNavigate();
  const { user, isAdmin } = useAuth();

  // Check if user is authenticated and has admin rights
  useEffect(() => {
    if (user && isAdmin) {
      setIsAuthenticated(true);
    } else if (user && !isAdmin) {
      setError('You do not have permission to add persons. Admin rights required.');
    }
  }, [user, isAdmin]);

  const handleSubmit = async (formData: any) => {
    try {
      console.log('Submitting person with data:', formData);

      // Check authentication before submitting
      if (!user || !isAdmin) {
        setError('Authentication required. Please log in as an admin.');
        return;
      }

      const response = await fetch('/api/persons', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify(formData)
      });

      // Log the response for debugging
      console.log('Response status:', response.status);

      const data = await response.json();

      if (!response.ok) {
        console.error('Error response:', data);
        throw new Error(data.message || data.error || 'Failed to add person');
      }

      navigate('/admin/persons');
    } catch (err: any) {
      setError(err.message || 'Failed to add person');
      console.error('Error adding person:', err);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center mb-6">
        <UserPlus className="w-6 h-6 text-red-600 mr-2" />
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Add Person</h1>
      </div>

      {error && (
        <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          {error}
        </div>
      )}

      {isAuthenticated ? (
        <PersonForm onSubmit={handleSubmit} />
      ) : (
        <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 p-4 rounded mb-4">
          {user ? 'Checking permissions...' : 'Please log in to add persons.'}
        </div>
      )}
    </div>
  );
};

export default AddPersonPage;
