import React, { useState, useEffect } from 'react';
import { Clock, MapPin, Calendar, Flag, Timer, Wrench, Fuel, Award, Car } from 'lucide-react';

interface Stage {
  id: string;
  name: string;
  number: number;
}

interface ItineraryItemFormProps {
  initialData?: any;
  rallyId: string;
  onSubmit: (formData: any) => Promise<void>;
  onCancel: () => void;
}

const ItineraryItemForm: React.FC<ItineraryItemFormProps> = ({
  initialData,
  rallyId,
  onSubmit,
  onCancel
}) => {
  const [form, setForm] = useState({
    rally_id: rallyId,
    type: 'stage',
    name: '',
    location: '',
    start_time: '',
    duration: '',
    leg_number: '1',
    day_number: '1',
    order_in_day: '10',
    related_id: '',
    additional_info: {}
  });

  const [stages, setStages] = useState<Stage[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    if (initialData) {
      // Format dates and times for form inputs
      const formattedData = {
        ...initialData,
        start_time: initialData.start_time ? new Date(initialData.start_time).toISOString().slice(0, 16) : '',
        duration: initialData.duration?.toString() || '',
        leg_number: initialData.leg_number?.toString() || '1',
        day_number: initialData.day_number?.toString() || '1',
        order_in_day: initialData.order_in_day?.toString() || '10',
        additional_info: initialData.additional_info || {}
      };
      setForm(formattedData);
    }

    fetchStages();
  }, [initialData, rallyId]);

  const fetchStages = async () => {
    try {
      const res = await fetch(`/api/stages?rally_id=${rallyId}`, { credentials: 'include' });
      if (!res.ok) throw new Error('Failed to fetch stages');
      const data = await res.json();
      setStages(Array.isArray(data) ? data : data.stages || []);
    } catch (err: any) {
      console.error('Failed to fetch stages:', err.message);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;

    if (name === 'type' && value === 'stage') {
      // When type is changed to stage, reset related fields
      setForm(prev => ({
        ...prev,
        [name]: value,
        related_id: '',
        additional_info: {}
      }));
    } else {
      setForm(prev => ({ ...prev, [name]: value }));
    }

    // Clear validation error when field is edited
    if (validationErrors[name]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  const handleAdditionalInfoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setForm(prev => ({
      ...prev,
      additional_info: {
        ...prev.additional_info,
        [name]: value
      }
    }));
  };

  const validateForm = () => {
    const errors: Record<string, string> = {};

    if (!form.type) errors.type = 'Type is required';
    if (!form.name) errors.name = 'Name is required';
    if (!form.start_time) errors.start_time = 'Start time is required';
    if (!form.leg_number) errors.leg_number = 'Leg number is required';
    if (!form.day_number) errors.day_number = 'Day number is required';
    if (!form.order_in_day) errors.order_in_day = 'Order is required';

    if (form.type === 'stage' && !form.related_id) {
      errors.related_id = 'Stage is required';
    }

    if ((form.type === 'service' || form.type === 'regroup') && !form.duration) {
      errors.duration = 'Duration is required';
    }

    if (form.type === 'stage' && form.additional_info && !form.additional_info.length) {
      errors.length = 'Length is required';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setLoading(true);
    setError('');

    try {
      // Format the data for API
      const formattedData = {
        ...form,
        rally_id: rallyId,
        duration: form.duration ? parseInt(form.duration) : null,
        leg_number: parseInt(form.leg_number),
        day_number: parseInt(form.day_number),
        order_in_day: parseInt(form.order_in_day),
        additional_info: form.additional_info
      };

      await onSubmit(formattedData);
    } catch (err: any) {
      console.error('Error saving itinerary item:', err);
      // If the error has a response property, it's likely a fetch error
      if (err.response) {
        try {
          const errorData = await err.response.json();
          setError(errorData.message || errorData.error || 'An error occurred while saving the itinerary item');
        } catch (jsonError) {
          setError(err.message || 'An error occurred while saving the itinerary item');
        }
      } else {
        setError(err.message || 'An error occurred while saving the itinerary item');
      }
    } finally {
      setLoading(false);
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'stage':
        return <Timer className="w-5 h-5 text-red-600" />;
      case 'service':
        return <Wrench className="w-5 h-5 text-blue-600" />;
      case 'regroup':
        return <Clock className="w-5 h-5 text-green-600" />;
      case 'refuel':
        return <Fuel className="w-5 h-5 text-yellow-600" />;
      case 'start':
        return <Flag className="w-5 h-5 text-purple-600" />;
      case 'finish':
        return <Flag className="w-5 h-5 text-red-600" />;
      case 'podium':
        return <Award className="w-5 h-5 text-yellow-600" />;
      case 'parc_ferme':
        return <Car className="w-5 h-5 text-gray-600" />;
      case 'shakedown':
        return <Timer className="w-5 h-5 text-orange-600" />;
      default:
        return <Clock className="w-5 h-5 text-gray-600" />;
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
      {error && (
        <div className="mb-4 p-3 bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-200 rounded-lg">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Type <span className="text-red-500">*</span>
            </label>
            <div className="relative">
              {getTypeIcon(form.type)}
              <select
                name="type"
                value={form.type}
                onChange={handleChange}
                className={`w-full pl-10 pr-3 py-2 rounded-lg border ${
                  validationErrors.type
                    ? 'border-red-500 dark:border-red-500'
                    : 'border-gray-300 dark:border-gray-600'
                } bg-white dark:bg-gray-700 text-gray-900 dark:text-white`}
              >
                <option value="stage">Stage</option>
                <option value="service">Service</option>
                <option value="regroup">Regroup</option>
                <option value="refuel">Refuel</option>
                <option value="start">Start</option>
                <option value="finish">Finish</option>
                <option value="podium">Podium</option>
                <option value="parc_ferme">Parc Fermé</option>
                <option value="shakedown">Shakedown</option>
                <option value="tyre_fitting">Tyre Fitting</option>
                <option value="remote_service">Remote Service</option>
              </select>
            </div>
            {validationErrors.type && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">{validationErrors.type}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Name <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              name="name"
              value={form.name}
              onChange={handleChange}
              className={`w-full px-3 py-2 rounded-lg border ${
                validationErrors.name
                  ? 'border-red-500 dark:border-red-500'
                  : 'border-gray-300 dark:border-gray-600'
              } bg-white dark:bg-gray-700 text-gray-900 dark:text-white`}
              placeholder="Item Name"
            />
            {validationErrors.name && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">{validationErrors.name}</p>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Location
            </label>
            <div className="relative">
              <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500" size={18} />
              <input
                type="text"
                name="location"
                value={form.location}
                onChange={handleChange}
                className="w-full pl-10 pr-3 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                placeholder="Location"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Start Time <span className="text-red-500">*</span>
            </label>
            <div className="relative">
              <Clock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500" size={18} />
              <input
                type="datetime-local"
                name="start_time"
                value={form.start_time}
                onChange={handleChange}
                className={`w-full pl-10 pr-3 py-2 rounded-lg border ${
                  validationErrors.start_time
                    ? 'border-red-500 dark:border-red-500'
                    : 'border-gray-300 dark:border-gray-600'
                } bg-white dark:bg-gray-700 text-gray-900 dark:text-white`}
              />
            </div>
            {validationErrors.start_time && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">{validationErrors.start_time}</p>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Leg Number <span className="text-red-500">*</span>
            </label>
            <input
              type="number"
              min="1"
              name="leg_number"
              value={form.leg_number}
              onChange={handleChange}
              className={`w-full px-3 py-2 rounded-lg border ${
                validationErrors.leg_number
                  ? 'border-red-500 dark:border-red-500'
                  : 'border-gray-300 dark:border-gray-600'
              } bg-white dark:bg-gray-700 text-gray-900 dark:text-white`}
            />
            {validationErrors.leg_number && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">{validationErrors.leg_number}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Day Number <span className="text-red-500">*</span>
            </label>
            <input
              type="number"
              min="1"
              name="day_number"
              value={form.day_number}
              onChange={handleChange}
              className={`w-full px-3 py-2 rounded-lg border ${
                validationErrors.day_number
                  ? 'border-red-500 dark:border-red-500'
                  : 'border-gray-300 dark:border-gray-600'
              } bg-white dark:bg-gray-700 text-gray-900 dark:text-white`}
            />
            {validationErrors.day_number && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">{validationErrors.day_number}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Order in Day <span className="text-red-500">*</span>
            </label>
            <input
              type="number"
              min="1"
              name="order_in_day"
              value={form.order_in_day}
              onChange={handleChange}
              className={`w-full px-3 py-2 rounded-lg border ${
                validationErrors.order_in_day
                  ? 'border-red-500 dark:border-red-500'
                  : 'border-gray-300 dark:border-gray-600'
              } bg-white dark:bg-gray-700 text-gray-900 dark:text-white`}
            />
            {validationErrors.order_in_day && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">{validationErrors.order_in_day}</p>
            )}
          </div>
        </div>

        {/* Type-specific fields */}
        {form.type === 'stage' && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Stage <span className="text-red-500">*</span>
              </label>
              <select
                name="related_id"
                value={form.related_id}
                onChange={handleChange}
                className={`w-full px-3 py-2 rounded-lg border ${
                  validationErrors.related_id
                    ? 'border-red-500 dark:border-red-500'
                    : 'border-gray-300 dark:border-gray-600'
                } bg-white dark:bg-gray-700 text-gray-900 dark:text-white`}
              >
                <option value="">Select Stage</option>
                {stages.map(stage => (
                  <option key={stage.id} value={stage.id}>
                    SS{stage.number}: {stage.name}
                  </option>
                ))}
              </select>
              {validationErrors.related_id && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{validationErrors.related_id}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Length (km) <span className="text-red-500">*</span>
              </label>
              <input
                type="number"
                step="0.01"
                min="0.1"
                name="length"
                value={form.additional_info?.length || ''}
                onChange={handleAdditionalInfoChange}
                className={`w-full px-3 py-2 rounded-lg border ${
                  validationErrors.length
                    ? 'border-red-500 dark:border-red-500'
                    : 'border-gray-300 dark:border-gray-600'
                } bg-white dark:bg-gray-700 text-gray-900 dark:text-white`}
                placeholder="Length in kilometers"
              />
              {validationErrors.length && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{validationErrors.length}</p>
              )}
            </div>
          </div>
        )}

        {(form.type === 'service' || form.type === 'regroup') && (
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Duration (minutes) <span className="text-red-500">*</span>
            </label>
            <input
              type="number"
              min="1"
              name="duration"
              value={form.duration}
              onChange={handleChange}
              className={`w-full px-3 py-2 rounded-lg border ${
                validationErrors.duration
                  ? 'border-red-500 dark:border-red-500'
                  : 'border-gray-300 dark:border-gray-600'
              } bg-white dark:bg-gray-700 text-gray-900 dark:text-white`}
              placeholder="Duration in minutes"
            />
            {validationErrors.duration && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">{validationErrors.duration}</p>
            )}
          </div>
        )}

        <div className="flex justify-end space-x-3 pt-4">
          <button
            type="button"
            onClick={onCancel}
            className="bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 px-4 py-2 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
          >
            Cancel
          </button>

          <button
            type="submit"
            disabled={loading}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg disabled:opacity-50 transition-colors"
          >
            {loading ? 'Saving...' : initialData ? 'Update Item' : 'Add Item'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default ItineraryItemForm;
