import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Navbar from './components/layout/Navbar';
import Footer from './components/layout/Footer';
import HomePage from './pages/HomePage';
import RalliesPage from './pages/RalliesPage';
import RallyDetailsPage from './pages/RallyDetailsPage';
import DriversPage from './pages/DriversPage';
import DriverProfilePage from './pages/DriverProfilePage';
import ChampionshipsPage from './pages/ChampionshipsPage';
import ChampionshipStandingsPage from './pages/ChampionshipStandingsPage';
import NewsPage from './pages/NewsPage';
import NewsDetailsPage from './pages/NewsDetailsPage';
import NotFoundPage from './pages/NotFoundPage';
import DiagnosticsPage from './pages/DiagnosticsPage';
import LoginPage from './pages/admin/LoginPage';
import DashboardPage from './pages/admin/DashboardPage';

// Next.js-style admin pages
import NewsManagementPage from './pages/admin/news';
import AddNewsPage from './pages/admin/news/add';
import EditNewsPage from './pages/admin/news/[id]';
import PenaltiesManagementPage from './pages/admin/penalties';
import AddPenaltyPage from './pages/admin/penalties/add';
import EditPenaltyPage from './pages/admin/penalties/[id]';
import RallyManagementPage from './pages/admin/rallies';
import AddRallyPage from './pages/admin/rallies/add';
import EditRallyPage from './pages/admin/rallies/[id]';
import ImportEWRCPage from './pages/admin/rallies/import';
import StagesManagementPage from './pages/admin/stages';
import AddStagePage from './pages/admin/stages/add';
import EditStagePage from './pages/admin/stages/[id]';
import StageResultsManagementPage from './pages/admin/stageResults';
import AddStageResultPage from './pages/admin/stageResults/add';
import EditStageResultPage from './pages/admin/stageResults/[id]';
import ImportStageResultsPage from './pages/admin/stageResults/import';
import EntriesManagementPage from './pages/admin/entries';
import AddEntryPage from './pages/admin/entries/add';
import EditEntryPage from './pages/admin/entries/[id]';
import ImportEntriesPage from './pages/admin/entries/import';
import PersonsManagementPage from './pages/admin/persons';
import AddPersonPage from './pages/admin/persons/add';
import EditPersonPage from './pages/admin/persons/[id]';
import ImportPersonsPage from './pages/admin/persons/import';
import ChampionshipsManagementPage from './pages/admin/championships';
import AddChampionshipPage from './pages/admin/championships/add';
import EditChampionshipPage from './pages/admin/championships/[id]';
import ShakedownsManagementPage from './pages/admin/shakedowns';
import AddShakedownPage from './pages/admin/shakedowns/add';
import EditShakedownPage from './pages/admin/shakedowns/[id]';

// Components
import ProtectedRoute from './components/admin/ProtectedRoute';
import ErrorBoundary from './components/ErrorBoundary';
import { RallyProvider } from './context/RallyContext';
import { AuthProvider } from './context/AuthContext';

function App() {
  return (
    <ErrorBoundary>
      <AuthProvider>
        <RallyProvider>
          <Router>
            <div className="flex flex-col min-h-screen bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100">
            <Routes>
              {/* Admin Routes */}
              <Route path="/admin/login" element={<LoginPage />} />
              <Route
                path="/admin"
                element={
                  <ProtectedRoute>
                    <>
                      <Navbar />
                      <main className="flex-grow">
                        <DashboardPage />
                      </main>
                      <Footer />
                    </>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/admin/rallies"
                element={
                  <ProtectedRoute>
                    <>
                      <Navbar />
                      <main className="flex-grow">
                        <RallyManagementPage />
                      </main>
                      <Footer />
                    </>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/admin/rallies/add"
                element={
                  <ProtectedRoute>
                    <>
                      <Navbar />
                      <main className="flex-grow">
                        <AddRallyPage />
                      </main>
                      <Footer />
                    </>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/admin/rallies/import"
                element={
                  <ProtectedRoute>
                    <>
                      <Navbar />
                      <main className="flex-grow">
                        <ImportEWRCPage />
                      </main>
                      <Footer />
                    </>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/admin/rallies/:id"
                element={
                  <ProtectedRoute>
                    <>
                      <Navbar />
                      <main className="flex-grow">
                        <EditRallyPage />
                      </main>
                      <Footer />
                    </>
                  </ProtectedRoute>
                }
              />

              <Route
                path="/admin/news"
                element={
                  <ProtectedRoute>
                    <>
                      <Navbar />
                      <main className="flex-grow">
                        <NewsManagementPage />
                      </main>
                      <Footer />
                    </>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/admin/news/add"
                element={
                  <ProtectedRoute>
                    <>
                      <Navbar />
                      <main className="flex-grow">
                        <AddNewsPage />
                      </main>
                      <Footer />
                    </>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/admin/news/:id"
                element={
                  <ProtectedRoute>
                    <>
                      <Navbar />
                      <main className="flex-grow">
                        <EditNewsPage />
                      </main>
                      <Footer />
                    </>
                  </ProtectedRoute>
                }
              />
              {/* Route for results management removed - needs to be reimplemented */}
              {/* <Route
                path="/admin/results"
                element={
                  <ProtectedRoute>
                    <>
                      <Navbar />
                      <main className="flex-grow">
                        <ResultsManagementPage />
                      </main>
                      <Footer />
                    </>
                  </ProtectedRoute>
                }
              /> */}
              <Route
                path="/admin/entries"
                element={
                  <ProtectedRoute>
                    <>
                      <Navbar />
                      <main className="flex-grow">
                        <EntriesManagementPage />
                      </main>
                      <Footer />
                    </>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/admin/entries/add"
                element={
                  <ProtectedRoute>
                    <>
                      <Navbar />
                      <main className="flex-grow">
                        <AddEntryPage />
                      </main>
                      <Footer />
                    </>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/admin/entries/import"
                element={
                  <ProtectedRoute>
                    <>
                      <Navbar />
                      <main className="flex-grow">
                        <ImportEntriesPage />
                      </main>
                      <Footer />
                    </>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/admin/entries/:id"
                element={
                  <ProtectedRoute>
                    <>
                      <Navbar />
                      <main className="flex-grow">
                        <EditEntryPage />
                      </main>
                      <Footer />
                    </>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/admin/penalties"
                element={
                  <ProtectedRoute>
                    <>
                      <Navbar />
                      <main className="flex-grow">
                        <PenaltiesManagementPage />
                      </main>
                      <Footer />
                    </>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/admin/penalties/add"
                element={
                  <ProtectedRoute>
                    <>
                      <Navbar />
                      <main className="flex-grow">
                        <AddPenaltyPage />
                      </main>
                      <Footer />
                    </>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/admin/penalties/:id"
                element={
                  <ProtectedRoute>
                    <>
                      <Navbar />
                      <main className="flex-grow">
                        <EditPenaltyPage />
                      </main>
                      <Footer />
                    </>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/admin/stages"
                element={
                  <ProtectedRoute>
                    <>
                      <Navbar />
                      <main className="flex-grow">
                        <StagesManagementPage />
                      </main>
                      <Footer />
                    </>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/admin/stages/add"
                element={
                  <ProtectedRoute>
                    <>
                      <Navbar />
                      <main className="flex-grow">
                        <AddStagePage />
                      </main>
                      <Footer />
                    </>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/admin/stageResults"
                element={
                  <ProtectedRoute>
                    <>
                      <Navbar />
                      <main className="flex-grow">
                        <StageResultsManagementPage />
                      </main>
                      <Footer />
                    </>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/admin/stageResults/add"
                element={
                  <ProtectedRoute>
                    <>
                      <Navbar />
                      <main className="flex-grow">
                        <AddStageResultPage />
                      </main>
                      <Footer />
                    </>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/admin/stageResults/import"
                element={
                  <ProtectedRoute>
                    <>
                      <Navbar />
                      <main className="flex-grow">
                        <ImportStageResultsPage />
                      </main>
                      <Footer />
                    </>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/admin/stageResults/:id"
                element={
                  <ProtectedRoute>
                    <>
                      <Navbar />
                      <main className="flex-grow">
                        <EditStageResultPage />
                      </main>
                      <Footer />
                    </>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/admin/stages/:id"
                element={
                  <ProtectedRoute>
                    <>
                      <Navbar />
                      <main className="flex-grow">
                        <EditStagePage />
                      </main>
                      <Footer />
                    </>
                  </ProtectedRoute>
                }
              />

              {/* Routes for power stage points, splits, and teams removed - need to be reimplemented */}
              {/* <Route
                path="/admin/powerstagepoints/add"
                element={
                  <ProtectedRoute>
                    <>
                      <Navbar />
                      <main className="flex-grow">
                        <AddPowerStagePointsPage />
                      </main>
                      <Footer />
                    </>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/admin/splits/add"
                element={
                  <ProtectedRoute>
                    <>
                      <Navbar />
                      <main className="flex-grow">
                        <AddSplitPage />
                      </main>
                      <Footer />
                    </>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/admin/teams/add"
                element={
                  <ProtectedRoute>
                    <>
                      <Navbar />
                      <main className="flex-grow">
                        <AddTeamPage />
                      </main>
                      <Footer />
                    </>
                  </ProtectedRoute>
                }
              /> */}
              <Route
                path="/admin/championships"
                element={
                  <ProtectedRoute>
                    <>
                      <Navbar />
                      <main className="flex-grow">
                        <ChampionshipsManagementPage />
                      </main>
                      <Footer />
                    </>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/admin/championships/manage"
                element={
                  <ProtectedRoute>
                    <>
                      <Navbar />
                      <main className="flex-grow">
                        <ChampionshipsManagementPage />
                      </main>
                      <Footer />
                    </>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/admin/championships/add"
                element={
                  <ProtectedRoute>
                    <>
                      <Navbar />
                      <main className="flex-grow">
                        <AddChampionshipPage />
                      </main>
                      <Footer />
                    </>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/admin/championships/:id"
                element={
                  <ProtectedRoute>
                    <>
                      <Navbar />
                      <main className="flex-grow">
                        <EditChampionshipPage />
                      </main>
                      <Footer />
                    </>
                  </ProtectedRoute>
                }
              />

              {/* Shakedown Routes */}
              <Route
                path="/admin/shakedowns"
                element={
                  <ProtectedRoute>
                    <>
                      <Navbar />
                      <main className="flex-grow">
                        <ShakedownsManagementPage />
                      </main>
                      <Footer />
                    </>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/admin/shakedowns/add"
                element={
                  <ProtectedRoute>
                    <>
                      <Navbar />
                      <main className="flex-grow">
                        <AddShakedownPage />
                      </main>
                      <Footer />
                    </>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/admin/shakedowns/:id"
                element={
                  <ProtectedRoute>
                    <>
                      <Navbar />
                      <main className="flex-grow">
                        <EditShakedownPage />
                      </main>
                      <Footer />
                    </>
                  </ProtectedRoute>
                }
              />

              {/* Routes for power stage points, splits, and teams management removed - need to be reimplemented */}
              {/* <Route
                path="/admin/powerstagepoints"
                element={
                  <ProtectedRoute>
                    <>
                      <Navbar />
                      <main className="flex-grow">
                        <PowerStagePointsManagementPage />
                      </main>
                      <Footer />
                    </>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/admin/splits"
                element={
                  <ProtectedRoute>
                    <>
                      <Navbar />
                      <main className="flex-grow">
                        <SplitsManagementPage />
                      </main>
                      <Footer />
                    </>
                  </ProtectedRoute>
                }
              />


              <Route
                path="/admin/powerstagepoints/:id"
                element={
                  <ProtectedRoute>
                    <>
                      <Navbar />
                      <main className="flex-grow">
                        <EditPowerStagePointsPage />
                      </main>
                      <Footer />
                    </>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/admin/splits/:id"
                element={
                  <ProtectedRoute>
                    <>
                      <Navbar />
                      <main className="flex-grow">
                        <EditSplitPage />
                      </main>
                      <Footer />
                    </>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/admin/teams/:id"
                element={
                  <ProtectedRoute>
                    <>
                      <Navbar />
                      <main className="flex-grow">
                        <EditTeamPage />
                      </main>
                      <Footer />
                    </>
                  </ProtectedRoute>
                }
              /> */}
              <Route
                path="/admin/persons"
                element={
                  <ProtectedRoute>
                    <>
                      <Navbar />
                      <main className="flex-grow">
                        <PersonsManagementPage />
                      </main>
                      <Footer />
                    </>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/admin/persons/add"
                element={
                  <ProtectedRoute>
                    <>
                      <Navbar />
                      <main className="flex-grow">
                        <AddPersonPage />
                      </main>
                      <Footer />
                    </>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/admin/persons/import"
                element={
                  <ProtectedRoute>
                    <>
                      <Navbar />
                      <main className="flex-grow">
                        <ImportPersonsPage />
                      </main>
                      <Footer />
                    </>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/admin/persons/:id"
                element={
                  <ProtectedRoute>
                    <>
                      <Navbar />
                      <main className="flex-grow">
                        <EditPersonPage />
                      </main>
                      <Footer />
                    </>
                  </ProtectedRoute>
                }
              />

              {/* Public Routes */}
              <Route
                path="/"
                element={
                  <>
                    <Navbar />
                    <main className="flex-grow">
                      <HomePage />
                    </main>
                    <Footer />
                  </>
                }
              />
              <Route
                path="/rallies"
                element={
                  <>
                    <Navbar />
                    <main className="flex-grow">
                      <RalliesPage />
                    </main>
                    <Footer />
                  </>
                }
              />
              <Route
                path="/rallies/:id"
                element={
                  <>
                    <Navbar />
                    <main className="flex-grow">
                      <RallyDetailsPage />
                    </main>
                    <Footer />
                  </>
                }
              />
              <Route
                path="/drivers"
                element={
                  <>
                    <Navbar />
                    <main className="flex-grow">
                      <DriversPage />
                    </main>
                    <Footer />
                  </>
                }
              />
              <Route
                path="/drivers/:id"
                element={
                  <>
                    <Navbar />
                    <main className="flex-grow">
                      <DriverProfilePage />
                    </main>
                    <Footer />
                  </>
                }
              />
              <Route
                path="/championships"
                element={
                  <>
                    <Navbar />
                    <main className="flex-grow">
                      <ChampionshipsPage />
                    </main>
                    <Footer />
                  </>
                }
              />
              <Route
                path="/championships/:id"
                element={
                  <>
                    <Navbar />
                    <main className="flex-grow">
                      <ChampionshipStandingsPage />
                    </main>
                    <Footer />
                  </>
                }
              />
              <Route
                path="/news"
                element={
                  <>
                    <Navbar />
                    <main className="flex-grow">
                      <NewsPage />
                    </main>
                    <Footer />
                  </>
                }
              />
              <Route
                path="/news/:id"
                element={
                  <>
                    <Navbar />
                    <main className="flex-grow">
                      <NewsDetailsPage />
                    </main>
                    <Footer />
                  </>
                }
              />
              <Route
                path="/diagnostics"
                element={
                  <>
                    <Navbar />
                    <main className="flex-grow">
                      <DiagnosticsPage />
                    </main>
                    <Footer />
                  </>
                }
              />
              <Route
                path="*"
                element={
                  <>
                    <Navbar />
                    <main className="flex-grow">
                      <NotFoundPage />
                    </main>
                    <Footer />
                  </>
                }
              />
            </Routes>
          </div>
        </Router>
      </RallyProvider>
    </AuthProvider>
    </ErrorBoundary>
  );
}

export default App;