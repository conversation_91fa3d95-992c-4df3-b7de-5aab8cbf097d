import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Flag, Route } from 'lucide-react';
import RallyForm from '../../../../components/admin/rally-form';
import StagesManager from '../../../../components/admin/stages-manager';
import EntriesManager from '../../../../components/admin/entries-manager';
import StageResultsManager from '../../../../components/admin/stage-results-manager';
import ItineraryTab from './itinerary';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '../../../../components/ui/tabs';

interface Rally {
  id: string;
  name: string;
  country: string;
  start_date: string;
  end_date: string;
  status: string;
  surface: string;
  championship_id: string | null;
  logo_url: string | null;
  banner_url: string | null;
}

interface Championship {
  id: string;
  name: string;
  year: number;
}

const EditRallyPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [rally, setRally] = useState<Rally | null>(null);
  const [championships, setChampionships] = useState<Championship[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const navigate = useNavigate();

  useEffect(() => {
    if (id) {
      fetchRally();
      fetchChampionships();
    }
  }, [id]);

  const fetchRally = async () => {
    try {
      const res = await fetch(`/api/rallies/${id}`, { credentials: 'include' });
      if (!res.ok) throw new Error('Failed to fetch rally');
      const data = await res.json();
      setRally(data);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const fetchChampionships = async () => {
    try {
      const res = await fetch('/api/championships', { credentials: 'include' });
      if (!res.ok) throw new Error('Failed to fetch championships');
      const data = await res.json();
      setChampionships(Array.isArray(data) ? data : data.championships || []);
    } catch (err: any) {
      setError(err.message);
    }
  };

  const handleSubmit = async (formData: any) => {
    // Extract championships from formData
    const { championships, ...rallyData } = formData;

    // Update rally basic data
    const response = await fetch(`/api/rallies/${id}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      credentials: 'include',
      body: JSON.stringify(rallyData)
    });

    if (!response.ok) {
      const data = await response.json();
      throw new Error(data.message || 'Failed to update rally');
    }

    // Update rally championships if provided
    if (championships) {
      const championshipsResponse = await fetch(`/api/rally-championships/rally/${id}/championships`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({ championships })
      });

      if (!championshipsResponse.ok) {
        const errorData = await championshipsResponse.json();
        throw new Error(`Failed to update championships: ${errorData.message}`);
      }
    }

    // Refresh the rally data
    fetchRally();
  };

  const handleDelete = async () => {
    const response = await fetch(`/api/rallies/${id}`, {
      method: 'DELETE',
      credentials: 'include',
    });

    if (!response.ok) {
      const data = await response.json();
      throw new Error(data.message || 'Failed to delete rally');
    }

    navigate('/admin/rallies');
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8 text-center">
        <div className="inline-block animate-spin rounded-full h-8 w-8 border-4 border-gray-300 border-t-red-600"></div>
        <p className="mt-2 text-gray-600 dark:text-gray-400">Loading rally...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="p-4 bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-200 rounded">
          {error}
        </div>
      </div>
    );
  }

  if (!rally) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="p-4 bg-yellow-100 dark:bg-yellow-900 text-yellow-700 dark:text-yellow-200 rounded">
          Rally not found
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center mb-6">
        <Flag className="w-6 h-6 text-red-600 mr-2" />
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Edit Rally: {rally.name}</h1>
      </div>

      <Tabs defaultValue="details" className="w-full">
        <TabsList className="mb-6">
          <TabsTrigger value="details">Rally Details</TabsTrigger>
          <TabsTrigger value="stages">Stages</TabsTrigger>
          <TabsTrigger value="entries">Entries</TabsTrigger>
          <TabsTrigger value="results">Stage Results</TabsTrigger>
          <TabsTrigger value="itinerary">Itinerary</TabsTrigger>
        </TabsList>

        <TabsContent value="details">
          <RallyForm
            initialData={rally}
            championships={championships}
            onSubmit={handleSubmit}
            onDelete={handleDelete}
          />
        </TabsContent>

        <TabsContent value="stages">
          <StagesManager rallyId={id || ''} />
        </TabsContent>

        <TabsContent value="entries">
          <EntriesManager rallyId={id || ''} />
        </TabsContent>

        <TabsContent value="results">
          <StageResultsManager rallyId={id || ''} />
        </TabsContent>

        <TabsContent value="itinerary">
          <ItineraryTab />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default EditRallyPage;
