import express from 'express';
import pool from '../config/db.js';
import { verifyToken, verifyAdmin } from '../middleware/auth.js';
import multer from 'multer';
import { parse } from 'csv-parse/sync';

const router = express.Router();

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, 'uploads/');
  },
  filename: function (req, file, cb) {
    cb(null, `${Date.now()}-${file.originalname}`);
  }
});

const upload = multer({ storage: storage });

// Create stage result
router.post('/', verifyToken, verifyAdmin, async (req, res) => {
  try {
    const { rally_id, stage_id, entry_id, time, nominal_time, super_rally, override_status } = req.body;

    // Check entry status before adding result
    const entryStatusResult = await pool.query(
      `SELECT status FROM entries WHERE id = $1`,
      [entry_id]
    );

    if (entryStatusResult.rows.length === 0) {
      return res.status(404).json({ message: 'Entry not found' });
    }

    const entryStatus = entryStatusResult.rows[0].status;

    // Prevent adding results for entries with retired/DNS/DNF/DSQ status unless override_status is true
    const invalidStatuses = ['retired', 'dns', 'dnf', 'dsq'];
    if (invalidStatuses.includes(entryStatus) && override_status !== 'true' && override_status !== true) {
      return res.status(400).json({
        message: `Cannot add result for entry with status: ${entryStatus}. Use override_status=true to override.`,
        status: entryStatus
      });
    }

    // Determine if entry is active based on current status
    const isActive = !invalidStatuses.includes(entryStatus);

    const result = await pool.query(
      `INSERT INTO results (rally_id, stage_id, entry_id, time, nominal_time, super_rally, is_active) VALUES ($1, $2, $3, $4, $5, $6, $7) RETURNING *`,
      [rally_id, stage_id, entry_id, time, nominal_time || false, super_rally || false, isActive]
    );
    res.status(201).json(result.rows[0]);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Failed to add stage result' });
  }
});

// Get all stage results (with entry, stage, and rally info) - PUBLIC ROUTE
router.get('/', async (req, res) => {
  try {
    const { stage_id, rally_id } = req.query;

    let query = `
      SELECT r.*,
        e.number AS entry_number,
        e.status AS entry_status,
        s.name AS stage_name,
        ra.name AS rally_name,
        pd.first_name AS driver_first_name,
        pd.last_name AS driver_last_name,
        pcd.first_name AS codriver_first_name,
        pcd.last_name AS codriver_last_name,
        p.time AS penalty_time,
        p.reason AS penalty_reason
      FROM results r
      JOIN entries e ON r.entry_id = e.id
      JOIN stages s ON r.stage_id = s.id
      JOIN rallies ra ON r.rally_id = ra.id
      JOIN persons pd ON e.driver_id = pd.id
      JOIN persons pcd ON e.codriver_id = pcd.id
      LEFT JOIN penalties p ON p.stage_id = r.stage_id AND p.entry_id = r.entry_id
    `;

    const queryParams = [];
    const conditions = [];

    if (stage_id) {
      conditions.push(`r.stage_id = $${queryParams.length + 1}`);
      queryParams.push(stage_id);
    }

    if (rally_id) {
      conditions.push(`r.rally_id = $${queryParams.length + 1}`);
      queryParams.push(rally_id);
    }

    if (conditions.length > 0) {
      query += ` WHERE ${conditions.join(' AND ')}`;
    }

    query += ` ORDER BY r.time ASC`;

    const result = await pool.query(query, queryParams);

    // Format the results to include driver and codriver names
    const formattedResults = result.rows.map(row => ({
      ...row,
      driver_name: `${row.driver_first_name} ${row.driver_last_name}`,
      codriver_name: `${row.codriver_first_name} ${row.codriver_last_name}`
    }));

    res.json(formattedResults);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Failed to fetch stage results' });
  }
});

// Get single stage result (with entry, stage, and rally info) - PUBLIC ROUTE
router.get('/:id', async (req, res) => {
  try {
    const stageResultId = req.params.id;
    const result = await pool.query(`
      SELECT r.*, e.number AS entry_number, s.name AS stage_name, ra.name AS rally_name
      FROM results r
      JOIN entries e ON r.entry_id = e.id
      JOIN stages s ON r.stage_id = s.id
      JOIN rallies ra ON r.rally_id = ra.id
      WHERE r.id = $1
    `, [stageResultId]);
    if (result.rows.length === 0) return res.status(404).json({ message: 'Stage result not found' });
    res.json(result.rows[0]);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Failed to fetch stage result' });
  }
});

// Update stage result
router.put('/:id', verifyToken, verifyAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { rally_id, stage_id, entry_id, time, nominal_time, super_rally, override_status } = req.body;

    // Check entry status before updating result
    const entryStatusResult = await pool.query(
      `SELECT status FROM entries WHERE id = $1`,
      [entry_id]
    );

    if (entryStatusResult.rows.length === 0) {
      return res.status(404).json({ message: 'Entry not found' });
    }

    const entryStatus = entryStatusResult.rows[0].status;

    // Prevent updating results for entries with retired/DNS/DNF/DSQ status unless override_status is true
    const invalidStatuses = ['retired', 'dns', 'dnf', 'dsq'];
    if (invalidStatuses.includes(entryStatus) && override_status !== 'true' && override_status !== true) {
      return res.status(400).json({
        message: `Cannot update result for entry with status: ${entryStatus}. Use override_status=true to override.`,
        status: entryStatus
      });
    }

    // Determine if entry is active based on current status
    const isActive = !invalidStatuses.includes(entryStatus);

    const result = await pool.query(
      `UPDATE results SET rally_id=$1, stage_id=$2, entry_id=$3, time=$4, nominal_time=$5, super_rally=$6, is_active=$7, updated_at=NOW() WHERE id=$8 RETURNING *`,
      [rally_id, stage_id, entry_id, time, nominal_time || false, super_rally || false, isActive, id]
    );
    if (result.rows.length === 0) return res.status(404).json({ message: 'Stage result not found' });
    res.json(result.rows[0]);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Failed to update stage result' });
  }
});

// Delete stage result
router.delete('/:id', verifyToken, verifyAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const result = await pool.query('DELETE FROM results WHERE id = $1 RETURNING *', [id]);
    if (result.rows.length === 0) return res.status(404).json({ message: 'Stage result not found' });
    res.json({ message: 'Stage result deleted', stageResult: result.rows[0] });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Failed to delete stage result' });
  }
});

// Import stage results from CSV - ADMIN ROUTE
router.post('/import', verifyToken, verifyAdmin, upload.single('file'), async (req, res) => {
  // Get rally_id, stage_id, and override_status from request body
  const { rally_id, stage_id, override_status } = req.body;
  const client = await pool.connect();
  try {
    if (!req.file) {
      return res.status(400).json({ message: 'No file uploaded' });
    }

    // Parse CSV file
    const fs = await import('fs');
    const csvContent = fs.readFileSync(req.file.path, 'utf8');
    const records = parse(csvContent, {
      columns: true,
      skip_empty_lines: true,
      trim: true
    });

    console.log('Parsed', records.length, 'records from CSV');

    // Results tracking
    const results = [];
    const errors = [];
    const skipped = [];

    // Check if rally_id and stage_id are provided
    if (!rally_id || !stage_id) {
      return res.status(400).json({
        message: 'Missing required parameters: rally_id and stage_id',
        error: 'Both rally_id and stage_id must be provided'
      });
    }

    // Check for required column headers (case-insensitive)
    const requiredColumns = [
      'entry_number',
      'time'
    ];

    // Optional columns for flags
    const optionalColumns = [
      'nominal_time',
      'super_rally',
      'is_active'
    ];

    // Column mapping logic
    const columnMapping = {};

    // Process records if any exist
    if (records.length > 0) {
      // Log the first record for debugging
      console.log('First record sample:', records[0]);

      const firstRecord = records[0];
      const headers = Object.keys(firstRecord);

      // Map required columns
      requiredColumns.forEach(expectedCol => {
        const matchingHeader = headers.find(h => h.toLowerCase() === expectedCol.toLowerCase());
        if (matchingHeader) {
          columnMapping[expectedCol] = matchingHeader;
        }
      });

      // Map optional columns
      optionalColumns.forEach(expectedCol => {
        const matchingHeader = headers.find(h => h.toLowerCase() === expectedCol.toLowerCase());
        if (matchingHeader) {
          columnMapping[expectedCol] = matchingHeader;
        }
      });

      // Check for missing columns
      const missingColumns = requiredColumns.filter(col => !columnMapping[col]);
      if (missingColumns.length > 0) {
        return res.status(400).json({
          message: `Missing required columns: ${missingColumns.join(', ')}`,
          error: 'CSV file must contain all required columns (case-insensitive)'
        });
      }

      // Log the column mapping for debugging
      console.log('Column mapping:', columnMapping);
    }

    // Process each record individually
    for (const row of records) {
      // Use a separate transaction for each row
      const rowClient = await pool.connect();
      try {
        await rowClient.query('BEGIN');

        // Extract values using column mapping
        const entryNumber = row[columnMapping['entry_number']];
        let time = row[columnMapping['time']];

        // Extract optional flag values
        const nominalTime = columnMapping['nominal_time'] ?
          (row[columnMapping['nominal_time']] === 'true' || row[columnMapping['nominal_time']] === '1' || row[columnMapping['nominal_time']] === 'TRUE') : false;
        const superRally = columnMapping['super_rally'] ?
          (row[columnMapping['super_rally']] === 'true' || row[columnMapping['super_rally']] === '1' || row[columnMapping['super_rally']] === 'TRUE') : false;
        const isActive = columnMapping['is_active'] ?
          (row[columnMapping['is_active']] === 'true' || row[columnMapping['is_active']] === '1' || row[columnMapping['is_active']] === 'TRUE') : true;

        // Check for required fields
        if (!entryNumber || !time) {
          await rowClient.query('ROLLBACK');
          skipped.push({
            row: row,
            reason: `Missing required fields. Entry number and time are required.`
          });
          continue;
        }

        // Convert time format if needed (MM:SS.ss to seconds)
        if (time.includes(':')) {
          const [minutes, seconds] = time.split(':');
          // Ensure we have exactly 2 decimal places
          const secondsValue = parseFloat(seconds).toFixed(2);
          time = (parseFloat(minutes) * 60 + parseFloat(secondsValue)).toString();
        }

        // Validate time is a number
        if (isNaN(parseFloat(time))) {
          await rowClient.query('ROLLBACK');
          skipped.push({
            row: row,
            reason: `Invalid time format: "${time}". Must be a number or MM:SS.sss format.`
          });
          continue;
        }

        // Check if rally exists
        const rallyResult = await rowClient.query(
          'SELECT * FROM rallies WHERE id = $1',
          [rally_id]
        );

        if (rallyResult.rows.length === 0) {
          await rowClient.query('ROLLBACK');
          skipped.push({
            row: row,
            reason: `Rally with ID "${rally_id}" not found`
          });
          continue;
        }

        // Check if stage exists
        const stageResult = await rowClient.query(
          'SELECT * FROM stages WHERE id = $1',
          [stage_id]
        );

        if (stageResult.rows.length === 0) {
          await rowClient.query('ROLLBACK');
          skipped.push({
            row: row,
            reason: `Stage with ID "${stage_id}" not found`
          });
          continue;
        }

        // Verify stage belongs to the rally
        if (stageResult.rows[0].rally_id !== rally_id) {
          await rowClient.query('ROLLBACK');
          skipped.push({
            row: row,
            reason: `Stage with ID "${stage_id}" does not belong to rally with ID "${rally_id}"`
          });
          continue;
        }

        // Get entry by rally_id and entry number
        const entryResult = await rowClient.query(
          'SELECT * FROM entries WHERE rally_id = $1 AND number = $2',
          [rally_id, parseInt(entryNumber)]
        );

        if (entryResult.rows.length === 0) {
          await rowClient.query('ROLLBACK');
          skipped.push({
            row: row,
            reason: `Entry number ${entryNumber} not found in rally "${rallyResult.rows[0].name}"`
          });
          continue;
        }

        const entry = entryResult.rows[0];
        const entryId = entry.id;

        // Check entry status before importing result, unless override_status is true
        const invalidStatuses = ['retired', 'dns', 'dnf', 'dsq'];
        if (invalidStatuses.includes(entry.status) && override_status !== 'true' && override_status !== true) {
          await rowClient.query('ROLLBACK');
          skipped.push({
            row: row,
            reason: `Cannot add result for entry #${entryNumber} with status: ${entry.status}. Use override option to import anyway.`
          });
          continue;
        }

        // Check if result already exists
        const existingResult = await rowClient.query(
          'SELECT * FROM results WHERE stage_id = $1 AND entry_id = $2',
          [stage_id, entryId]
        );

        if (existingResult.rows.length > 0) {
          // Use is_active from CSV if provided, otherwise determine from entry status
          const finalIsActive = columnMapping['is_active'] ? isActive : !invalidStatuses.includes(entry.status);

          // Update existing result
          const result = await rowClient.query(
            `UPDATE results SET time = $1, nominal_time = $2, super_rally = $3, is_active = $4, updated_at = NOW()
             WHERE stage_id = $5 AND entry_id = $6 RETURNING *`,
            [parseFloat(time), nominalTime, superRally, finalIsActive, stage_id, entryId]
          );

          results.push({
            ...result.rows[0],
            action: 'updated'
          });
        } else {
          // Use is_active from CSV if provided, otherwise determine from entry status
          const finalIsActive = columnMapping['is_active'] ? isActive : !invalidStatuses.includes(entry.status);

          // Insert new result
          const result = await rowClient.query(
            `INSERT INTO results (rally_id, stage_id, entry_id, time, nominal_time, super_rally, is_active)
             VALUES ($1, $2, $3, $4, $5, $6, $7) RETURNING *`,
            [rally_id, stage_id, entryId, parseFloat(time), nominalTime, superRally, finalIsActive]
          );

          results.push({
            ...result.rows[0],
            action: 'inserted'
          });
        }

        // Commit this row's transaction
        await rowClient.query('COMMIT');
        console.log(`Successfully imported stage result for entry ${entryId} in stage ${stage_id}`);
      } catch (rowError) {
        // Rollback this row's transaction
        await rowClient.query('ROLLBACK');
        console.error('Error importing row:', row, rowError);
        errors.push({
          row: row,
          error: rowError.message
        });
      } finally {
        // Release the client for this row if it hasn't been released yet
        try {
          if (rowClient) {
            rowClient.release();
          }
        } catch (releaseError) {
          console.error('Error releasing client:', releaseError.message);
        }
      }
    }

    // Count reasons for skipping
    const skipReasons = {};
    skipped.forEach(item => {
      if (!skipReasons[item.reason]) {
        skipReasons[item.reason] = 0;
      }
      skipReasons[item.reason]++;
    });

    // Get the first few skipped entries for debugging
    const sampleSkipped = skipped.slice(0, 5).map(item => ({
      reason: item.reason,
      data: item.row
    }));

    // Count actions (inserted vs updated)
    const actionCounts = {
      inserted: results.filter(r => r.action === 'inserted').length,
      updated: results.filter(r => r.action === 'updated').length
    };

    res.json({
      success: true,
      imported: results.length,
      inserted: actionCounts.inserted,
      updated: actionCounts.updated,
      errors: errors.length,
      skipped: skipped.length,
      skipReasons,
      sampleSkipped,
      details: { results, errors, skipped }
    });
  } catch (error) {
    console.error('Import error:', error);
    res.status(500).json({ message: 'Failed to import stage results', error: error.message });
  } finally {
    // Clean up the temporary file
    try {
      const fs = await import('fs');
      if (req.file && req.file.path) {
        fs.unlinkSync(req.file.path);
      }
    } catch (cleanupError) {
      console.error('Error cleaning up temporary file:', cleanupError);
    }

    client.release();
  }
});

export default router;
