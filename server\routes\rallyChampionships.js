import express from 'express';
import pool from '../config/db.js';
import { verifyToken, verifyAdmin } from '../middleware/auth.js';

const router = express.Router();

// Get championships for a specific rally - PUBLIC ROUTE
router.get('/rally/:rallyId', async (req, res) => {
  try {
    const { rallyId } = req.params;
    const result = await pool.query(`
      SELECT c.*, ce.coefficient
      FROM championships c
      JOIN championship_events ce ON c.id = ce.championship_id
      WHERE ce.rally_id = $1
      ORDER BY c.name ASC
    `, [rallyId]);
    res.json(result.rows);
  } catch (error) {
    console.error('Error fetching rally championships:', error);
    res.status(500).json({ message: 'Failed to fetch rally championships' });
  }
});

// Get rallies for a specific championship - PUBLIC ROUTE
router.get('/championship/:championshipId', async (req, res) => {
  try {
    const { championshipId } = req.params;
    const result = await pool.query(`
      SELECT r.*, ce.coefficient
      FROM rallies r
      JOIN championship_events ce ON r.id = ce.rally_id
      WHERE ce.championship_id = $1
      ORDER BY r.start_date ASC
    `, [championshipId]);
    res.json(result.rows);
  } catch (error) {
    console.error('Error fetching championship rallies:', error);
    res.status(500).json({ message: 'Failed to fetch championship rallies' });
  }
});

// Add rally to championship - ADMIN ONLY
router.post('/', verifyToken, verifyAdmin, async (req, res) => {
  try {
    const { rally_id, championship_id, coefficient = 1.0 } = req.body;

    if (!rally_id || !championship_id) {
      return res.status(400).json({ message: 'Rally ID and Championship ID are required' });
    }

    const result = await pool.query(
      `INSERT INTO championship_events (rally_id, championship_id, coefficient)
       VALUES ($1, $2, $3)
       RETURNING *`,
      [rally_id, championship_id, coefficient]
    );

    res.status(201).json(result.rows[0]);
  } catch (error) {
    if (error.code === '23505') { // Unique constraint violation
      return res.status(409).json({ message: 'Rally is already part of this championship' });
    }
    console.error('Error adding rally to championship:', error);
    res.status(500).json({ message: 'Failed to add rally to championship' });
  }
});

// Update rally-championship relationship - ADMIN ONLY
router.put('/:id', verifyToken, verifyAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { coefficient } = req.body;

    const result = await pool.query(
      `UPDATE championship_events
       SET coefficient = $1, updated_at = NOW()
       WHERE id = $2
       RETURNING *`,
      [coefficient, id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ message: 'Championship event not found' });
    }

    res.json(result.rows[0]);
  } catch (error) {
    console.error('Error updating championship event:', error);
    res.status(500).json({ message: 'Failed to update championship event' });
  }
});

// Remove rally from championship - ADMIN ONLY
router.delete('/:id', verifyToken, verifyAdmin, async (req, res) => {
  try {
    const { id } = req.params;

    const result = await pool.query(
      'DELETE FROM championship_events WHERE id = $1 RETURNING *',
      [id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ message: 'Championship event not found' });
    }

    res.json({ message: 'Rally removed from championship', event: result.rows[0] });
  } catch (error) {
    console.error('Error removing rally from championship:', error);
    res.status(500).json({ message: 'Failed to remove rally from championship' });
  }
});

// Bulk update rally championships - ADMIN ONLY
router.put('/rally/:rallyId/championships', verifyToken, verifyAdmin, async (req, res) => {
  const client = await pool.connect();

  try {
    const { rallyId } = req.params;
    const { championships } = req.body; // Array of {championship_id, coefficient}

    await client.query('BEGIN');

    // Remove existing championship associations for this rally
    await client.query('DELETE FROM championship_events WHERE rally_id = $1', [rallyId]);

    // Add new championship associations
    if (championships && championships.length > 0) {
      for (const champ of championships) {
        // Support both 'id' and 'championship_id' field names for flexibility
        const championshipId = champ.championship_id || champ.id;
        await client.query(
          'INSERT INTO championship_events (rally_id, championship_id, coefficient) VALUES ($1, $2, $3)',
          [rallyId, championshipId, champ.coefficient || 1.0]
        );
      }
    }

    await client.query('COMMIT');

    // Fetch and return updated championships for this rally
    const result = await client.query(`
      SELECT c.*, ce.coefficient, ce.id as event_id
      FROM championships c
      JOIN championship_events ce ON c.id = ce.championship_id
      WHERE ce.rally_id = $1
      ORDER BY c.name ASC
    `, [rallyId]);

    res.json(result.rows);
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Error updating rally championships:', error);
    res.status(500).json({ message: 'Failed to update rally championships' });
  } finally {
    client.release();
  }
});

export default router;
