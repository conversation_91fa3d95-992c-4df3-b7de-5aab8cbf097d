import React from 'react';
import { useParams, Link } from 'react-router-dom';
import { useRallyContext } from '../context/RallyContext';
import StandingsTable from '../components/championships/StandingsTable';
import { Trophy, Calendar, Flag, AlertTriangle, ChevronLeft, ChevronRight } from 'lucide-react';
import { getCountryCode } from '../utils/countryUtils.js';

const ChampionshipDetailsPage = () => {
  const { id } = useParams();
  const { championships, drivers, rallies, loading } = useRallyContext();

  const championship = championships.find(c => c.id === id);
  // Filter rallies that belong to this championship based on championships array
  const championshipRallies = rallies.filter(rally =>
    rally.championships && rally.championships.some(champ => champ.id === championship?.id)
  );

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen pt-16">
        <div className="inline-block animate-spin rounded-full h-8 w-8 border-4 border-gray-300 border-t-red-600"></div>
        <p className="ml-2">Loading championship details...</p>
      </div>
    );
  }

  if (!championship) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen pt-16 px-4">
        <div className="text-center">
          <AlertTriangle className="w-12 h-12 text-red-600 mb-4 mx-auto" />
          <h2 className="text-2xl font-bold mb-2">Championship Not Found</h2>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            The championship you're looking for doesn't exist or has been removed.
          </p>
          <Link
            to="/championships"
            className="bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded-lg flex items-center justify-center w-48 mx-auto"
          >
            <ChevronLeft className="w-4 h-4 mr-2" />
            All Championships
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="pt-16 pb-12 bg-gray-50 dark:bg-gray-900">
      {/* Championship Header */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 shadow-sm mb-6">
        <div className="container mx-auto px-4 py-6">
          <Link
            to="/championships"
            className="inline-flex items-center text-gray-600 dark:text-gray-400 hover:text-red-600 dark:hover:text-red-400 mb-4"
          >
            <ChevronLeft className="w-4 h-4 mr-1" />
            Back to Championships
          </Link>

          <div className="flex items-center justify-between">
            <div className="flex items-center">
              {championship.logo ? (
                <img
                  src={championship.logo}
                  alt={championship.name}
                  className="h-16 w-auto mr-4"
                />
              ) : (
                <Trophy className="w-12 h-12 text-red-600 mr-4" />
              )}
              <div>
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                  {championship.name} {championship.year}
                </h1>
                <p className="text-gray-600 dark:text-gray-400 mt-1">
                  Season Championship
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Championship Standings */}
          <div className="lg:col-span-2">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden mb-6">
              <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white flex items-center">
                  <Trophy className="w-5 h-5 text-yellow-500 mr-2" />
                  Championship Standings
                </h2>
              </div>
              {championship.standings ? (
                <StandingsTable
                  standings={championship.standings}
                  drivers={drivers}
                />
              ) : (
                <div className="p-6 text-center text-gray-500 dark:text-gray-400">
                  No standings data available for this championship
                </div>
              )}
            </div>
          </div>

          {/* Rally Calendar */}
          <div className="lg:col-span-1">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white flex items-center">
                  <Calendar className="w-5 h-5 text-blue-500 mr-2" />
                  Rally Calendar
                </h2>
              </div>
              <div className="divide-y divide-gray-200 dark:divide-gray-700">
                {championshipRallies.map(rally => (
                  <Link
                    key={rally.id}
                    to={`/rallies/${rally.id}`}
                    className="block px-6 py-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="w-8 h-6 rounded mr-3 border border-gray-200 dark:border-gray-600 overflow-hidden flex items-center justify-center">
                          <span
                            className={`fi fi-${getCountryCode(rally.country)}`}
                            style={{ width: '100%', height: '100%', objectFit: 'cover' }}
                          ></span>
                        </div>
                        <div>
                          <h3 className="text-gray-900 dark:text-white font-medium">
                            {rally.name}
                          </h3>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            {new Date(rally.start_date).toLocaleDateString('en-GB', {
                              day: 'numeric',
                              month: 'short'
                            })} - {new Date(rally.end_date).toLocaleDateString('en-GB', {
                              day: 'numeric',
                              month: 'short'
                            })}
                          </p>
                        </div>
                      </div>
                      <div>
                        {rally.status === 'upcoming' && (
                          <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded-full text-xs">
                            Upcoming
                          </span>
                        )}
                        {rally.status === 'running' && (
                          <span className="bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 px-2 py-1 rounded-full text-xs">
                            Running
                          </span>
                        )}
                        {rally.status === 'finished' && (
                          <span className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-2 py-1 rounded-full text-xs">
                            Finished
                          </span>
                        )}
                      </div>
                    </div>
                    <div className="mt-2 flex items-center text-sm text-gray-600 dark:text-gray-400">
                      <Flag className="w-4 h-4 mr-1" />
                      <span className="capitalize">{rally.surface}</span>
                      <span className="mx-2">•</span>
                      <span>View stages</span>
                    </div>
                  </Link>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChampionshipDetailsPage;