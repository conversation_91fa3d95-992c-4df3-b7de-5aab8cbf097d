import express from 'express';
import pool from '../config/db.js';
import { verifyToken, verifyAdmin } from '../middleware/auth.js';
import { getCountryCode } from '../utils/countryUtils.js';
import multer from 'multer';
import { parse } from 'csv-parse/sync';

const router = express.Router();

/**
 * Create a new person
 *
 * Request body:
 * {
 *   first_name: string,
 *   last_name: string,
 *   nationality: string,
 *   date_of_birth: string (optional),
 *   photo_url: string (optional),
 *   bio: string (optional)
 * }
 */
router.post('/', verifyToken, verifyAdmin, async (req, res) => {
  try {
    const { first_name, last_name, nationality, date_of_birth, photo_url, bio } = req.body;

    // Insert person
    const result = await pool.query(
      `INSERT INTO persons (first_name, last_name, nationality, date_of_birth, photo_url, bio)
       VALUES ($1, $2, $3, $4, $5, $6) RETURNING *`,
      [first_name, last_name, nationality, date_of_birth, photo_url, bio]
    );

    res.status(201).json(result.rows[0]);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Failed to add person', error: error.message });
  }
});

/**
 * Get all persons - PUBLIC ROUTE
 */
router.get('/', async (req, res) => {
  try {
    const result = await pool.query(`
      SELECT * FROM persons
      ORDER BY last_name, first_name
    `);

    // Add flag code to each person
    const personsWithFlags = result.rows.map(person => ({
      ...person,
      flag_code: getCountryCode(person.nationality)
    }));

    res.json(personsWithFlags);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Failed to fetch persons', error: error.message });
  }
});

/**
 * Get a specific person by ID - PUBLIC ROUTE
 */
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const result = await pool.query(`
      SELECT * FROM persons
      WHERE id = $1
    `, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({ message: 'Person not found' });
    }

    // Add flag code to person
    const person = {
      ...result.rows[0],
      flag_code: getCountryCode(result.rows[0].nationality)
    };

    res.json(person);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Failed to fetch person', error: error.message });
  }
});

/**
 * Update a person's information
 */
router.put('/:id', verifyToken, verifyAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { first_name, last_name, nationality, date_of_birth, photo_url, bio } = req.body;

    // Update person information
    const result = await pool.query(
      `UPDATE persons
       SET first_name=$1, last_name=$2, nationality=$3, date_of_birth=$4, photo_url=$5, bio=$6, updated_at=NOW()
       WHERE id=$7 RETURNING *`,
      [first_name, last_name, nationality, date_of_birth, photo_url, bio, id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ message: 'Person not found' });
    }

    res.json(result.rows[0]);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Failed to update person', error: error.message });
  }
});

/**
 * Delete a person
 */
router.delete('/:id', verifyToken, verifyAdmin, async (req, res) => {
  try {
    const { id } = req.params;

    // Check if person exists
    const checkResult = await pool.query('SELECT * FROM persons WHERE id = $1', [id]);
    if (checkResult.rows.length === 0) {
      return res.status(404).json({ message: 'Person not found' });
    }

    // Delete the person
    const result = await pool.query('DELETE FROM persons WHERE id = $1 RETURNING *', [id]);

    res.json({
      message: 'Person deleted successfully',
      person: result.rows[0]
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Failed to delete person', error: error.message });
  }
});

// Set up multer for file uploads
const storage = multer.memoryStorage();
const upload = multer({ storage: storage });

// Import persons from CSV - ADMIN ROUTE
router.post('/import', verifyToken, verifyAdmin, upload.single('file'), async (req, res) => {
  try {
    console.log('Import request received');

    if (!req.file) {
      console.log('No file uploaded');
      return res.status(400).json({ message: 'No file uploaded' });
    }

    console.log('File received, size:', req.file.size, 'bytes');

    // Parse CSV file
    const csvContent = req.file.buffer.toString('utf8');
    console.log('CSV content sample:', csvContent.substring(0, 200) + '...');

    let records;
    try {
      records = parse(csvContent, {
        columns: true,
        skip_empty_lines: true,
        trim: true
      });
      console.log('Parsed', records.length, 'records from CSV');

      // Check for required column headers (case-insensitive)
      const requiredColumns = ['first_name', 'last_name', 'nationality'];
      const headerCheck = {};

      // Get the actual column headers from the first record
      if (records.length > 0) {
        const firstRecord = records[0];
        const headers = Object.keys(firstRecord).map(h => h.toLowerCase());
        console.log('CSV headers:', headers);

        // Check if all required columns exist (case-insensitive)
        requiredColumns.forEach(col => {
          headerCheck[col] = headers.some(h => h.toLowerCase() === col.toLowerCase());
        });

        const missingColumns = requiredColumns.filter(col => !headerCheck[col]);
        if (missingColumns.length > 0) {
          return res.status(400).json({
            message: `Missing required columns: ${missingColumns.join(', ')}`,
            error: 'CSV file must contain first_name, last_name, and nationality columns (case-insensitive)'
          });
        }
      }

    } catch (parseError) {
      console.error('CSV parsing error:', parseError);
      return res.status(400).json({
        message: 'Failed to parse CSV file',
        error: parseError.message
      });
    }

    // Validate data
    if (!records.length) {
      console.log('CSV file contains no data');
      return res.status(400).json({ message: 'CSV file contains no data' });
    }

    // Insert persons
    const results = [];
    const skipped = [];
    const errors = [];

    // Create a mapping of actual column names to expected column names (case-insensitive)
    const columnMapping = {};
    if (records.length > 0) {
      const firstRecord = records[0];
      const headers = Object.keys(firstRecord);

      // Map each expected column to the actual column name in the CSV
      ['first_name', 'last_name', 'nationality', 'date_of_birth', 'photo_url', 'bio'].forEach(expectedCol => {
        const matchingHeader = headers.find(h => h.toLowerCase() === expectedCol.toLowerCase());
        if (matchingHeader) {
          columnMapping[expectedCol] = matchingHeader;
        }
      });

      console.log('Column mapping:', columnMapping);
    }

    for (const row of records) {
      try {
        // Get values using the column mapping (handles case differences)
        const firstName = columnMapping['first_name'] ? row[columnMapping['first_name']] : row.first_name;
        const lastName = columnMapping['last_name'] ? row[columnMapping['last_name']] : row.last_name;
        const nationality = columnMapping['nationality'] ? row[columnMapping['nationality']] : row.nationality;
        const dateOfBirth = columnMapping['date_of_birth'] ? row[columnMapping['date_of_birth']] : row.date_of_birth;
        const photoUrl = columnMapping['photo_url'] ? row[columnMapping['photo_url']] : row.photo_url;
        const bio = columnMapping['bio'] ? row[columnMapping['bio']] : row.bio;

        // Validate required fields
        if (!firstName || !lastName || !nationality) {
          skipped.push({
            row: row,
            reason: 'Missing required field(s): ' +
              (!firstName ? 'first_name ' : '') +
              (!lastName ? 'last_name ' : '') +
              (!nationality ? 'nationality' : '')
          });
          continue; // Skip invalid rows
        }

        // Insert the person
        const result = await pool.query(
          `INSERT INTO persons (first_name, last_name, nationality, date_of_birth, photo_url, bio)
           VALUES ($1, $2, $3, $4, $5, $6) RETURNING *`,
          [firstName, lastName, nationality, dateOfBirth || null, photoUrl || null, bio || null]
        );

        results.push(result.rows[0]);
      } catch (rowError) {
        console.error('Error importing row:', row, rowError);
        errors.push({
          row: row,
          error: rowError.message
        });
      }
    }

    console.log('Import completed:', results.length, 'imported,', skipped.length, 'skipped,', errors.length, 'errors');

    res.status(201).json({
      message: `Successfully imported ${results.length} persons. Skipped: ${skipped.length}. Errors: ${errors.length}.`,
      persons: results,
      skipped: skipped,
      errors: errors
    });
  } catch (error) {
    console.error('Import error:', error);
    res.status(500).json({ message: 'Failed to import persons', error: error.message });
  }
});

export default router;


