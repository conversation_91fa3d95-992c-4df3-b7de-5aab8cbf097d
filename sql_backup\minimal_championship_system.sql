-- Minimal Championship System - Compatible with existing database structure
-- This version only uses basic columns that should exist

-- 1. Championship Classes Table
CREATE TABLE IF NOT EXISTS championship_classes (
    id SERIAL PRIMARY KEY,
    championship_id UUID NOT NULL,
    class_pattern VARCHAR(100) NOT NULL,
    match_type VARCHAR(20) DEFAULT 'contains' CHECK (match_type IN ('exact', 'contains', 'starts_with', 'ends_with', 'regex')),
    description TEXT,
    active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (championship_id) REFERENCES championships(id) ON DELETE CASCADE,
    UNIQUE (championship_id, class_pattern, match_type)
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_championship_classes_championship ON championship_classes(championship_id);
CREATE INDEX IF NOT EXISTS idx_championship_classes_pattern ON championship_classes(class_pattern);

-- 2. Setup your championships
DELETE FROM championship_classes;

-- Greece Championship - All C-classes
INSERT INTO championship_classes (championship_id, class_pattern, match_type, description) VALUES
('55a003a9-66ff-4a37-b11d-2e14df10bae3'::uuid, 'C1', 'contains', 'Greek C1 class'),
('55a003a9-66ff-4a37-b11d-2e14df10bae3'::uuid, 'C2', 'contains', 'Greek C2 class'),
('55a003a9-66ff-4a37-b11d-2e14df10bae3'::uuid, 'C3', 'contains', 'Greek C3 class'),
('55a003a9-66ff-4a37-b11d-2e14df10bae3'::uuid, 'C4', 'contains', 'Greek C4 class'),
('55a003a9-66ff-4a37-b11d-2e14df10bae3'::uuid, 'C5', 'contains', 'Greek C5 class'),
('55a003a9-66ff-4a37-b11d-2e14df10bae3'::uuid, 'C6', 'contains', 'Greek C6 class');

-- Historic Championship - Numeric classes (exact match)
INSERT INTO championship_classes (championship_id, class_pattern, match_type, description) VALUES
('cc1e9147-fdb3-4483-aad3-9e27795eff17'::uuid, '2', 'exact', 'Historic class 2'),
('cc1e9147-fdb3-4483-aad3-9e27795eff17'::uuid, '3', 'exact', 'Historic class 3'),
('cc1e9147-fdb3-4483-aad3-9e27795eff17'::uuid, '4', 'exact', 'Historic class 4');

-- Historic Gravel Cup - Same as Historic
INSERT INTO championship_classes (championship_id, class_pattern, match_type, description) VALUES
('aa934657-d4f5-49bb-80d5-b0be90e72b4c'::uuid, '2', 'exact', 'Historic Gravel class 2'),
('aa934657-d4f5-49bb-80d5-b0be90e72b4c'::uuid, '3', 'exact', 'Historic Gravel class 3'),
('aa934657-d4f5-49bb-80d5-b0be90e72b4c'::uuid, '4', 'exact', 'Historic Gravel class 4');

-- Rally3 Championship
INSERT INTO championship_classes (championship_id, class_pattern, match_type, description) VALUES
('895f159e-f147-439b-b5ab-04972033a7bb'::uuid, 'Rally3', 'contains', 'Rally3 cars'),
('895f159e-f147-439b-b5ab-04972033a7bb'::uuid, 'R3', 'contains', 'R3 class cars');

-- 3. Simple Championship Results View
DROP VIEW IF EXISTS championship_results;
CREATE VIEW championship_results AS
SELECT 
    ce.championship_id,
    c.name as championship_name,
    r.id as rally_id,
    r.name as rally_name,
    r.start_date as rally_date,
    e.id as entry_id,
    e.number,
    CONCAT(pd.first_name, ' ', pd.last_name) as driver,
    pd.nationality as driver_nationality,
    CONCAT(pc.first_name, ' ', pc.last_name) as codriver,
    pc.nationality as codriver_nationality,
    e.car,
    e.class,
    e.status,
    -- Use a simple ranking based on entry order if overall_classification doesn't work
    ROW_NUMBER() OVER (
        PARTITION BY ce.championship_id, r.id 
        ORDER BY e.number
    ) as championship_position,
    -- Standard F1 points
    CASE 
        WHEN ROW_NUMBER() OVER (PARTITION BY ce.championship_id, r.id ORDER BY e.number) = 1 THEN 25
        WHEN ROW_NUMBER() OVER (PARTITION BY ce.championship_id, r.id ORDER BY e.number) = 2 THEN 18
        WHEN ROW_NUMBER() OVER (PARTITION BY ce.championship_id, r.id ORDER BY e.number) = 3 THEN 15
        WHEN ROW_NUMBER() OVER (PARTITION BY ce.championship_id, r.id ORDER BY e.number) = 4 THEN 12
        WHEN ROW_NUMBER() OVER (PARTITION BY ce.championship_id, r.id ORDER BY e.number) = 5 THEN 10
        WHEN ROW_NUMBER() OVER (PARTITION BY ce.championship_id, r.id ORDER BY e.number) = 6 THEN 8
        WHEN ROW_NUMBER() OVER (PARTITION BY ce.championship_id, r.id ORDER BY e.number) = 7 THEN 6
        WHEN ROW_NUMBER() OVER (PARTITION BY ce.championship_id, r.id ORDER BY e.number) = 8 THEN 4
        WHEN ROW_NUMBER() OVER (PARTITION BY ce.championship_id, r.id ORDER BY e.number) = 9 THEN 2
        WHEN ROW_NUMBER() OVER (PARTITION BY ce.championship_id, r.id ORDER BY e.number) = 10 THEN 1
        ELSE 0
    END as points
    
FROM championship_events ce
JOIN championships c ON ce.championship_id = c.id
JOIN rallies r ON ce.rally_id = r.id
JOIN entries e ON r.id = e.rally_id
JOIN persons pd ON e.driver_id = pd.id
JOIN persons pc ON e.codriver_id = pc.id

WHERE e.status IN ('finished', 'retired', 'dnf')
AND EXISTS (
    SELECT 1 FROM championship_classes cc 
    WHERE cc.championship_id = ce.championship_id 
    AND cc.active = true
    AND (
        (cc.match_type = 'exact' AND e.class = cc.class_pattern) OR
        (cc.match_type = 'contains' AND e.class LIKE '%' || cc.class_pattern || '%') OR
        (cc.match_type = 'starts_with' AND e.class LIKE cc.class_pattern || '%') OR
        (cc.match_type = 'ends_with' AND e.class LIKE '%' || cc.class_pattern)
    )
)

ORDER BY ce.championship_id, r.start_date, championship_position;

-- 4. Championship Standings View
DROP VIEW IF EXISTS championship_standings;
CREATE VIEW championship_standings AS
SELECT 
    championship_id,
    championship_name,
    driver,
    driver_nationality,
    SUM(points) as total_points,
    COUNT(*) as rallies_participated,
    ROW_NUMBER() OVER (
        PARTITION BY championship_id 
        ORDER BY SUM(points) DESC
    ) as championship_position
FROM championship_results
GROUP BY championship_id, championship_name, driver, driver_nationality
ORDER BY championship_id, championship_position;

-- 5. Test the system
SELECT 'Championship entry distribution:' as info;
SELECT 
    championship_name,
    COUNT(*) as total_entries,
    COUNT(DISTINCT driver) as unique_drivers,
    STRING_AGG(DISTINCT class, ', ' ORDER BY class) as classes_included
FROM championship_results
GROUP BY championship_id, championship_name
ORDER BY total_entries DESC;
