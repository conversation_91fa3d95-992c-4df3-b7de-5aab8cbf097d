import express from 'express';
import pool from '../config/db.js';
import { verifyToken, verifyAdmin } from '../middleware/auth.js';

const router = express.Router();

// Create driver (person + driver)
router.post('/', verifyToken, verifyAdmin, async (req, res) => {
  try {
    // Check if we're adding an existing person as a driver
    if (req.body.id) {
      const personId = req.body.id;

      // Check if the person exists
      const personCheck = await pool.query('SELECT * FROM persons WHERE id = $1', [personId]);
      if (personCheck.rows.length === 0) {
        return res.status(404).json({ message: 'Person not found' });
      }

      // Check if the person is already a driver
      const driverCheck = await pool.query('SELECT * FROM drivers WHERE id = $1', [personId]);
      if (driverCheck.rows.length > 0) {
        return res.status(400).json({ message: 'Person is already a driver' });
      }

      // Add the person as a driver
      await pool.query('INSERT INTO drivers (id) VALUES ($1)', [personId]);

      // Return the driver with person details
      const driver = await pool.query(`
        SELECT d.id, p.first_name, p.last_name, p.nationality, p.date_of_birth, p.photo_url, p.bio, p.created_at, p.updated_at
        FROM drivers d
        JOIN persons p ON d.id = p.id
        WHERE d.id = $1
      `, [personId]);

      return res.status(201).json(driver.rows[0]);
    }

    // Original code for creating a new person and driver
    const { first_name, last_name, nationality, date_of_birth, photo_url, bio } = req.body;
    const personResult = await pool.query(
      `INSERT INTO persons (first_name, last_name, nationality, date_of_birth, photo_url, bio) VALUES ($1, $2, $3, $4, $5, $6) RETURNING id`,
      [first_name, last_name, nationality, date_of_birth, photo_url, bio]
    );
    const personId = personResult.rows[0].id;
    await pool.query(`INSERT INTO drivers (id) VALUES ($1)`, [personId]);
    const driver = await pool.query(`
      SELECT d.id, p.first_name, p.last_name, p.nationality, p.date_of_birth, p.photo_url, p.bio, p.created_at, p.updated_at
      FROM drivers d
      JOIN persons p ON d.id = p.id
      WHERE d.id = $1
    `, [personId]);
    res.status(201).json(driver.rows[0]);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Failed to add driver' });
  }
});

// Get all drivers (with person details) - PUBLIC ROUTE
router.get('/', async (req, res) => {
  try {
    const result = await pool.query(`
      SELECT d.id, p.first_name, p.last_name, p.nationality, p.date_of_birth, p.photo_url, p.bio, p.created_at, p.updated_at
      FROM drivers d
      JOIN persons p ON d.id = p.id
      ORDER BY p.last_name, p.first_name
    `);
    res.json(result.rows);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Failed to fetch drivers' });
  }
});

// Get single driver (with person details) - PUBLIC ROUTE
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const result = await pool.query(`
      SELECT d.id, p.first_name, p.last_name, p.nationality, p.date_of_birth, p.photo_url, p.bio, p.created_at, p.updated_at
      FROM drivers d
      JOIN persons p ON d.id = p.id
      WHERE d.id = $1
    `, [id]);
    if (result.rows.length === 0) return res.status(404).json({ message: 'Driver not found' });
    res.json(result.rows[0]);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Failed to fetch driver' });
  }
});

// Update driver (person)
router.put('/:id', verifyToken, verifyAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { first_name, last_name, nationality, date_of_birth, photo_url, bio } = req.body;
    const result = await pool.query(
      `UPDATE persons SET first_name=$1, last_name=$2, nationality=$3, date_of_birth=$4, photo_url=$5, bio=$6, updated_at=NOW() WHERE id=$7 RETURNING *`,
      [first_name, last_name, nationality, date_of_birth, photo_url, bio, id]
    );
    if (result.rows.length === 0) return res.status(404).json({ message: 'Driver not found' });
    res.json(result.rows[0]);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Failed to update driver' });
  }
});

// Delete driver (person + driver)
router.delete('/:id', verifyToken, verifyAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    // Deleting from persons will cascade to drivers due to FK
    const result = await pool.query('DELETE FROM persons WHERE id = $1 RETURNING *', [id]);
    if (result.rows.length === 0) return res.status(404).json({ message: 'Driver not found' });
    res.json({ message: 'Driver deleted', driver: result.rows[0] });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Failed to delete driver' });
  }
});

export default router;
