import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Upload, FileUp } from 'lucide-react';
import { useAuth } from '../../../../context/AuthContext';

const ImportPersonsPage: React.FC = () => {
  const [file, setFile] = useState<File | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const navigate = useNavigate();
  const { user, isAdmin } = useAuth();

  // Check if user is authenticated and has admin rights
  useEffect(() => {
    if (user && isAdmin) {
      setIsAuthenticated(true);
    } else if (user && !isAdmin) {
      setError('You do not have permission to import persons. Admin rights required.');
    }
  }, [user, isAdmin]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setFile(e.target.files[0]);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Check authentication before submitting
    if (!user || !isAdmin) {
      setError('Authentication required. Please log in as an admin.');
      return;
    }

    if (!file) {
      setError('Please select a file');
      return;
    }

    setLoading(true);
    setError('');
    setSuccess('');

    const formData = new FormData();
    formData.append('file', file);

    try {
      console.log('Sending import request...');

      const response = await fetch('/api/persons/import', {
        method: 'POST',
        credentials: 'include',
        body: formData,
      });

      console.log('Response status:', response.status);

      // Try to parse the response as JSON
      let data;
      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        data = await response.json();
        console.log('Response data:', data);
      } else {
        const text = await response.text();
        console.log('Response text:', text);
        data = { message: text };
      }

      if (!response.ok) {
        throw new Error(data.message || 'Import failed');
      }

      setSuccess(`Successfully imported ${data.persons ? data.persons.length : 0} persons`);
      setTimeout(() => navigate('/admin/persons'), 2000);
    } catch (err: any) {
      console.error('Import error:', err);
      setError(err.message || 'Failed to import persons');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Import Persons</h1>
      </div>

      <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        {success && (
          <div className="mb-4 p-4 bg-green-100 text-green-800 rounded">
            {success}
          </div>
        )}

        {error && (
          <div className="mb-4 p-4 bg-red-100 text-red-800 rounded">
            {error}
          </div>
        )}

        {!user && (
          <div className="mb-4 p-4 bg-yellow-100 text-yellow-800 rounded">
            Please log in to import persons.
          </div>
        )}

        {user && !isAdmin && (
          <div className="mb-4 p-4 bg-yellow-100 text-yellow-800 rounded">
            You need admin privileges to import persons.
          </div>
        )}

        {user && isAdmin && (
          <form onSubmit={handleSubmit}>
            <div className="mb-6">
              <label className="block text-gray-700 dark:text-gray-300 mb-2">
                CSV File
              </label>
              <div className="flex items-center justify-center w-full">
                <label className="flex flex-col w-full h-32 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700">
                  <div className="flex flex-col items-center justify-center pt-5 pb-6">
                    <FileUp className="w-8 h-8 mb-2 text-gray-500 dark:text-gray-400" />
                    <p className="mb-2 text-sm text-gray-500 dark:text-gray-400">
                      <span className="font-semibold">Click to upload</span> or drag and drop
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      CSV file with columns: first_name, last_name, nationality, date_of_birth (optional), photo_url (optional), bio (optional)
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      <strong>Note:</strong> For nationality, use full country names like "Greece", "France", "United States"
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      <strong>Column order doesn't matter</strong> - the system will automatically detect columns by their names
                    </p>
                  </div>
                  <input
                    type="file"
                    className="hidden"
                    accept=".csv"
                    onChange={handleFileChange}
                  />
                </label>
              </div>
              {file && (
                <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                  Selected file: {file.name}
                </p>
              )}
            </div>

            <div className="flex justify-end space-x-4">
              <button
                type="button"
                onClick={() => navigate('/admin/persons')}
                className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
                disabled={loading}
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 flex items-center"
                disabled={loading}
              >
                {loading ? (
                  <>
                    <span className="animate-spin mr-2">⟳</span>
                    Importing...
                  </>
                ) : (
                  <>
                    <Upload className="w-4 h-4 mr-2" />
                    Import Persons
                  </>
                )}
              </button>
            </div>
          </form>
        )}
      </div>
    </div>
  );
};

export default ImportPersonsPage;
