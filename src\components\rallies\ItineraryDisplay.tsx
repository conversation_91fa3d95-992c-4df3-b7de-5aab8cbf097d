import React, { useState, useEffect } from 'react';
import { Calendar, Clock, MapPin, Flag, Timer, Wrench, Fuel, Award, Car } from 'lucide-react';

interface ItineraryItem {
  id: string;
  rally_id: string;
  type: string;
  name: string;
  location: string;
  start_time: string;
  duration: number | null;
  leg_number: number;
  day_number: number;
  order_in_day: number;
  related_id: string | null;
  additional_info: any;
}

interface ItinerarySummary {
  legs: { leg_number: number; day_number: number }[];
  stageTotals: { leg_number: number; stage_count: number; stage_distance: number }[];
  overallTotals: { total_stages: number; total_stage_distance: number };
}

interface ItineraryDisplayProps {
  rallyId: string;
}

export const ItineraryDisplay: React.FC<ItineraryDisplayProps> = ({ rallyId }) => {
  const [itineraryItems, setItineraryItems] = useState<ItineraryItem[]>([]);
  const [summary, setSummary] = useState<ItinerarySummary | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [activeDay, setActiveDay] = useState<number | null>(null);

  useEffect(() => {
    fetchItinerary();
  }, [rallyId]);

  const fetchItinerary = async () => {
    setLoading(true);
    try {
      const [itemsRes, summaryRes] = await Promise.all([
        fetch(`/api/itinerary/rally/${rallyId}`),
        fetch(`/api/itinerary/rally/${rallyId}/summary`)
      ]);

      if (!itemsRes.ok || !summaryRes.ok) {
        throw new Error('Failed to fetch itinerary data');
      }

      const [items, summaryData] = await Promise.all([
        itemsRes.json(),
        summaryRes.json()
      ]);

      setItineraryItems(items);
      setSummary(summaryData);

      // Set active day to the first day by default
      if (items.length > 0) {
        setActiveDay(items[0].day_number);
      }
    } catch (err: any) {
      console.error('Error fetching itinerary:', err);
      setError(err.message || 'Failed to load itinerary');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-GB', {
      weekday: 'long',
      day: 'numeric',
      month: 'long'
    });
  };

  const formatTime = (timeString: string) => {
    return new Date(timeString).toLocaleTimeString('en-GB', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getItemIcon = (type: string) => {
    switch (type) {
      case 'stage':
        return <Timer className="w-5 h-5 text-red-600" />;
      case 'service':
        return <Wrench className="w-5 h-5 text-blue-600" />;
      case 'regroup':
        return <Clock className="w-5 h-5 text-green-600" />;
      case 'refuel':
        return <Fuel className="w-5 h-5 text-yellow-600" />;
      case 'start':
        return <Flag className="w-5 h-5 text-purple-600" />;
      case 'finish':
        return <Flag className="w-5 h-5 text-red-600" />;
      case 'podium':
        return <Award className="w-5 h-5 text-yellow-600" />;
      case 'parc_ferme':
        return <Car className="w-5 h-5 text-gray-600" />;
      case 'shakedown':
        return <Timer className="w-5 h-5 text-orange-600" />;
      default:
        return <Clock className="w-5 h-5 text-gray-600" />;
    }
  };

  const getItemTypeLabel = (type: string) => {
    switch (type) {
      case 'stage':
        return 'Stage';
      case 'service':
        return 'Service';
      case 'regroup':
        return 'Regroup';
      case 'refuel':
        return 'Refuel';
      case 'start':
        return 'Start';
      case 'finish':
        return 'Finish';
      case 'podium':
        return 'Podium';
      case 'parc_ferme':
        return 'Parc Fermé';
      case 'shakedown':
        return 'Shakedown';
      case 'tyre_fitting':
        return 'Tyre Fitting';
      case 'remote_service':
        return 'Remote Service';
      default:
        return type.charAt(0).toUpperCase() + type.slice(1).replace('_', ' ');
    }
  };

  const getDayItems = (dayNumber: number) => {
    return itineraryItems.filter(item => item.day_number === dayNumber);
  };

  const getUniqueDays = () => {
    const days = new Set<number>();
    itineraryItems.forEach(item => days.add(item.day_number));
    return Array.from(days).sort((a, b) => a - b);
  };

  const getDayDate = (dayNumber: number) => {
    const dayItem = itineraryItems.find(item => item.day_number === dayNumber);
    return dayItem ? formatDate(dayItem.start_time) : `Day ${dayNumber}`;
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-200 p-4 rounded-lg">
        <p>{error}</p>
      </div>
    );
  }

  if (itineraryItems.length === 0) {
    return (
      <div className="bg-gray-100 dark:bg-gray-700 p-6 rounded-lg text-center">
        <p className="text-gray-600 dark:text-gray-300">No itinerary information available for this rally.</p>
      </div>
    );
  }

  const days = getUniqueDays();

  return (
    <div className="space-y-6">
      {/* Summary */}
      {summary && (
        <div className="bg-gray-100 dark:bg-gray-700 rounded-lg p-4">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">Rally Overview</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-white dark:bg-gray-800 p-3 rounded-lg shadow-sm">
              <div className="text-sm text-gray-500 dark:text-gray-400">Total Stages</div>
              <div className="text-xl font-bold text-gray-900 dark:text-white">{summary.overallTotals.total_stages}</div>
            </div>
            <div className="bg-white dark:bg-gray-800 p-3 rounded-lg shadow-sm">
              <div className="text-sm text-gray-500 dark:text-gray-400">Total Distance</div>
              <div className="text-xl font-bold text-gray-900 dark:text-white">
                {summary.overallTotals.total_stage_distance
                  ? Number(summary.overallTotals.total_stage_distance).toFixed(2)
                  : '0.00'} km
              </div>
            </div>
            <div className="bg-white dark:bg-gray-800 p-3 rounded-lg shadow-sm">
              <div className="text-sm text-gray-500 dark:text-gray-400">Legs</div>
              <div className="text-xl font-bold text-gray-900 dark:text-white">{summary.stageTotals.length}</div>
            </div>
          </div>
        </div>
      )}

      {/* Day Tabs */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="flex space-x-2 overflow-x-auto pb-1">
          {days.map(day => (
            <button
              key={day}
              onClick={() => setActiveDay(day)}
              className={`py-2 px-4 text-sm font-medium rounded-t-lg ${
                activeDay === day
                  ? 'bg-white dark:bg-gray-800 text-red-600 dark:text-red-500 border-b-2 border-red-600 dark:border-red-500'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
              }`}
            >
              <Calendar className="w-4 h-4 inline mr-1" />
              {getDayDate(day)}
            </button>
          ))}
        </nav>
      </div>

      {/* Day Content */}
      {activeDay !== null && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Time</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Type</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Name</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Location</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Details</th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {getDayItems(activeDay).map(item => (
                  <tr key={item.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      {formatTime(item.start_time)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        {getItemIcon(item.type)}
                        <span className="ml-2 text-sm text-gray-600 dark:text-gray-400">
                          {getItemTypeLabel(item.type)}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                      {item.name}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400">
                      {item.location && (
                        <div className="flex items-center">
                          <MapPin className="w-4 h-4 mr-1 text-gray-400" />
                          {item.location}
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400">
                      {item.type === 'stage' && item.additional_info && (
                        <span>
                          {(() => {
                            try {
                              if (typeof item.additional_info === 'string') {
                                return JSON.parse(item.additional_info).length;
                              } else {
                                return item.additional_info.length;
                              }
                            } catch (e) {
                              return '?';
                            }
                          })()} km
                        </span>
                      )}
                      {item.type === 'service' && item.duration && (
                        <span>{item.duration} min</span>
                      )}
                      {item.type === 'regroup' && item.duration && (
                        <span>{item.duration} min</span>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
};

export default ItineraryDisplay;
