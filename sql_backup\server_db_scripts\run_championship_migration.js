import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import pool from '../config/db.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function runChampionshipMigration() {
  const client = await pool.connect();
  
  try {
    console.log('🚀 Starting championship views migration...');
    
    // Read the migration SQL file
    const migrationPath = path.join(__dirname, 'migrations', 'add_championship_views.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    // Execute the migration
    await client.query('BEGIN');
    
    console.log('📝 Executing championship views migration...');
    await client.query(migrationSQL);
    
    await client.query('COMMIT');
    
    console.log('✅ Championship views migration completed successfully!');
    
    // Test the new views
    console.log('\n🔍 Testing new views...');
    
    const testQueries = [
      {
        name: 'championship_overall_classification',
        query: 'SELECT COUNT(*) as count FROM championship_overall_classification'
      },
      {
        name: 'championship_points',
        query: 'SELECT COUNT(*) as count FROM championship_points'
      },
      {
        name: 'championship_standings',
        query: 'SELECT COUNT(*) as count FROM championship_standings'
      },
      {
        name: 'championship_class_standings',
        query: 'SELECT COUNT(*) as count FROM championship_class_standings'
      }
    ];
    
    for (const test of testQueries) {
      try {
        const result = await client.query(test.query);
        console.log(`✓ ${test.name}: ${result.rows[0].count} rows`);
      } catch (error) {
        console.log(`✗ ${test.name}: Error - ${error.message}`);
      }
    }
    
    // Show sample data if available
    console.log('\n📊 Sample championship data:');
    try {
      const sampleResult = await client.query(`
        SELECT 
          championship_name,
          COUNT(DISTINCT rally_id) as rallies,
          COUNT(DISTINCT driver_id) as drivers
        FROM championship_points 
        GROUP BY championship_id, championship_name
        ORDER BY championship_name
        LIMIT 5
      `);
      
      if (sampleResult.rows.length > 0) {
        console.log('Championships with data:');
        sampleResult.rows.forEach(row => {
          console.log(`  - ${row.championship_name}: ${row.rallies} rallies, ${row.drivers} drivers`);
        });
      } else {
        console.log('  No championship data found. Add rallies to championships to see results.');
      }
    } catch (error) {
      console.log(`  Error fetching sample data: ${error.message}`);
    }
    
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('❌ Migration failed:', error);
    throw error;
  } finally {
    client.release();
  }
}

// Run the migration if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runChampionshipMigration()
    .then(() => {
      console.log('\n🎉 Migration completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Migration failed:', error);
      process.exit(1);
    });
}

export default runChampionshipMigration;
