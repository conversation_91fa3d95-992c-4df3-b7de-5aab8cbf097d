import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Clock } from 'lucide-react';
import StageResultForm from '../../../../components/admin/stage-result-form';

const AddStageResultPage: React.FC = () => {
  const [error, setError] = useState('');
  const navigate = useNavigate();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const rallyId = queryParams.get('rally_id');
  const stageId = queryParams.get('stage_id');

  const handleSubmit = async (formData: any) => {
    console.log('Submitting stage result with data:', formData);
    
    const response = await fetch('/api/stageResults', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      credentials: 'include',
      body: JSON.stringify(formData)
    });

    // Log the response for debugging
    console.log('Response status:', response.status);
    
    if (!response.ok) {
      const data = await response.json();
      console.error('Error response:', data);
      throw new Error(data.message || data.error || 'Failed to add stage result');
    }

    // If we came from a rally page, go back there
    if (rallyId) {
      navigate(`/admin/rallies/${rallyId}`);
    } else if (stageId) {
      // If we have a stage ID but no rally ID, try to find the rally ID
      const stageResponse = await fetch(`/api/stages/${stageId}`, { credentials: 'include' });
      if (stageResponse.ok) {
        const stageData = await stageResponse.json();
        navigate(`/admin/rallies/${stageData.rally_id}`);
      } else {
        navigate('/admin/stageResults');
      }
    } else {
      navigate('/admin/stageResults');
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center mb-6">
        <Clock className="w-6 h-6 text-red-600 mr-2" />
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Add Stage Result</h1>
      </div>

      {error && (
        <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          {error}
        </div>
      )}

      <StageResultForm 
        onSubmit={handleSubmit} 
        preselectedRallyId={rallyId || undefined}
        preselectedStageId={stageId || undefined}
      />
    </div>
  );
};

export default AddStageResultPage;
