import pool from './server/config/db.js';
import fs from 'fs';

async function processAllChampionshipAssociations() {
  const client = await pool.connect();
  
  try {
    console.log('=== Processing All Championship Associations ===');
    
    // Read JSON file
    const jsonContent = fs.readFileSync('./ewrc_complete_results_94918-rally-stereas-elladas-2025.json', 'utf8');
    const ewrcData = JSON.parse(jsonContent);
    
    console.log('Loaded EWRC data with', ewrcData.results.length, 'results');
    
    // Get rally ID
    const rally = await client.query(
      "SELECT id FROM rallies WHERE name LIKE '%Stereas Elladas 2025%'",
    );
    
    if (rally.rows.length === 0) {
      console.error('Rally not found');
      process.exit(1);
    }
    
    const rallyId = rally.rows[0].id;
    console.log('Rally ID:', rallyId);
    
    // Collect all unique entries and their championships
    const entryChampionshipMap = new Map();
    
    for (const result of ewrcData.results) {
      const entryNumber = parseInt(result.entry_number.replace('#', ''));
      
      // Store championship associations for this entry (from first occurrence)
      if (result.championship && Array.isArray(result.championship) && !entryChampionshipMap.has(entryNumber)) {
        entryChampionshipMap.set(entryNumber, result.championship);
      }
    }
    
    console.log(`Found ${entryChampionshipMap.size} unique entries with championship data`);
    
    // Clear all existing entry-championship associations for this rally
    await client.query(`
      DELETE FROM entry_championships 
      WHERE entry_id IN (
        SELECT id FROM entries WHERE rally_id = $1
      )
    `, [rallyId]);
    
    console.log('Cleared existing entry-championship associations');
    
    let processedEntries = 0;
    let totalAssociations = 0;
    
    // Process each entry
    for (const [entryNumber, championshipNames] of entryChampionshipMap.entries()) {
      // Get entry from database
      const entryQuery = await client.query(
        'SELECT id FROM entries WHERE rally_id = $1 AND number = $2',
        [rallyId, entryNumber]
      );
      
      if (entryQuery.rows.length === 0) {
        console.log(`⚠ Entry #${entryNumber} not found in database`);
        continue;
      }
      
      const entryId = entryQuery.rows[0].id;
      
      // Process championship associations
      if (championshipNames && Array.isArray(championshipNames)) {
        for (const championshipName of championshipNames) {
          try {
            // Find championship using the same logic as in the import
            let existingChampionship = await client.query(
              'SELECT id, name FROM championships WHERE LOWER(name) = LOWER($1)',
              [championshipName.trim()]
            );

            // If not found, try without (GR) suffix
            if (existingChampionship.rows.length === 0 && championshipName.includes('(GR)')) {
              const nameWithoutGR = championshipName.replace(/\s*\(GR\)\s*/gi, '').trim();
              existingChampionship = await client.query(
                'SELECT id, name FROM championships WHERE LOWER(name) = LOWER($1)',
                [nameWithoutGR]
              );
            }

            // If still not found, try partial match
            if (existingChampionship.rows.length === 0) {
              const mainWord = championshipName.replace(/\s*\(GR\)\s*/gi, '').split(' ')[0].trim();
              if (mainWord.length > 2) {
                existingChampionship = await client.query(
                  'SELECT id, name FROM championships WHERE LOWER(name) LIKE LOWER($1)',
                  [`%${mainWord}%`]
                );
              }
            }

            if (existingChampionship.rows.length > 0) {
              const championshipId = existingChampionship.rows[0].id;

              // Insert entry-championship association
              await client.query(
                'INSERT INTO entry_championships (entry_id, championship_id) VALUES ($1, $2) ON CONFLICT (entry_id, championship_id) DO NOTHING',
                [entryId, championshipId]
              );

              totalAssociations++;
            }
          } catch (error) {
            console.error(`✗ Error associating entry #${entryNumber} with championship ${championshipName}:`, error.message);
          }
        }
      }
      
      processedEntries++;
      if (processedEntries % 10 === 0) {
        console.log(`Processed ${processedEntries}/${entryChampionshipMap.size} entries...`);
      }
    }
    
    console.log(`\n✅ Processing complete!`);
    console.log(`  Processed entries: ${processedEntries}`);
    console.log(`  Total associations created: ${totalAssociations}`);
    
    // Show summary
    const summary = await client.query(`
      SELECT 
        c.name as championship,
        COUNT(*) as entries
      FROM entry_championships ec
      JOIN entries e ON ec.entry_id = e.id
      JOIN championships c ON ec.championship_id = c.id
      WHERE e.rally_id = $1
      GROUP BY c.name
      ORDER BY COUNT(*) DESC
    `, [rallyId]);
    
    console.log('\nChampionship Summary:');
    summary.rows.forEach(row => {
      console.log(`  ${row.championship}: ${row.entries} entries`);
    });
    
    process.exit(0);
  } catch (error) {
    console.error('Error processing championship associations:', error);
    process.exit(1);
  } finally {
    client.release();
  }
}

processAllChampionshipAssociations();
