import React, { useState, useEffect } from 'react';
import { useRallyContext } from '../context/RallyContext';
import RallyCard from '../components/rallies/RallyCard';
import { Filter, Search, Calendar, Flag, X } from 'lucide-react';
import { Rally, Surface } from '../types';

const RalliesPage: React.FC = () => {
  const { rallies, loading, error } = useRallyContext();
  const [filteredRallies, setFilteredRallies] = useState<Rally[]>([]);
  const [selectedYear, setSelectedYear] = useState<number | null>(null);
  const [selectedSurface, setSelectedSurface] = useState<Surface | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);

  const years = Array.from(new Set(rallies.map(rally => new Date(rally.start_date).getFullYear()))).sort((a, b) => b - a);
  const surfaces: Surface[] = ['gravel', 'tarmac', 'snow', 'mixed'];

  useEffect(() => {
    let result = [...rallies];

    // Apply year filter
    if (selectedYear) {
      result = result.filter(rally => new Date(rally.start_date).getFullYear() === selectedYear);
    }

    // Apply surface filter
    if (selectedSurface) {
      result = result.filter(rally => rally.surface === selectedSurface);
    }

    // Apply search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      result = result.filter(
        rally =>
          rally.name.toLowerCase().includes(query) ||
          rally.country.toLowerCase().includes(query)
      );
    }

    setFilteredRallies(result);
  }, [rallies, selectedYear, selectedSurface, searchQuery]);

  const clearFilters = () => {
    setSelectedYear(null);
    setSelectedSurface(null);
    setSearchQuery('');
  };

  const toggleFilters = () => {
    setShowFilters(!showFilters);
  };

  return (
    <div className="pt-16 pb-12 min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="bg-gray-800 text-white py-12">
        <div className="container mx-auto px-4">
          <h1 className="text-3xl font-bold mb-4">Rallies</h1>
          <p className="text-gray-300 max-w-2xl">
            Browse and discover rally events from around the world. Filter by year, surface, or search for specific rallies.
          </p>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        {/* Search and Filters */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 mb-8">
          <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
            <div className="relative flex-grow max-w-md">
              <Search className="absolute top-2.5 left-3 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search rallies..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 w-full border border-gray-300 dark:border-gray-700 rounded-lg py-2 px-4 focus:outline-none focus:ring-2 focus:ring-red-500 dark:bg-gray-700 dark:text-white"
              />
              {searchQuery && (
                <button
                  onClick={() => setSearchQuery('')}
                  className="absolute right-2 top-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200"
                >
                  <X className="h-5 w-5" />
                </button>
              )}
            </div>

            <div className="flex items-center space-x-2">
              <button
                onClick={toggleFilters}
                className="flex items-center space-x-1 bg-gray-100 dark:bg-gray-700 px-3 py-2 rounded-lg text-gray-700 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
              >
                <Filter className="h-4 w-4" />
                <span>Filters</span>
              </button>

              {(selectedYear || selectedSurface) && (
                <button
                  onClick={clearFilters}
                  className="flex items-center space-x-1 bg-red-100 dark:bg-red-900 px-3 py-2 rounded-lg text-red-700 dark:text-red-200 hover:bg-red-200 dark:hover:bg-red-800 transition-colors"
                >
                  <X className="h-4 w-4" />
                  <span>Clear</span>
                </button>
              )}
            </div>
          </div>

          {/* Expandable Filter Section */}
          {showFilters && (
            <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center">
                    <Calendar className="h-4 w-4 mr-1" />
                    Filter by Year
                  </label>
                  <div className="flex flex-wrap gap-2">
                    {years.map(year => (
                      <button
                        key={year}
                        onClick={() => setSelectedYear(selectedYear === year ? null : year)}
                        className={`px-3 py-1 rounded-full text-sm ${
                          selectedYear === year
                            ? 'bg-red-600 text-white'
                            : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                        }`}
                      >
                        {year}
                      </button>
                    ))}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center">
                    <Flag className="h-4 w-4 mr-1" />
                    Filter by Surface
                  </label>
                  <div className="flex flex-wrap gap-2">
                    {surfaces.map(surface => (
                      <button
                        key={surface}
                        onClick={() => setSelectedSurface(selectedSurface === surface ? null : surface)}
                        className={`px-3 py-1 rounded-full text-sm ${
                          selectedSurface === surface
                            ? 'bg-red-600 text-white'
                            : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                        }`}
                      >
                        {surface.charAt(0).toUpperCase() + surface.slice(1)}
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Active Filters Display */}
        {(selectedYear || selectedSurface || searchQuery) && (
          <div className="flex items-center mb-4 text-sm">
            <span className="text-gray-600 dark:text-gray-400 mr-2">Active filters:</span>
            <div className="flex flex-wrap gap-2">
              {selectedYear && (
                <span className="bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 px-2 py-1 rounded-full flex items-center">
                  {selectedYear}
                  <button onClick={() => setSelectedYear(null)} className="ml-1 text-red-600 dark:text-red-300">
                    <X className="h-3 w-3" />
                  </button>
                </span>
              )}
              {selectedSurface && (
                <span className="bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 px-2 py-1 rounded-full flex items-center">
                  {selectedSurface.charAt(0).toUpperCase() + selectedSurface.slice(1)}
                  <button onClick={() => setSelectedSurface(null)} className="ml-1 text-red-600 dark:text-red-300">
                    <X className="h-3 w-3" />
                  </button>
                </span>
              )}
              {searchQuery && (
                <span className="bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 px-2 py-1 rounded-full flex items-center">
                  "{searchQuery}"
                  <button onClick={() => setSearchQuery('')} className="ml-1 text-red-600 dark:text-red-300">
                    <X className="h-3 w-3" />
                  </button>
                </span>
              )}
            </div>
          </div>
        )}

        {/* Results Count */}
        <div className="mb-6">
          <p className="text-gray-600 dark:text-gray-400">
            Showing {filteredRallies.length} {filteredRallies.length === 1 ? 'rally' : 'rallies'}
          </p>
        </div>

        {/* Rallies Grid */}
        {loading ? (
          <div className="text-center py-12">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-4 border-gray-300 border-t-red-600"></div>
            <p className="mt-2 text-gray-700 dark:text-gray-300">Loading rallies...</p>
          </div>
        ) : error ? (
          <div className="text-center py-12 text-red-600">
            {error}
          </div>
        ) : filteredRallies.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-700 dark:text-gray-300">No rallies found matching your criteria.</p>
            <button
              onClick={clearFilters}
              className="mt-4 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
            >
              Clear Filters
            </button>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredRallies.map(rally => (
              <RallyCard key={rally.id} rally={rally} />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default RalliesPage;