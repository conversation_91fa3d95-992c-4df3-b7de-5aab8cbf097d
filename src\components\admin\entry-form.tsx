import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

interface Rally {
  id: string;
  name: string;
}

interface Person {
  id: string;
  first_name: string;
  last_name: string;
  nationality?: string;
}

interface Team {
  id: string;
  name: string;
}

interface EntryFormProps {
  initialData?: any;
  onSubmit?: (formData: any) => Promise<void>;
  onDelete?: () => Promise<void>;
  preselectedRallyId?: string;
}

const EntryForm: React.FC<EntryFormProps> = ({
  initialData,
  onSubmit,
  onDelete,
  preselectedRallyId
}) => {
  const [form, setForm] = useState({
    rally_id: '',
    driver_id: '',
    codriver_id: '',
    car: '',
    team_id: '',
    number: '',
    class: '',
    status: 'entered' // Default to 'entered' which is a valid entry_status enum value
  });
  const [rallies, setRallies] = useState<Rally[]>([]);
  const [persons, setPersons] = useState<Person[]>([]);
  const [teams, setTeams] = useState<Team[]>([]);
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const navigate = useNavigate();

  // Status options - must match the entry_status enum in the database
  const statusOptions = [
    { value: 'entered', label: 'Entered' },
    { value: 'running', label: 'Running' },
    { value: 'finished', label: 'Finished' },
    { value: 'retired', label: 'Retired' },
    { value: 'dns', label: 'Did Not Start (DNS)' },
    { value: 'dnf', label: 'Did Not Finish (DNF)' },
    { value: 'dsq', label: 'Disqualified (DSQ)' },
  ];

  // Note: Class options removed as per request to make class a free text field

  useEffect(() => {
    fetchRallies();
    fetchPersons();
    fetchTeams();
  }, []);

  useEffect(() => {
    if (initialData) {
      setForm({
        rally_id: initialData.rally_id || '',
        driver_id: initialData.driver_id || '',
        codriver_id: initialData.codriver_id || '',
        car: initialData.car || '',
        team_id: initialData.team_id || '',
        number: initialData.number !== undefined && initialData.number !== null ? String(initialData.number) : '',
        class: initialData.class || '',
        status: initialData.status || ''
      });
    } else if (preselectedRallyId) {
      setForm(prev => ({ ...prev, rally_id: preselectedRallyId }));
    }
  }, [initialData, preselectedRallyId]);

  const fetchRallies = async () => {
    try {
      const res = await fetch('/api/rallies', { credentials: 'include' });
      if (!res.ok) throw new Error('Failed to fetch rallies');
      const data = await res.json();
      setRallies(Array.isArray(data) ? data : data.rallies || []);
    } catch (err: any) {
      setError(err.message);
    }
  };

  const fetchPersons = async () => {
    try {
      const res = await fetch('/api/persons', { credentials: 'include' });
      if (!res.ok) throw new Error('Failed to fetch persons');
      const data = await res.json();
      setPersons(Array.isArray(data) ? data : data.persons || []);
    } catch (err: any) {
      setError(err.message);
    }
  };

  const fetchTeams = async () => {
    try {
      const res = await fetch('/api/teams', { credentials: 'include' });
      if (!res.ok) throw new Error('Failed to fetch teams');
      const data = await res.json();
      setTeams(Array.isArray(data) ? data : data.teams || []);
    } catch (err: any) {
      setError(err.message);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const validateForm = () => {
    const errors: Record<string, string> = {};

    if (!form.rally_id) errors.rally_id = 'Rally is required';
    if (!form.driver_id) errors.driver_id = 'Driver is required';
    if (!form.codriver_id) errors.codriver_id = 'Codriver is required';
    if (!form.car.trim()) errors.car = 'Car is required';
    if (!form.number.trim()) errors.number = 'Number is required';
    if (isNaN(Number(form.number))) errors.number = 'Number must be a number';
    if (!form.class) errors.class = 'Class is required';
    if (!form.status) errors.status = 'Status is required';

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    setLoading(true);
    setError('');

    try {
      // Format the form data for submission
      const formattedData = {
        ...form,
        number: parseInt(form.number),
        team_id: form.team_id ? form.team_id : null
      };

      // Ensure the selected driver is in the drivers table
      await ensurePersonIsDriver(form.driver_id);

      // Ensure the selected codriver is in the codrivers table
      await ensurePersonIsCodriver(form.codriver_id);

      if (onSubmit) {
        await onSubmit(formattedData);
      } else {
        // Default submission logic if no onSubmit provided
        const url = initialData ? `/api/entries/${initialData.id}` : '/api/entries';
        const method = initialData ? 'PUT' : 'POST';

        // Log the form data being sent
        console.log('Submitting entry form data:', formattedData);

        const response = await fetch(url, {
          method,
          headers: { 'Content-Type': 'application/json' },
          credentials: 'include',
          body: JSON.stringify(formattedData)
        });

        // Log the response
        console.log('Response status:', response.status);

        try {
          const responseData = await response.json();
          console.log('Response data:', responseData);

          if (!response.ok) {
            const errorMessage = responseData.message || responseData.error || `Failed to ${initialData ? 'update' : 'add'} entry`;
            console.error('Error details from server:', responseData);
            throw new Error(errorMessage);
          }
        } catch (jsonError) {
          console.error('Error parsing JSON response:', jsonError);
          if (!response.ok) {
            throw new Error(`Failed to ${initialData ? 'update' : 'add'} entry. Server returned status ${response.status}`);
          }
        }

        navigate('/admin/entries');
      }
    } catch (err: any) {
      setError(err.message);
      window.scrollTo(0, 0); // Scroll to top to show error
    } finally {
      setLoading(false);
    }
  };

  // Helper function to ensure a person is registered as a driver
  const ensurePersonIsDriver = async (personId: string) => {
    try {
      // First, check if the person is already a driver
      const checkResponse = await fetch(`/api/drivers/${personId}`, {
        method: 'GET',
        credentials: 'include'
      });

      // If not found (404), add them as a driver
      if (checkResponse.status === 404) {
        console.log(`Person ${personId} is not a driver yet. Adding to drivers table...`);

        // Add the person as a driver
        const addDriverResponse = await fetch('/api/drivers', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          credentials: 'include',
          body: JSON.stringify({ id: personId })
        });

        if (!addDriverResponse.ok) {
          console.warn(`Failed to add person ${personId} as a driver. The server will handle this.`);
        } else {
          console.log(`Successfully added person ${personId} as a driver.`);
        }
      } else if (!checkResponse.ok && checkResponse.status !== 404) {
        throw new Error('Error checking driver status');
      }
    } catch (err: any) {
      console.error('Error ensuring person is a driver:', err);
      // We'll continue anyway and let the server handle any issues
    }
  };

  // Helper function to ensure a person is registered as a codriver
  const ensurePersonIsCodriver = async (personId: string) => {
    try {
      // First, check if the person is already a codriver
      const checkResponse = await fetch(`/api/codrivers/${personId}`, {
        method: 'GET',
        credentials: 'include'
      });

      // If not found (404), add them as a codriver
      if (checkResponse.status === 404) {
        console.log(`Person ${personId} is not a codriver yet. Adding to codrivers table...`);

        // Add the person as a codriver
        const addCodriverResponse = await fetch('/api/codrivers', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          credentials: 'include',
          body: JSON.stringify({ id: personId })
        });

        if (!addCodriverResponse.ok) {
          console.warn(`Failed to add person ${personId} as a codriver. The server will handle this.`);
        } else {
          console.log(`Successfully added person ${personId} as a codriver.`);
        }
      } else if (!checkResponse.ok && checkResponse.status !== 404) {
        throw new Error('Error checking codriver status');
      }
    } catch (err: any) {
      console.error('Error ensuring person is a codriver:', err);
      // We'll continue anyway and let the server handle any issues
    }
  };

  const handleDelete = async () => {
    if (!initialData || !onDelete) return;

    if (!window.confirm('Are you sure you want to delete this entry?')) {
      return;
    }

    try {
      await onDelete();
    } catch (err: any) {
      setError(err.message);
    }
  };

  const handleCancel = () => {
    navigate('/admin/entries');
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      {error && (
        <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Rally
          </label>
          <select
            name="rally_id"
            value={form.rally_id}
            onChange={handleChange}
            required
            className={`w-full px-3 py-2 rounded border ${
              validationErrors.rally_id ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
            } dark:bg-gray-700 dark:text-white`}
          >
            <option value="">Select Rally</option>
            {rallies.map(rally => (
              <option key={rally.id} value={rally.id}>{rally.name}</option>
            ))}
          </select>
          {validationErrors.rally_id && (
            <p className="mt-1 text-sm text-red-500">{validationErrors.rally_id}</p>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Driver
            </label>
            <select
              name="driver_id"
              value={form.driver_id}
              onChange={handleChange}
              required
              className={`w-full px-3 py-2 rounded border ${
                validationErrors.driver_id ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
              } dark:bg-gray-700 dark:text-white`}
            >
              <option value="">Select Driver</option>
              {persons.map(person => (
                <option key={person.id} value={person.id}>{person.first_name} {person.last_name}</option>
              ))}
            </select>
            {validationErrors.driver_id && (
              <p className="mt-1 text-sm text-red-500">{validationErrors.driver_id}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Codriver
            </label>
            <select
              name="codriver_id"
              value={form.codriver_id}
              onChange={handleChange}
              required
              className={`w-full px-3 py-2 rounded border ${
                validationErrors.codriver_id ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
              } dark:bg-gray-700 dark:text-white`}
            >
              <option value="">Select Codriver</option>
              {persons.map(person => (
                <option key={person.id} value={person.id}>{person.first_name} {person.last_name}</option>
              ))}
            </select>
            {validationErrors.codriver_id && (
              <p className="mt-1 text-sm text-red-500">{validationErrors.codriver_id}</p>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Car
            </label>
            <input
              name="car"
              value={form.car}
              onChange={handleChange}
              required
              placeholder="Car (e.g. Toyota GR Yaris Rally1)"
              className={`w-full px-3 py-2 rounded border ${
                validationErrors.car ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
              } dark:bg-gray-700 dark:text-white`}
            />
            {validationErrors.car && (
              <p className="mt-1 text-sm text-red-500">{validationErrors.car}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Team
            </label>
            <select
              name="team_id"
              value={form.team_id}
              onChange={handleChange}
              className="w-full px-3 py-2 rounded border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
            >
              <option value="">No Team (Private Entry)</option>
              {teams.map(team => (
                <option key={team.id} value={team.id}>{team.name}</option>
              ))}
            </select>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Number
            </label>
            <input
              name="number"
              value={form.number}
              onChange={handleChange}
              required
              type="number"
              min="1"
              placeholder="Car Number"
              className={`w-full px-3 py-2 rounded border ${
                validationErrors.number ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
              } dark:bg-gray-700 dark:text-white`}
            />
            {validationErrors.number && (
              <p className="mt-1 text-sm text-red-500">{validationErrors.number}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Class
            </label>
            <input
              name="class"
              value={form.class}
              onChange={handleChange}
              required
              placeholder="Enter classes separated by commas (e.g. C6, F2, Under 30)"
              className={`w-full px-3 py-2 rounded border ${
                validationErrors.class ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
              } dark:bg-gray-700 dark:text-white`}
            />
            {validationErrors.class && (
              <p className="mt-1 text-sm text-red-500">{validationErrors.class}</p>
            )}
            {!validationErrors.class && (
              <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                Separate multiple classes with commas (e.g. C6, F2, Under 30)
              </p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Status
            </label>
            <select
              name="status"
              value={form.status}
              onChange={handleChange}
              required
              className={`w-full px-3 py-2 rounded border ${
                validationErrors.status ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
              } dark:bg-gray-700 dark:text-white`}
            >
              <option value="">Select Status</option>
              {statusOptions.map(option => (
                <option key={option.value} value={option.value}>{option.label}</option>
              ))}
            </select>
            {validationErrors.status && (
              <p className="mt-1 text-sm text-red-500">{validationErrors.status}</p>
            )}
          </div>
        </div>

        <div className="flex justify-between pt-4">
          <div>
            {initialData && onDelete && (
              <button
                type="button"
                onClick={handleDelete}
                className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
              >
                Delete
              </button>
            )}
          </div>
          <div className="flex space-x-3">
            <button
              type="button"
              onClick={handleCancel}
              className="px-4 py-2 border border-gray-300 rounded text-gray-700 bg-white hover:bg-gray-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:hover:bg-gray-600"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors disabled:opacity-50"
            >
              {loading ? (initialData ? 'Saving...' : 'Adding...') : (initialData ? 'Save Changes' : 'Add Entry')}
            </button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default EntryForm;
