import React, { useState, useRef, useEffect } from 'react';
import { X, Plus } from 'lucide-react';

interface ClassInputProps {
  value: string;
  onChange: (value: string) => void;
  separator?: string;
  placeholder?: string;
  suggestions?: string[];
  className?: string;
  error?: string;
}

const ClassInput: React.FC<ClassInputProps> = ({
  value,
  onChange,
  separator = ',',
  placeholder = 'Enter classes (e.g. RC1, RC2, WRC)',
  suggestions = ['RC1', 'RC2', 'RC3', 'RC4', 'RC5', 'WRC', 'WRC2', 'WRC3', 'R5', 'Rally2', 'Rally3', 'Rally4', 'Rally5'],
  className = '',
  error
}) => {
  const [inputValue, setInputValue] = useState('');
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [filteredSuggestions, setFilteredSuggestions] = useState<string[]>([]);
  const inputRef = useRef<HTMLInputElement>(null);

  // Parse current classes
  const currentClasses = value
    .split(separator)
    .map(cls => cls.trim())
    .filter(cls => cls.length > 0);

  // Filter suggestions based on input and exclude already selected classes
  useEffect(() => {
    if (inputValue.trim()) {
      const filtered = suggestions.filter(suggestion =>
        suggestion.toLowerCase().includes(inputValue.toLowerCase()) &&
        !currentClasses.some(cls => cls.toLowerCase() === suggestion.toLowerCase())
      );
      setFilteredSuggestions(filtered);
    } else {
      setFilteredSuggestions(
        suggestions.filter(suggestion =>
          !currentClasses.some(cls => cls.toLowerCase() === suggestion.toLowerCase())
        )
      );
    }
  }, [inputValue, suggestions, currentClasses, separator]);

  const addClass = (className: string) => {
    const trimmedClass = className.trim();
    if (trimmedClass && !currentClasses.some(cls => cls.toLowerCase() === trimmedClass.toLowerCase())) {
      const newClasses = [...currentClasses, trimmedClass];
      onChange(newClasses.join(`${separator} `));
      setInputValue('');
      setShowSuggestions(false);
    }
  };

  const removeClass = (index: number) => {
    const newClasses = currentClasses.filter((_, i) => i !== index);
    onChange(newClasses.join(`${separator} `));
  };

  const handleInputKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === separator) {
      e.preventDefault();
      if (inputValue.trim()) {
        addClass(inputValue);
      }
    } else if (e.key === 'Backspace' && !inputValue && currentClasses.length > 0) {
      removeClass(currentClasses.length - 1);
    }
  };

  const handleSuggestionClick = (suggestion: string) => {
    addClass(suggestion);
    inputRef.current?.focus();
  };

  return (
    <div className="relative">
      {/* Selected Classes */}
      <div className={`min-h-[42px] w-full px-3 py-2 rounded border ${
        error ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
      } dark:bg-gray-700 dark:text-white flex flex-wrap gap-2 items-center ${className}`}>
        {currentClasses.map((cls, index) => (
          <span
            key={index}
            className="inline-flex items-center px-2 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
          >
            {cls}
            <button
              type="button"
              onClick={() => removeClass(index)}
              className="ml-1 text-blue-600 hover:text-blue-800 dark:text-blue-300 dark:hover:text-blue-100"
            >
              <X className="w-3 h-3" />
            </button>
          </span>
        ))}
        
        {/* Input for new class */}
        <input
          ref={inputRef}
          type="text"
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          onKeyDown={handleInputKeyDown}
          onFocus={() => setShowSuggestions(true)}
          onBlur={() => setTimeout(() => setShowSuggestions(false), 200)}
          placeholder={currentClasses.length === 0 ? placeholder : 'Add another class...'}
          className="flex-1 min-w-[120px] bg-transparent border-none outline-none text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
        />
      </div>

      {/* Error message */}
      {error && (
        <p className="mt-1 text-sm text-red-500">{error}</p>
      )}

      {/* Suggestions dropdown */}
      {showSuggestions && filteredSuggestions.length > 0 && (
        <div className="absolute z-10 w-full mt-1 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-60 overflow-auto">
          {filteredSuggestions.map((suggestion, index) => (
            <button
              key={index}
              type="button"
              onClick={() => handleSuggestionClick(suggestion)}
              className="w-full px-3 py-2 text-left hover:bg-gray-100 dark:hover:bg-gray-600 text-gray-900 dark:text-white"
            >
              {suggestion}
            </button>
          ))}
        </div>
      )}

      {/* Help text */}
      <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
        Type a class name and press Enter or "{separator}" to add. Click suggestions or type custom classes.
      </p>
    </div>
  );
};

export default ClassInput;
