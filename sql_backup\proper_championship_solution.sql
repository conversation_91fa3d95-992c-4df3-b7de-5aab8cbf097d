-- Proper Championship Solution
-- Works with your existing schema - no new tables needed!

-- 1. First, let's understand what we have
SELECT 'Your existing championship structure:' as info;

-- Show existing championships
SELECT 'Available Championships:' as section;
SELECT id, name, year, type, description FROM championships ORDER BY year DESC, name;

-- Show existing championship events
SELECT 'Championship Events:' as section;
SELECT 
    c.name as championship,
    r.name as rally,
    r.start_date,
    ce.coefficient
FROM championship_events ce
JOIN championships c ON c.id = ce.championship_id
JOIN rallies r ON r.id = ce.rally_id
ORDER BY c.name, r.start_date;

-- 2. Check what rallies are NOT in any championship
SELECT 'Rallies not assigned to any championship:' as unassigned;
SELECT 
    r.id,
    r.name,
    r.country,
    r.start_date,
    COUNT(e.id) as total_entries,
    STRING_AGG(DISTINCT e.class, ', ') as classes_available
FROM rallies r
LEFT JOIN entries e ON r.id = e.rally_id
WHERE r.id NOT IN (SELECT DISTINCT rally_id FROM championship_events)
AND e.class IS NOT NULL
GROUP BY r.id, r.name, r.country, r.start_date
ORDER BY r.start_date DESC;

-- 3. Show what classes exist across all rallies
SELECT 'All classes in your database:' as classes;
SELECT 
    TRIM(unnest(string_to_array(e.class, ','))) as class_name,
    COUNT(*) as usage_count,
    COUNT(DISTINCT r.country) as countries,
    STRING_AGG(DISTINCT r.country, ', ') as country_list
FROM entries e
JOIN rallies r ON e.rally_id = r.id
WHERE e.class IS NOT NULL AND e.class != ''
GROUP BY TRIM(unnest(string_to_array(e.class, ',')))
ORDER BY usage_count DESC;

-- 4. Test your existing championship views
SELECT 'Testing championship_overall_classification:' as test;
SELECT 
    championship_name,
    rally_name,
    COUNT(*) as entries,
    STRING_AGG(DISTINCT class, ', ') as classes
FROM championship_overall_classification
GROUP BY championship_id, championship_name, rally_id, rally_name
ORDER BY championship_name, rally_name
LIMIT 10;

SELECT 'Testing championship_standings:' as test;
SELECT 
    championship_name,
    position,
    driver,
    rallies_completed,
    total_points,
    total_power_stage_points,
    grand_total_points
FROM championship_standings
WHERE position <= 5
ORDER BY championship_name, position;

SELECT 'Testing championship_class_standings:' as test;
SELECT 
    championship_name,
    class,
    class_position,
    driver,
    rallies_completed,
    class_points
FROM championship_class_standings
WHERE class_position <= 3
ORDER BY championship_name, class, class_position
LIMIT 15;

-- 5. Suggestions for populating championships
SELECT 'Suggested rally assignments:' as suggestions;

-- Find rallies with C-classes for Greece championship
SELECT 'Rallies with C-classes (for Greece championship):' as suggestion_type;
SELECT DISTINCT
    r.id,
    r.name,
    r.country,
    r.start_date,
    STRING_AGG(DISTINCT e.class, ', ') as c_classes_found
FROM rallies r
JOIN entries e ON r.id = e.rally_id
WHERE (e.class LIKE '%C1%' OR e.class LIKE '%C2%' OR e.class LIKE '%C3%' 
       OR e.class LIKE '%C4%' OR e.class LIKE '%C5%' OR e.class LIKE '%C6%')
AND r.id NOT IN (SELECT rally_id FROM championship_events WHERE championship_id = '55a003a9-66ff-4a37-b11d-2e14df10bae3')
GROUP BY r.id, r.name, r.country, r.start_date
ORDER BY r.start_date DESC;

-- Find rallies with numeric classes for Historic championships
SELECT 'Rallies with numeric classes (for Historic championships):' as suggestion_type;
SELECT DISTINCT
    r.id,
    r.name,
    r.country,
    r.start_date,
    STRING_AGG(DISTINCT e.class, ', ') as numeric_classes_found
FROM rallies r
JOIN entries e ON r.id = e.rally_id
WHERE e.class IN ('1', '2', '3', '4')
AND r.id NOT IN (
    SELECT rally_id FROM championship_events 
    WHERE championship_id IN ('cc1e9147-fdb3-4483-aad3-9e27795eff17', 'aa934657-d4f5-49bb-80d5-b0be90e72b4c')
)
GROUP BY r.id, r.name, r.country, r.start_date
ORDER BY r.start_date DESC;

-- 6. Ready-to-run assignment queries
SELECT 'Ready-to-run assignment queries:' as ready_queries;

-- You can copy and run these individually:

/*
-- Add rallies with C-classes to Greece Championship
INSERT INTO championship_events (championship_id, rally_id, coefficient)
SELECT DISTINCT 
    '55a003a9-66ff-4a37-b11d-2e14df10bae3'::uuid,
    r.id,
    1.0
FROM rallies r
JOIN entries e ON r.id = e.rally_id
WHERE (e.class LIKE '%C1%' OR e.class LIKE '%C2%' OR e.class LIKE '%C3%' 
       OR e.class LIKE '%C4%' OR e.class LIKE '%C5%' OR e.class LIKE '%C6%')
AND r.id NOT IN (
    SELECT rally_id FROM championship_events 
    WHERE championship_id = '55a003a9-66ff-4a37-b11d-2e14df10bae3'
)
ON CONFLICT DO NOTHING;

-- Add rallies with numeric classes to Historic Championship
INSERT INTO championship_events (championship_id, rally_id, coefficient)
SELECT DISTINCT 
    'cc1e9147-fdb3-4483-aad3-9e27795eff17'::uuid,
    r.id,
    1.0
FROM rallies r
JOIN entries e ON r.id = e.rally_id
WHERE e.class IN ('2', '3', '4')
AND r.id NOT IN (
    SELECT rally_id FROM championship_events 
    WHERE championship_id = 'cc1e9147-fdb3-4483-aad3-9e27795eff17'
)
ON CONFLICT DO NOTHING;

-- Add rallies with numeric classes to Historic Gravel Cup
INSERT INTO championship_events (championship_id, rally_id, coefficient)
SELECT DISTINCT 
    'aa934657-d4f5-49bb-80d5-b0be90e72b4c'::uuid,
    r.id,
    1.0
FROM rallies r
JOIN entries e ON r.id = e.rally_id
WHERE e.class IN ('2', '3', '4')
AND r.id NOT IN (
    SELECT rally_id FROM championship_events 
    WHERE championship_id = 'aa934657-d4f5-49bb-80d5-b0be90e72b4c'
)
ON CONFLICT DO NOTHING;

-- Add Rally3 cars to Rally3 Championship (if you have Rally3 entries)
INSERT INTO championship_events (championship_id, rally_id, coefficient)
SELECT DISTINCT 
    '895f159e-f147-439b-b5ab-04972033a7bb'::uuid,
    r.id,
    1.0
FROM rallies r
JOIN entries e ON r.id = e.rally_id
WHERE (e.class LIKE '%Rally3%' OR e.class LIKE '%R3%')
AND r.id NOT IN (
    SELECT rally_id FROM championship_events 
    WHERE championship_id = '895f159e-f147-439b-b5ab-04972033a7bb'
)
ON CONFLICT DO NOTHING;
*/
