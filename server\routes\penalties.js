import express from 'express';
import pool from '../config/db.js';
import { verifyToken, verifyAdmin } from '../middleware/auth.js';

const router = express.Router();

// Create penalty
router.post('/', verifyToken, verifyAdmin, async (req, res) => {
  try {
    const { entry_id, stage_id, penalty_type, penalty_value, notes } = req.body;
    const result = await pool.query(
      `INSERT INTO penalties (entry_id, stage_id, penalty_type, penalty_value, notes)
       VALUES ($1, $2, $3, $4, $5) RETURNING *`,
      [entry_id, stage_id, penalty_type, penalty_value, notes]
    );
    res.status(201).json(result.rows[0]);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Failed to add penalty' });
  }
});

// Get all penalties (with rally, stage, and entry info) - PUBLIC ROUTE
router.get('/', async (req, res) => {
  try {
    const result = await pool.query(`
      SELECT p.*, r.name AS rally_name, s.name AS stage_name, e.number AS entry_number
      FROM penalties p
      JOIN rallies r ON p.rally_id = r.id
      LEFT JOIN stages s ON p.stage_id = s.id
      JOIN entries e ON p.entry_id = e.id
      ORDER BY p.created_at DESC
    `);
    res.json(result.rows);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Failed to fetch penalties' });
  }
});

// Get single penalty (with rally, stage, and entry info) - PUBLIC ROUTE
router.get('/:id', async (req, res) => {
  try {
    const penaltyId = req.params.id;
    const result = await pool.query(`
      SELECT p.*, r.name AS rally_name, s.name AS stage_name, e.number AS entry_number
      FROM penalties p
      JOIN rallies r ON p.rally_id = r.id
      LEFT JOIN stages s ON p.stage_id = s.id
      JOIN entries e ON p.entry_id = e.id
      WHERE p.id = $1
    `, [penaltyId]);
    if (result.rows.length === 0) return res.status(404).json({ message: 'Penalty not found' });
    res.json(result.rows[0]);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Failed to fetch penalty' });
  }
});

// Update penalty
router.put('/:id', verifyToken, verifyAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { entry_id, stage_id, penalty_type, penalty_value, notes } = req.body;
    const result = await pool.query(
      `UPDATE penalties SET entry_id=$1, stage_id=$2, penalty_type=$3, penalty_value=$4, notes=$5, updated_at=NOW() WHERE id=$6 RETURNING *`,
      [entry_id, stage_id, penalty_type, penalty_value, notes, id]
    );
    if (result.rows.length === 0) return res.status(404).json({ message: 'Penalty not found' });
    res.json(result.rows[0]);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Failed to update penalty' });
  }
});

// Delete penalty
router.delete('/:id', verifyToken, verifyAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const result = await pool.query('DELETE FROM penalties WHERE id = $1 RETURNING *', [id]);
    if (result.rows.length === 0) return res.status(404).json({ message: 'Penalty not found' });
    res.json({ message: 'Penalty deleted', penalty: result.rows[0] });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Failed to delete penalty' });
  }
});

export default router;
