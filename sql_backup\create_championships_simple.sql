-- Alternative approach: Create championships without adding constraints
-- This uses WHERE NOT EXISTS to avoid duplicates

-- Insert championships only if they don't already exist
INSERT INTO championships (name, year, type, description)
SELECT 'Greece', 2025, 'national', 'Greek National Rally Championship 2025'
WHERE NOT EXISTS (SELECT 1 FROM championships WHERE name = 'Greece' AND year = 2025);

INSERT INTO championships (name, year, type, description)
SELECT 'Historic (GR)', 2025, 'national', 'Greek Historic Rally Championship 2025'
WHERE NOT EXISTS (SELECT 1 FROM championships WHERE name = 'Historic (GR)' AND year = 2025);

INSERT INTO championships (name, year, type, description)
SELECT 'Rally3 (GR)', 2025, 'national', 'Greek Rally3 Championship 2025'
WHERE NOT EXISTS (SELECT 1 FROM championships WHERE name = 'Rally3 (GR)' AND year = 2025);

INSERT INTO championships (name, year, type, description)
SELECT 'Historic Gravel Cup (GR)', 2025, 'local', 'Greek Historic Gravel Cup 2025'
WHERE NOT EXISTS (SELECT 1 FROM championships WHERE name = 'Historic Gravel Cup (GR)' AND year = 2025);

-- Verify the championships were created
SELECT id, name, year, type, description FROM championships WHERE year = 2025 ORDER BY name;
