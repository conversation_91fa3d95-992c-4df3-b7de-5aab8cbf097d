import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Clock } from 'lucide-react';
import StageResultForm from '../../../../components/admin/stage-result-form';

interface StageResult {
  id: string;
  rally_id: string;
  stage_id: string;
  entry_id: string;
  time: number;
  rally_name?: string;
  stage_name?: string;
  entry_number?: number;
}

const EditStageResultPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [stageResult, setStageResult] = useState<StageResult | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const navigate = useNavigate();

  useEffect(() => {
    if (id) {
      fetchStageResult();
    }
  }, [id]);

  const fetchStageResult = async () => {
    try {
      const res = await fetch(`/api/stageResults/${id}`, { credentials: 'include' });
      if (!res.ok) throw new Error('Failed to fetch stage result');
      const data = await res.json();
      setStageResult(data);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (formData: any) => {
    console.log('Updating stage result with data:', formData);
    
    const response = await fetch(`/api/stageResults/${id}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      credentials: 'include',
      body: JSON.stringify(formData)
    });

    // Log the response for debugging
    console.log('Response status:', response.status);
    
    if (!response.ok) {
      const data = await response.json();
      console.error('Error response:', data);
      throw new Error(data.message || data.error || 'Failed to update stage result');
    }

    navigate('/admin/stageResults');
  };

  const handleDelete = async () => {
    const response = await fetch(`/api/stageResults/${id}`, {
      method: 'DELETE',
      credentials: 'include',
    });

    if (!response.ok) {
      const data = await response.json();
      throw new Error(data.message || data.error || 'Failed to delete stage result');
    }

    navigate('/admin/stageResults');
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8 text-center">
        <div className="inline-block animate-spin rounded-full h-8 w-8 border-4 border-gray-300 border-t-red-600"></div>
        <p className="mt-2 text-gray-600 dark:text-gray-400">Loading stage result...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="p-4 bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-200 rounded">
          {error}
        </div>
      </div>
    );
  }

  if (!stageResult) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="p-4 bg-yellow-100 dark:bg-yellow-900 text-yellow-700 dark:text-yellow-200 rounded">
          Stage result not found
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center mb-6">
        <Clock className="w-6 h-6 text-red-600 mr-2" />
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          Edit Stage Result
          {stageResult.stage_name && <span className="ml-2 text-gray-600 dark:text-gray-400 text-lg">
            ({stageResult.stage_name})
          </span>}
        </h1>
      </div>

      <StageResultForm 
        initialData={stageResult} 
        onSubmit={handleSubmit}
        onDelete={handleDelete}
      />
    </div>
  );
};

export default EditStageResultPage;
