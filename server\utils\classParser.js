// Known class names for intelligent parsing
// Add all your rally classes here
const KNOWN_CLASSES = [
  // Single letter/number classes
  'C6', 'F2', 'E', 'A', 'B', 'C', 'D', 'F', 'G', 'H',
  'C1', 'C2', 'C3', 'C4', 'C5', 'C7', 'C8', 'C9',
  'F1', 'F3', 'F4', 'F5',
  'E1', 'E2', 'E3',
  
  // Multi-word classes
  'Under 30', 'Over 30', 'Under 25', 'Over 25', 'Under 35', 'Over 35',
  'Under 40', 'Over 40', 'Under 45', 'Over 45', 'Under 50', 'Over 50',
  'Ladies Class', 'Women Class', 'Junior Class', 'Senior Class',
  'Novice Class', 'Expert Class', 'Professional Class',
  'Historic Class', 'Classic Class', 'Modern Class',
  'Two Wheel Drive', '2WD', 'Four Wheel Drive', '4WD',
  'Front Wheel Drive', 'FWD', 'Rear Wheel Drive', 'RWD',
  
  // Rally-specific classes
  'RC1', 'RC2', 'RC3', 'RC4', 'RC5',
  'WRC', 'WRC2', 'WRC3',
  'R5', 'Rally2', 'Rally3', 'Rally4', 'Rally5',
  'N1', 'N2', 'N3', 'N4', 'N5',
  'A5', 'A6', 'A7', 'A8',
  
  // Add more as needed...
];

/**
 * Parse a class string with space-separated classes
 * Uses intelligent matching against known class names
 * @param {string} classString - The input string like "C6 F2 Under 30"
 * @returns {string[]} - Array of individual classes
 */
export function parseClasses(classString) {
  if (!classString || typeof classString !== 'string') {
    return [];
  }

  const input = classString.trim();
  if (!input) return [];

  const classes = [];
  let remaining = input;

  // Sort known classes by length (longest first) to match multi-word classes first
  const sortedKnownClasses = [...KNOWN_CLASSES].sort((a, b) => b.length - a.length);

  while (remaining.length > 0) {
    let matched = false;
    
    // Try to match against known classes
    for (const knownClass of sortedKnownClasses) {
      const regex = new RegExp(`^${escapeRegex(knownClass)}(?:\\s|$)`, 'i');
      if (regex.test(remaining)) {
        classes.push(knownClass);
        remaining = remaining.substring(knownClass.length).trim();
        matched = true;
        break;
      }
    }
    
    // If no known class matched, take the first word
    if (!matched) {
      const firstWord = remaining.split(/\s+/)[0];
      classes.push(firstWord);
      remaining = remaining.substring(firstWord.length).trim();
    }
  }

  return classes;
}

/**
 * Join classes back into a string
 * @param {string[]} classes - Array of class names
 * @returns {string} - Space-separated class string
 */
export function joinClasses(classes) {
  if (!Array.isArray(classes)) return '';
  return classes.filter(cls => cls && cls.trim()).join(' ');
}

/**
 * Check if an entry belongs to a specific class
 * @param {string} entryClasses - The entry's class string
 * @param {string} targetClass - The class to check for
 * @returns {boolean}
 */
export function entryHasClass(entryClasses, targetClass) {
  const classes = parseClasses(entryClasses);
  return classes.some(cls => cls.toLowerCase() === targetClass.toLowerCase());
}

/**
 * Get all unique classes from an array of class strings
 * @param {string[]} classStrings - Array of class strings
 * @returns {string[]} - Array of unique class names
 */
export function getAllUniqueClasses(classStrings) {
  const allClasses = new Set();
  
  classStrings.forEach(classString => {
    const classes = parseClasses(classString);
    classes.forEach(cls => allClasses.add(cls));
  });
  
  return Array.from(allClasses).sort();
}

/**
 * Escape special regex characters
 * @param {string} string 
 * @returns {string}
 */
function escapeRegex(string) {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

/**
 * Add new known classes (for dynamic updates)
 * @param {string[]} newClasses 
 */
export function addKnownClasses(newClasses) {
  newClasses.forEach(cls => {
    if (cls && !KNOWN_CLASSES.includes(cls)) {
      KNOWN_CLASSES.push(cls);
    }
  });
}

/**
 * Get all known classes
 * @returns {string[]}
 */
export function getKnownClasses() {
  return [...KNOWN_CLASSES];
}

// Example usage:
// parseClasses("C6 F2 Under 30") => ["C6", "F2", "Under 30"]
// parseClasses("C6 F2 E") => ["C6", "F2", "E"]
// entryHasClass("C6 F2 Under 30", "Under 30") => true
