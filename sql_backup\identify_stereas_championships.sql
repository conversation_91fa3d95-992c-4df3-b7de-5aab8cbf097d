-- Script to identify and configure Rally Stereas Elladas 2025 championships

-- Step 1: Find the rally ID
SELECT 'Rally Information:' as info;
SELECT 
    id as rally_id,
    name,
    start_date,
    country
FROM rallies 
WHERE name LIKE '%Stereas%' OR name LIKE '%Elladas%'
ORDER BY start_date DESC;

-- Step 2: Find all championships associated with this rally
SELECT 'Championships for Rally Stereas Elladas 2025:' as info;
SELECT 
    c.id as championship_id,
    c.name as championship_name,
    c.year,
    ce.coefficient
FROM championships c
JOIN championship_events ce ON c.id = ce.championship_id
JOIN rallies r ON ce.rally_id = r.id
WHERE r.name LIKE '%Stereas%' AND r.name LIKE '%2025%'
ORDER BY c.name;

-- Step 3: Show current entry distribution (all entries in all championships)
SELECT 'Current entries per championship (BEFORE filtering):' as info;
SELECT 
    c.name as championship,
    COUNT(e.id) as total_entries
FROM championships c
JOIN championship_events ce ON c.id = ce.championship_id
JOIN rallies r ON ce.rally_id = r.id
JOIN entries e ON r.id = e.rally_id
WHERE r.name LIKE '%Stereas%' AND r.name LIKE '%2025%'
GROUP BY c.id, c.name
ORDER BY c.name;

-- Step 4: Show class distribution in the rally
SELECT 'Class distribution in Rally Stereas Elladas 2025:' as info;
SELECT 
    e.class,
    COUNT(*) as entry_count,
    ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER (), 1) as percentage
FROM entries e
JOIN rallies r ON e.rally_id = r.id
WHERE r.name LIKE '%Stereas%' AND r.name LIKE '%2025%'
GROUP BY e.class
ORDER BY entry_count DESC;

-- Step 5: Suggested championship class mappings
-- Copy the championship IDs from Step 2 and use them below

SELECT 'SUGGESTED SETUP - Replace championship IDs with actual ones:' as info;

-- Example setup commands (replace with your actual championship IDs):
SELECT 'For Championship 1 (Top Classes):' as setup_info;
SELECT 'INSERT INTO championship_classes (championship_id, class_name) VALUES' as sql_command;
SELECT '(''your-championship-1-id'', ''C1''),' as sql_values;
SELECT '(''your-championship-1-id'', ''C2''),' as sql_values;
SELECT '(''your-championship-1-id'', ''2''),' as sql_values;
SELECT '(''your-championship-1-id'', ''3'');' as sql_values;

SELECT 'For Championship 2 (Mid Classes):' as setup_info;
SELECT 'INSERT INTO championship_classes (championship_id, class_name) VALUES' as sql_command;
SELECT '(''your-championship-2-id'', ''C3''),' as sql_values;
SELECT '(''your-championship-2-id'', ''C4''),' as sql_values;
SELECT '(''your-championship-2-id'', ''4'');' as sql_values;

SELECT 'For Championship 3 (Lower Classes):' as setup_info;
SELECT 'INSERT INTO championship_classes (championship_id, class_name) VALUES' as sql_command;
SELECT '(''your-championship-3-id'', ''C5''),' as sql_values;
SELECT '(''your-championship-3-id'', ''C6'');' as sql_values;

SELECT 'For Championship 4 (Special/F2 Classes):' as setup_info;
SELECT 'INSERT INTO championship_classes (championship_id, class_name) VALUES' as sql_command;
SELECT '(''your-championship-4-id'', ''F2''),' as sql_values;
SELECT '(''your-championship-4-id'', ''F2 E'');' as sql_values;

-- Alternative: One championship for all classes
SELECT 'ALTERNATIVE - One championship for all classes:' as info;
SELECT 'INSERT INTO championship_classes (championship_id, class_name) VALUES' as sql_command;
SELECT '(''your-main-championship-id'', ''ALL'');' as sql_values;
