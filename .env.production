# Production Environment Variables
# Copy these to your hosting platform's environment variables section

# Server Configuration
NODE_ENV=production
PORT=3000

# Database Configuration (PostgreSQL)
# Replace with your actual database connection string
DATABASE_URL=postgresql://username:password@hostname:port/database_name

# JWT Secret (Generate a strong random string)
JWT_SECRET=your_super_secret_jwt_key_here_make_it_long_and_random

# Client URL (Your domain)
CLIENT_URL=https://yourdomain.com

# Optional: Database connection pool settings
DB_HOST=your_db_host
DB_PORT=5432
DB_NAME=your_db_name
DB_USER=your_db_user
DB_PASSWORD=your_db_password
DB_SSL=true
