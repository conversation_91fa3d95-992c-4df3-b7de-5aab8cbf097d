import express from 'express';
import pool from '../config/db.js';
import { verifyToken, verifyAdmin } from '../middleware/auth.js';
import multer from 'multer';

// Configure multer for file uploads
const upload = multer({ dest: 'server/uploads/' });

const router = express.Router();

// Add Rally (admin only)
router.post('/', verifyToken, verifyAdmin, async (req, res) => {
  try {
    const {
      name,
      country,
      start_date,
      end_date,
      surface,
      status,
      championship_id,
      logo_url,
      banner_url // <-- use banner_url per schema
    } = req.body;

    // Log the request body for debugging
    console.log('POST /api/rallies request body:', req.body);

    // Validate required fields
    if (!name) {
      return res.status(400).json({ message: 'Rally name is required' });
    }
    if (!country) {
      return res.status(400).json({ message: 'Country is required' });
    }
    if (!start_date) {
      return res.status(400).json({ message: 'Start date is required' });
    }
    if (!end_date) {
      return res.status(400).json({ message: 'End date is required' });
    }
    if (!surface) {
      return res.status(400).json({ message: 'Surface is required' });
    }
    if (!status) {
      return res.status(400).json({ message: 'Status is required' });
    }

    // Validate status is one of the allowed ENUM values
    if (!['upcoming', 'running', 'finished'].includes(status)) {
      return res.status(400).json({
        message: 'Invalid status value',
        error: `Status must be one of: upcoming, running, finished. Received: ${status}`
      });
    }

    try {
      const result = await pool.query(
        `INSERT INTO rallies (name, country, start_date, end_date, surface, status, logo_url, banner_url)
         VALUES ($1, $2, $3, $4, $5, $6, $7, $8) RETURNING *`,
        [
          name,
          country,
          start_date,
          end_date,
          surface,
          status,
          logo_url || null,
          banner_url || null
        ]
      );
      res.status(201).json(result.rows[0]);
    } catch (error) {
      console.error('Error in POST /api/rallies:', error);
      res.status(500).json({
        message: 'Failed to add rally',
        error: error.message,
        details: error.detail || error.toString()
      });
    }
  } catch (error) {
    console.error('Error in POST /api/rallies:', error);
    res.status(500).json({
      message: 'Failed to add rally',
      error: error.message,
      details: error.detail || error.toString()
    });
  }
});

// Edit Rally (admin only)
router.put('/:id', verifyToken, verifyAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      country,
      start_date,
      end_date,
      surface,
      status,
      logo_url,
      banner_url // <-- use banner_url per schema
    } = req.body;

    const result = await pool.query(
      `UPDATE rallies SET name=$1, country=$2, start_date=$3, end_date=$4, surface=$5, status=$6, logo_url=$7, banner_url=$8, updated_at=NOW()
       WHERE id=$9 RETURNING *`,
      [
        name,
        country,
        start_date,
        end_date,
        surface,
        status,
        logo_url,
        banner_url,
        id
      ]
    );
    if (result.rows.length === 0) {
      return res.status(404).json({ message: 'Rally not found' });
    }
    res.json(result.rows[0]);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Failed to update rally' });
  }
});

// Delete Rally (admin only)
router.delete('/:id', verifyToken, verifyAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const result = await pool.query('DELETE FROM rallies WHERE id = $1 RETURNING *', [id]);
    if (result.rows.length === 0) {
      return res.status(404).json({ message: 'Rally not found' });
    }
    res.json({ message: 'Rally deleted', rally: result.rows[0] });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Failed to delete rally' });
  }
});

// Get all rallies (with championship names) - PUBLIC ROUTE
router.get('/', async (req, res) => {
  try {
    const result = await pool.query(`
      SELECT r.*,
        COALESCE(
          STRING_AGG(c.name || ' ' || c.year, ', ' ORDER BY c.name),
          ''
        ) AS championship_names,
        COALESCE(
          JSON_AGG(
            JSON_BUILD_OBJECT(
              'id', c.id,
              'name', c.name,
              'year', c.year,
              'coefficient', ce.coefficient
            ) ORDER BY c.name
          ) FILTER (WHERE c.id IS NOT NULL),
          '[]'::json
        ) AS championships
      FROM rallies r
      LEFT JOIN championship_events ce ON r.id = ce.rally_id
      LEFT JOIN championships c ON ce.championship_id = c.id
      GROUP BY r.id
      ORDER BY r.start_date DESC
    `);
    res.json(result.rows);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Failed to fetch rallies' });
  }
});

// Get single rally by id (with championship names) - PUBLIC ROUTE
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const result = await pool.query(`
      SELECT r.*,
        COALESCE(
          STRING_AGG(c.name || ' ' || c.year, ', ' ORDER BY c.name),
          ''
        ) AS championship_names,
        COALESCE(
          JSON_AGG(
            JSON_BUILD_OBJECT(
              'id', c.id,
              'name', c.name,
              'year', c.year,
              'coefficient', ce.coefficient
            ) ORDER BY c.name
          ) FILTER (WHERE c.id IS NOT NULL),
          '[]'::json
        ) AS championships
      FROM rallies r
      LEFT JOIN championship_events ce ON r.id = ce.rally_id
      LEFT JOIN championships c ON ce.championship_id = c.id
      WHERE r.id = $1
      GROUP BY r.id
    `, [id]);
    if (result.rows.length === 0) {
      return res.status(404).json({ message: 'Rally not found' });
    }
    res.json(result.rows[0]);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Failed to fetch rally' });
  }
});

// Import rally data from EWRC JSON - ADMIN ROUTE
router.post('/import-ewrc', verifyToken, verifyAdmin, upload.single('file'), async (req, res) => {
  const client = await pool.connect();
  try {
    if (!req.file) {
      return res.status(400).json({ message: 'No JSON file uploaded' });
    }

    // Parse JSON file
    const fs = await import('fs');
    const jsonContent = fs.readFileSync(req.file.path, 'utf8');
    const ewrcData = JSON.parse(jsonContent);

    await client.query('BEGIN');

    // Debug: Log the structure of incoming data
    console.log('=== EWRC IMPORT DEBUG ===');
    console.log('Rally info:', JSON.stringify(ewrcData.rally_info, null, 2));
    console.log('Total results:', ewrcData.results?.length || 0);

    // Debug: Show first few results to check structure
    if (ewrcData.results && ewrcData.results.length > 0) {
      console.log('First result sample:', JSON.stringify(ewrcData.results[0], null, 2));
      if (ewrcData.results.length > 1) {
        console.log('Second result sample:', JSON.stringify(ewrcData.results[1], null, 2));
      }
    }

    // Helper function to parse driver-codriver names
    const parseDriverCodriver = (driverCodriverStr) => {
      const parts = driverCodriverStr.split(' - ');
      if (parts.length !== 2) {
        throw new Error(`Invalid driver-codriver format: ${driverCodriverStr}`);
      }

      const parseFullName = (fullName) => {
        const nameParts = fullName.trim().split(' ');

        // Greek names in EWRC are typically in "LastName FirstName" format
        // So we need to reverse this to get "FirstName LastName" for database storage
        if (nameParts.length >= 2) {
          const lastName = nameParts[0]; // First word is actually the last name
          const firstName = nameParts.slice(1).join(' '); // Rest is the first name
          return { firstName, lastName };
        } else {
          // Single name - use as both first and last name
          return { firstName: nameParts[0], lastName: nameParts[0] };
        }
      };

      return {
        driver: parseFullName(parts[0]),
        codriver: parseFullName(parts[1])
      };
    };

    // Helper function to find or create person (WITH FLEXIBLE DUPLICATE PREVENTION)
    const findOrCreatePerson = async (firstName, lastName, nationality = 'Unknown') => {
      // Strategy 1: Exact match (case-insensitive)
      let existingPerson = await client.query(
        'SELECT id, first_name, last_name FROM persons WHERE LOWER(first_name) = LOWER($1) AND LOWER(last_name) = LOWER($2)',
        [firstName, lastName]
      );

      if (existingPerson.rows.length > 0) {
        console.log(`Found exact match: ${firstName} ${lastName} → ${existingPerson.rows[0].first_name} ${existingPerson.rows[0].last_name} (ID: ${existingPerson.rows[0].id})`);
        return existingPerson.rows[0].id;
      }

      // Strategy 2: Handle abbreviated names (e.g., "G." matches "George")
      // Check if lastName is abbreviated (single letter + dot)
      const isAbbreviated = /^[A-Z]\.?$/i.test(lastName);

      if (isAbbreviated) {
        // Remove dot and get just the letter
        const abbreviation = lastName.replace('.', '').toUpperCase();

        // Find persons with same first name and last name starting with this letter
        existingPerson = await client.query(
          'SELECT id, first_name, last_name FROM persons WHERE LOWER(first_name) = LOWER($1) AND UPPER(LEFT(last_name, 1)) = $2',
          [firstName, abbreviation]
        );

        if (existingPerson.rows.length === 1) {
          console.log(`Found abbreviated match: ${firstName} ${lastName} → ${existingPerson.rows[0].first_name} ${existingPerson.rows[0].last_name} (ID: ${existingPerson.rows[0].id})`);
          return existingPerson.rows[0].id;
        } else if (existingPerson.rows.length > 1) {
          console.log(`Multiple matches found for ${firstName} ${lastName}:`);
          existingPerson.rows.forEach(person => {
            console.log(`  - ${person.first_name} ${person.last_name} (ID: ${person.id})`);
          });
          // Use the first match but log the ambiguity
          console.log(`Using first match: ${existingPerson.rows[0].first_name} ${existingPerson.rows[0].last_name} (ID: ${existingPerson.rows[0].id})`);
          return existingPerson.rows[0].id;
        }
      }

      // Strategy 3: Reverse check - if database has abbreviated name and JSON has full name
      // Check if any existing person has abbreviated last name that matches our full name
      if (!isAbbreviated && lastName.length > 1) {
        const firstLetter = lastName.charAt(0).toUpperCase();
        existingPerson = await client.query(
          'SELECT id, first_name, last_name FROM persons WHERE LOWER(first_name) = LOWER($1) AND (last_name = $2 OR last_name = $3)',
          [firstName, firstLetter + '.', firstLetter]
        );

        if (existingPerson.rows.length > 0) {
          console.log(`Found reverse abbreviated match: ${firstName} ${lastName} → ${existingPerson.rows[0].first_name} ${existingPerson.rows[0].last_name} (ID: ${existingPerson.rows[0].id})`);
          return existingPerson.rows[0].id;
        }
      }

      // Strategy 4: Fuzzy matching for common name variations
      // Handle cases like "Anapoliotakis" vs "Anapoliotakis G." where first names might be swapped
      const nameParts = firstName.split(' ');
      if (nameParts.length > 1) {
        // Try different combinations of name parts
        for (let i = 0; i < nameParts.length; i++) {
          const altFirstName = nameParts[i];
          const altLastName = nameParts.filter((_, idx) => idx !== i).join(' ') + (lastName !== altFirstName ? ' ' + lastName : '');

          existingPerson = await client.query(
            'SELECT id, first_name, last_name FROM persons WHERE LOWER(first_name) = LOWER($1) AND LOWER(last_name) = LOWER($2)',
            [altFirstName, altLastName.trim()]
          );

          if (existingPerson.rows.length > 0) {
            console.log(`Found name variation match: ${firstName} ${lastName} → ${existingPerson.rows[0].first_name} ${existingPerson.rows[0].last_name} (ID: ${existingPerson.rows[0].id})`);
            return existingPerson.rows[0].id;
          }
        }
      }

      // No match found - create new person
      const newPerson = await client.query(
        'INSERT INTO persons (first_name, last_name, nationality) VALUES ($1, $2, $3) RETURNING id',
        [firstName, lastName, nationality]
      );

      console.log(`Created new person: ${firstName} ${lastName} (ID: ${newPerson.rows[0].id})`);
      return newPerson.rows[0].id;
    };

    // 1. Create or find rally
    const rallyInfo = ewrcData.rally_info;
    // Parse date format "10.5.2025" to "2025-05-10"
    const parseDate = (dateStr) => {
      const [day, month, year] = dateStr.split('.');
      return new Date(`${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`);
    };

    const startDate = parseDate(rallyInfo.start_date);
    const endDate = parseDate(rallyInfo.end_date);

    // Check if rally already exists
    const existingRally = await client.query(
      'SELECT id FROM rallies WHERE name = $1 AND start_date::date = $2::date',
      [rallyInfo.name, startDate]
    );

    let rallyId;
    if (existingRally.rows.length > 0) {
      rallyId = existingRally.rows[0].id;
      console.log(`Found existing rally: ${rallyInfo.name} (ID: ${rallyId})`);
      // Update existing rally
      await client.query(`
        UPDATE rallies SET
          country = $2, end_date = $3, status = $4, surface = $5
        WHERE id = $1
      `, [
        rallyId,
        ewrcData.results[0]?.country || 'Unknown',
        endDate,
        'finished',
        'asphalt'
      ]);
    } else {
      // Create new rally
      const rallyResult = await client.query(`
        INSERT INTO rallies (name, country, start_date, end_date, status, surface)
        VALUES ($1, $2, $3, $4, $5, $6)
        RETURNING id
      `, [
        rallyInfo.name,
        ewrcData.results[0]?.country || 'Unknown',
        startDate,
        endDate,
        'finished',
        'asphalt'
      ]);
      rallyId = rallyResult.rows[0].id;
      console.log(`Created new rally: ${rallyInfo.name} (ID: ${rallyId})`);
    }

    // Process championships if available
    if (rallyInfo.championship && Array.isArray(rallyInfo.championship)) {
      console.log(`Processing ${rallyInfo.championship.length} championships for rally`);

      // Remove existing championship associations for this rally
      await client.query('DELETE FROM championship_events WHERE rally_id = $1', [rallyId]);

      // First, let's see what championships exist in the database
      const allChampionships = await client.query('SELECT id, name, year, type FROM championships ORDER BY name');
      console.log('📋 Available championships in database:');
      allChampionships.rows.forEach(champ => {
        console.log(`  - "${champ.name}" (${champ.year}) [${champ.type}] ID: ${champ.id}`);
      });

      let championshipsLinked = 0;
      for (const championshipName of rallyInfo.championship) {
        try {
          console.log(`🔍 Looking for championship: "${championshipName}"`);

          // Try multiple matching strategies
          let existingChampionship = null;

          // Strategy 1: Exact match (case-insensitive)
          existingChampionship = await client.query(
            'SELECT id, name FROM championships WHERE LOWER(name) = LOWER($1)',
            [championshipName.trim()]
          );

          // Strategy 2: If not found and contains (GR), try without (GR)
          if (existingChampionship.rows.length === 0 && championshipName.includes('(GR)')) {
            const nameWithoutGR = championshipName.replace(/\s*\(GR\)\s*/gi, '').trim();
            console.log(`   Trying without (GR): "${nameWithoutGR}"`);

            existingChampionship = await client.query(
              'SELECT id, name FROM championships WHERE LOWER(name) = LOWER($1)',
              [nameWithoutGR]
            );
          }

          // Strategy 3: If still not found, try partial match (contains the main word)
          if (existingChampionship.rows.length === 0) {
            const mainWord = championshipName.replace(/\s*\(GR\)\s*/gi, '').split(' ')[0].trim();
            if (mainWord.length > 2) { // Only try if main word is meaningful
              console.log(`   Trying partial match with: "${mainWord}"`);

              existingChampionship = await client.query(
                'SELECT id, name FROM championships WHERE LOWER(name) LIKE LOWER($1)',
                [`%${mainWord}%`]
              );
            }
          }

          console.log(`📊 Query result for "${championshipName}": ${existingChampionship.rows.length} rows found`);

          if (existingChampionship.rows.length > 0) {
            const championshipId = existingChampionship.rows[0].id;
            const actualName = existingChampionship.rows[0].name;

            console.log(`✅ Found championship: "${championshipName}" → "${actualName}" (ID: ${championshipId})`);

            // Add rally to championship
            await client.query(
              'INSERT INTO championship_events (rally_id, championship_id, coefficient) VALUES ($1, $2, $3) ON CONFLICT (championship_id, rally_id) DO NOTHING',
              [rallyId, championshipId, 1.0]
            );

            championshipsLinked++;
            console.log(`✓ Added rally to championship: ${championshipName} → ${actualName}`);
          } else {
            console.log(`⚠ Championship not found in database: "${championshipName}"`);
            console.log(`   Tried: exact match, without (GR), and partial match`);
          }
        } catch (error) {
          console.error(`✗ Error linking championship ${championshipName}:`, error.message);
        }
      }
      console.log(`Successfully linked ${championshipsLinked} out of ${rallyInfo.championship.length} championships`);
    }

    // Helper function to parse stage date and time
    const parseStageDateTime = (stageDate, stageTime, rallyYear) => {
      if (!stageDate || !stageTime) {
        console.log(`Missing stage date/time, using rally start date: ${startDate}`);
        return startDate; // Fallback to rally start date
      }

      try {
        // Parse date like "25.5." and combine with rally year
        // First split by dots, then clean up the parts
        const dateParts = stageDate.split('.').filter(part => part.length > 0);
        if (dateParts.length >= 2) {
          const day = parseInt(dateParts[0]);
          const month = parseInt(dateParts[1]);

          // Parse time like "09:23"
          const timeParts = stageTime.split(':');
          const hours = parseInt(timeParts[0]);
          const minutes = parseInt(timeParts[1]);

          // Create date object
          const stageDateTime = new Date(rallyYear, month - 1, day, hours, minutes);
          const isoString = stageDateTime.toISOString();
          console.log(`Parsed stage date/time: ${stageDate} ${stageTime} → ${isoString}`);
          return isoString;
        }
      } catch (error) {
        console.warn(`Failed to parse stage date/time: ${stageDate} ${stageTime}`, error);
      }

      console.log(`Failed to parse, using rally start date: ${startDate}`);
      return startDate; // Fallback to rally start date
    };

    // 2. Process stages
    const stageMap = new Map();
    const stagesByNumber = new Map();

    // Group results by stage to get stage info with all new fields
    for (const result of ewrcData.results) {
      if (!stagesByNumber.has(result.stage_number)) {
        stagesByNumber.set(result.stage_number, {
          number: result.stage_number,
          name: result.stage_name,
          distance_km: result.stage_distance_km || 10.0,
          date: result.stage_date,
          start_time: result.stage_start_time
        });
        console.log(`Found stage ${result.stage_number}: ${result.stage_name} (${result.stage_distance_km}km) on ${result.stage_date} at ${result.stage_start_time}`);
      }
    }

    // Create stages
    for (const stageInfo of stagesByNumber.values()) {
      // Parse stage start time
      const stageStartTime = parseStageDateTime(
        stageInfo.date,
        stageInfo.start_time,
        new Date(startDate).getFullYear()
      );

      // Check if stage already exists
      const existingStage = await client.query(
        'SELECT id FROM stages WHERE rally_id = $1 AND number = $2',
        [rallyId, stageInfo.number]
      );

      let stageResult;
      if (existingStage.rows.length > 0) {
        // Update existing stage with new data
        stageResult = await client.query(`
          UPDATE stages SET
            name = $2,
            length = $3,
            start_time = $4
          WHERE rally_id = $1 AND number = $5
          RETURNING id
        `, [rallyId, stageInfo.name, stageInfo.distance_km, stageStartTime, stageInfo.number]);
        console.log(`Updated existing stage ${stageInfo.number}: ${stageInfo.name} (${stageInfo.distance_km}km)`);
      } else {
        // Create new stage
        stageResult = await client.query(`
          INSERT INTO stages (rally_id, name, number, length, surface, start_time, status)
          VALUES ($1, $2, $3, $4, $5, $6, $7)
          RETURNING id
        `, [
          rallyId,
          stageInfo.name,
          stageInfo.number,
          stageInfo.distance_km,
          'gravel', // Default surface for Greek rallies
          stageStartTime,
          'finished'
        ]);
        console.log(`Created new stage ${stageInfo.number}: ${stageInfo.name} (${stageInfo.distance_km}km)`);
      }

      stageMap.set(stageInfo.number, stageResult.rows[0].id);
    }

    // 3. Process entries and results
    const entryMap = new Map();
    const entryStatusMap = new Map(); // Track overall status for each entry
    const entryChampionshipMap = new Map(); // Track championship associations per entry

    // First pass: collect all results for each entry to determine overall status and championships
    const resultsByEntry = new Map();
    for (const result of ewrcData.results) {
      const entryNumber = parseInt(result.entry_number.replace('#', ''));
      if (!resultsByEntry.has(entryNumber)) {
        resultsByEntry.set(entryNumber, []);
      }
      resultsByEntry.get(entryNumber).push(result);

      // Store championship associations for this entry (from first occurrence)
      if (result.championship && Array.isArray(result.championship) && !entryChampionshipMap.has(entryNumber)) {
        entryChampionshipMap.set(entryNumber, result.championship);
        console.log(`Entry #${entryNumber} championships:`, result.championship);
      }
    }

    // Determine overall status for each entry
    for (const [entryNumber, results] of resultsByEntry.entries()) {
      const allActive = results.every(r => r.active);
      const allInactive = results.every(r => !r.active);

      let overallStatus;
      if (allActive) {
        overallStatus = 'finished'; // Finished - completed all stages
      } else if (allInactive) {
        overallStatus = 'dns'; // Did not start - inactive in all stages
      } else {
        overallStatus = 'dnf'; // Did not finish - started but failed to complete at least one stage
      }

      entryStatusMap.set(entryNumber, overallStatus);
    }

    for (const result of ewrcData.results) {
      const entryNumber = parseInt(result.entry_number.replace('#', ''));

      if (!entryMap.has(entryNumber)) {
        // Parse driver and codriver names
        const { driver, codriver } = parseDriverCodriver(result.driver_codriver);

        // Find or create persons (WITH DUPLICATE PREVENTION)
        const driverId = await findOrCreatePerson(
          driver.firstName,
          driver.lastName,
          result.country
        );
        const codriverId = await findOrCreatePerson(
          codriver.firstName,
          codriver.lastName,
          result.country
        );

        // Ensure they exist in drivers/codrivers tables
        await client.query(
          'INSERT INTO drivers (id) VALUES ($1) ON CONFLICT (id) DO NOTHING',
          [driverId]
        );
        await client.query(
          'INSERT INTO codrivers (id) VALUES ($1) ON CONFLICT (id) DO NOTHING',
          [codriverId]
        );

        // Check if entry already exists
        const existingEntry = await client.query(
          'SELECT id FROM entries WHERE rally_id = $1 AND number = $2',
          [rallyId, entryNumber]
        );

        // Use the overall status we calculated for this entry
        const entryStatus = entryStatusMap.get(entryNumber) || 'entered';

        let entryResult;
        if (existingEntry.rows.length > 0) {
          // Update existing entry
          entryResult = await client.query(`
            UPDATE entries SET
              driver_id = $2, codriver_id = $3, car = $4, class = $5, status = $6
            WHERE rally_id = $1 AND number = $7
            RETURNING id
          `, [
            rallyId,
            driverId,
            codriverId,
            result.car,
            result.group.join(', '), // Convert array to comma-separated string
            entryStatus,
            entryNumber
          ]);
          console.log(`Updated existing entry #${entryNumber} (status: ${entryStatus})`);
        } else {
          // Create new entry
          entryResult = await client.query(`
            INSERT INTO entries (rally_id, driver_id, codriver_id, car, number, class, status)
            VALUES ($1, $2, $3, $4, $5, $6, $7)
            RETURNING id
          `, [
            rallyId,
            driverId,
            codriverId,
            result.car,
            entryNumber,
            result.group.join(', '), // Convert array to comma-separated string
            entryStatus
          ]);
          console.log(`Created new entry #${entryNumber} (status: ${entryStatus})`);
        }

        entryMap.set(entryNumber, entryResult.rows[0].id);

        // Store championship associations for this entry
        const entryId = entryResult.rows[0].id;
        const championshipNames = entryChampionshipMap.get(entryNumber);

        if (championshipNames && Array.isArray(championshipNames)) {
          console.log(`Processing championships for entry #${entryNumber}:`, championshipNames);

          // Clear existing championship associations for this entry
          await client.query('DELETE FROM entry_championships WHERE entry_id = $1', [entryId]);

          for (const championshipName of championshipNames) {
            try {
              // Find championship in database using the same logic as rally championship linking
              let existingChampionship = await client.query(
                'SELECT id, name FROM championships WHERE LOWER(name) = LOWER($1)',
                [championshipName.trim()]
              );

              // If not found, try without (GR) suffix
              if (existingChampionship.rows.length === 0 && championshipName.includes('(GR)')) {
                const nameWithoutGR = championshipName.replace(/\s*\(GR\)\s*/gi, '').trim();
                existingChampionship = await client.query(
                  'SELECT id, name FROM championships WHERE LOWER(name) = LOWER($1)',
                  [nameWithoutGR]
                );
              }

              // If still not found, try partial match
              if (existingChampionship.rows.length === 0) {
                const mainWord = championshipName.replace(/\s*\(GR\)\s*/gi, '').split(' ')[0].trim();
                if (mainWord.length > 2) {
                  existingChampionship = await client.query(
                    'SELECT id, name FROM championships WHERE LOWER(name) LIKE LOWER($1)',
                    [`%${mainWord}%`]
                  );
                }
              }

              if (existingChampionship.rows.length > 0) {
                const championshipId = existingChampionship.rows[0].id;
                const actualName = existingChampionship.rows[0].name;

                // Insert entry-championship association
                await client.query(
                  'INSERT INTO entry_championships (entry_id, championship_id) VALUES ($1, $2) ON CONFLICT (entry_id, championship_id) DO NOTHING',
                  [entryId, championshipId]
                );

                console.log(`✓ Associated entry #${entryNumber} with championship: "${championshipName}" → "${actualName}"`);
              } else {
                console.log(`⚠ Championship not found for entry #${entryNumber}: "${championshipName}"`);
              }
            } catch (error) {
              console.error(`✗ Error associating entry #${entryNumber} with championship ${championshipName}:`, error.message);
            }
          }
        }
      }

      // Create stage result for all entries (active and inactive) to track participation
      // This includes: times > 0, nominal times, and inactive entries (DNS/DNF tracking)
      if (result.stage_time_ms > 0 || result.nominal_time || result.active === false) {
        const stageId = stageMap.get(result.stage_number);
        const entryId = entryMap.get(entryNumber);
        const timeInSeconds = result.stage_time_ms > 0 ? result.stage_time_ms / 1000 : 0;

        // Check if result already exists
        const existingResult = await client.query(
          'SELECT id FROM results WHERE stage_id = $1 AND entry_id = $2',
          [stageId, entryId]
        );

        if (existingResult.rows.length > 0) {
          // Update existing result
          await client.query(`
            UPDATE results SET time = $3, nominal_time = $4, super_rally = $5, is_active = $6
            WHERE stage_id = $1 AND entry_id = $2
          `, [stageId, entryId, timeInSeconds, result.nominal_time || false, result.super_rally || false, result.active || false]);
          console.log(`Updated result for entry #${entryNumber} on stage ${result.stage_number} (active: ${result.active}, nominal: ${result.nominal_time}, super_rally: ${result.super_rally})`);
        } else {
          // Create new result
          await client.query(`
            INSERT INTO results (rally_id, stage_id, entry_id, time, nominal_time, super_rally, is_active)
            VALUES ($1, $2, $3, $4, $5, $6, $7)
          `, [rallyId, stageId, entryId, timeInSeconds, result.nominal_time || false, result.super_rally || false, result.active || false]);
          console.log(`Created result for entry #${entryNumber} on stage ${result.stage_number} (active: ${result.active}, nominal: ${result.nominal_time}, super_rally: ${result.super_rally})`);
        }

        // Add penalties if they exist and are greater than 0
        if (result.penalties_ms && result.penalties_ms > 0) {
          const penaltyTimeInSeconds = result.penalties_ms / 1000;

          // Check if penalty already exists for this specific stage and entry
          const existingPenalty = await client.query(
            'SELECT id FROM penalties WHERE rally_id = $1 AND stage_id = $2 AND entry_id = $3',
            [rallyId, stageId, entryId]
          );

          if (existingPenalty.rows.length > 0) {
            // Update existing penalty
            await client.query(`
              UPDATE penalties SET time = $4, reason = $5, updated_at = NOW()
              WHERE rally_id = $1 AND stage_id = $2 AND entry_id = $3
            `, [rallyId, stageId, entryId, penaltyTimeInSeconds, 'Time penalty']);
            console.log(`Updated penalty for entry #${entryNumber} on stage ${result.stage_number}: ${penaltyTimeInSeconds}s`);
          } else {
            // Create new penalty only for this specific stage
            await client.query(`
              INSERT INTO penalties (rally_id, stage_id, entry_id, time, reason)
              VALUES ($1, $2, $3, $4, $5)
            `, [rallyId, stageId, entryId, penaltyTimeInSeconds, 'Time penalty']);
            console.log(`Created penalty for entry #${entryNumber} on stage ${result.stage_number}: ${penaltyTimeInSeconds}s`);
          }
        } else {
          // If no penalty in JSON, remove any existing penalty for this stage/entry combination
          const deletedPenalty = await client.query(
            'DELETE FROM penalties WHERE rally_id = $1 AND stage_id = $2 AND entry_id = $3 RETURNING id',
            [rallyId, stageId, entryId]
          );

          if (deletedPenalty.rows.length > 0) {
            console.log(`Removed penalty for entry #${entryNumber} on stage ${result.stage_number} (no penalty in JSON)`);
          }
        }
      }
    }

    await client.query('COMMIT');

    // Clean up uploaded file
    fs.unlinkSync(req.file.path);

    // Get championship count for response
    const championshipCount = await client.query(
      'SELECT COUNT(*) as count FROM championship_events WHERE rally_id = $1',
      [rallyId]
    );

    // Get entry-championship associations count
    const entryChampionshipCount = await client.query(
      `SELECT COUNT(*) as count FROM entry_championships ec
       JOIN entries e ON ec.entry_id = e.id
       WHERE e.rally_id = $1`,
      [rallyId]
    );

    // Get entry status breakdown for response
    const entryStatusBreakdown = await client.query(
      'SELECT status, COUNT(*) as count FROM entries WHERE rally_id = $1 GROUP BY status',
      [rallyId]
    );

    const statusCounts = {};
    entryStatusBreakdown.rows.forEach(row => {
      statusCounts[row.status] = parseInt(row.count);
    });

    res.json({
      message: 'EWRC data imported successfully',
      rally_id: rallyId,
      stages_imported: stageMap.size,
      entries_imported: entryMap.size,
      results_imported: ewrcData.results.filter(r => r.stage_time_ms > 0 || r.nominal_time || r.active === false).length,
      championships_linked: parseInt(championshipCount.rows[0].count),
      entry_championship_associations: parseInt(entryChampionshipCount.rows[0].count),
      entry_status_breakdown: statusCounts
    });

  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Import error:', error);

    // Clean up uploaded file on error
    if (req.file) {
      const fs = await import('fs');
      try {
        fs.unlinkSync(req.file.path);
      } catch (cleanupError) {
        console.error('File cleanup error:', cleanupError);
      }
    }

    res.status(500).json({
      message: 'Failed to import EWRC data',
      error: error.message
    });
  } finally {
    client.release();
  }
});

// Debug endpoint to test championship functionality
router.post('/debug-championships', verifyToken, verifyAdmin, async (req, res) => {
  try {
    const { championshipNames } = req.body;

    console.log('=== CHAMPIONSHIP DEBUG ===');
    console.log('Championship names to test:', championshipNames || ['Greece', 'Historic (GR)', 'Rally3 (GR)', 'Historic Gravel Cup (GR)']);

    const namesToTest = championshipNames || ['Greece', 'Historic (GR)', 'Rally3 (GR)', 'Historic Gravel Cup (GR)'];
    const results = [];

    for (const championshipName of namesToTest) {
      try {
        console.log(`Testing: "${championshipName}"`);

        const testResult = {
          name: championshipName,
          strategies: {}
        };

        // Strategy 1: Exact match (case-insensitive)
        const exactMatch = await pool.query(
          'SELECT id, name, year, type FROM championships WHERE LOWER(name) = LOWER($1)',
          [championshipName.trim()]
        );
        testResult.strategies.exact_match = {
          query: championshipName.trim(),
          results: exactMatch.rows
        };

        // Strategy 2: Without (GR) if applicable
        if (championshipName.includes('(GR)')) {
          const nameWithoutGR = championshipName.replace(/\s*\(GR\)\s*/gi, '').trim();
          const withoutGRMatch = await pool.query(
            'SELECT id, name, year, type FROM championships WHERE LOWER(name) = LOWER($1)',
            [nameWithoutGR]
          );
          testResult.strategies.without_gr = {
            query: nameWithoutGR,
            results: withoutGRMatch.rows
          };
        }

        // Strategy 3: Partial match
        const mainWord = championshipName.replace(/\s*\(GR\)\s*/gi, '').split(' ')[0].trim();
        if (mainWord.length > 2) {
          const partialMatch = await pool.query(
            'SELECT id, name, year, type FROM championships WHERE LOWER(name) LIKE LOWER($1)',
            [`%${mainWord}%`]
          );
          testResult.strategies.partial_match = {
            query: `%${mainWord}%`,
            results: partialMatch.rows
          };
        }

        // Determine overall result
        const totalMatches = Object.values(testResult.strategies).reduce((sum, strategy) => sum + strategy.results.length, 0);
        testResult.found = totalMatches > 0;
        testResult.total_matches = totalMatches;

        results.push(testResult);

      } catch (error) {
        results.push({
          name: championshipName,
          found: false,
          error: error.message
        });
      }
    }

    // Also list all available championships
    const allChampionships = await pool.query('SELECT id, name, year, type FROM championships ORDER BY name');

    res.json({
      test_results: results,
      total_championships_in_db: allChampionships.rows.length,
      available_championships: allChampionships.rows
    });

  } catch (error) {
    console.error('Championship debug error:', error);
    res.status(500).json({ error: error.message });
  }
});

// Debug endpoint to test stage date parsing
router.post('/debug-stage-parsing', verifyToken, verifyAdmin, async (req, res) => {
  try {
    const { stageDate, stageTime, rallyYear } = req.body;

    console.log('=== STAGE PARSING DEBUG ===');
    console.log('Input:', { stageDate, stageTime, rallyYear });

    const parseStageDateTime = (stageDate, stageTime, rallyYear) => {
      if (!stageDate || !stageTime) {
        return { error: 'Missing stage date or time' };
      }

      try {
        // Parse date like "25.5." and combine with rally year
        // First split by dots, then clean up the parts
        const dateParts = stageDate.split('.').filter(part => part.length > 0);
        console.log('Date parts after split and filter:', dateParts);

        if (dateParts.length >= 2) {
          const day = parseInt(dateParts[0]);
          const month = parseInt(dateParts[1]);

          console.log('Parsed day/month:', { day, month });

          // Parse time like "09:23"
          const timeParts = stageTime.split(':');
          const hours = parseInt(timeParts[0]);
          const minutes = parseInt(timeParts[1]);

          console.log('Parsed time:', { hours, minutes });

          // Create date object
          const stageDateTime = new Date(rallyYear, month - 1, day, hours, minutes);
          const isoString = stageDateTime.toISOString();

          return {
            success: true,
            parsed_date: isoString,
            local_date: stageDateTime.toString(),
            components: { day, month, hours, minutes, year: rallyYear }
          };
        } else {
          return { error: 'Invalid date format - not enough parts' };
        }
      } catch (error) {
        return { error: error.message };
      }
    };

    const result = parseStageDateTime(stageDate, stageTime, rallyYear);

    res.json({
      input: { stageDate, stageTime, rallyYear },
      result: result
    });

  } catch (error) {
    console.error('Stage parsing debug error:', error);
    res.status(500).json({ error: error.message });
  }
});

export default router;
