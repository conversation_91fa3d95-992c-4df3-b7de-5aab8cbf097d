# API Documentation

This document describes the REST API endpoints for the Stagetime Rally Management System.

## Base URL

```
http://localhost:3001/api
```

## Authentication

Most endpoints require authentication using JWT tokens. Include the token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

### Authentication Endpoints

#### POST /auth/login
Login with email and password.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password"
}
```

**Response:**
```json
{
  "token": "jwt-token-here",
  "user": {
    "id": "uuid",
    "email": "<EMAIL>",
    "role": "admin"
  }
}
```

## Rally Management

### GET /rallies
Get all rallies.

**Query Parameters:**
- `status` - Filter by rally status (upcoming, running, finished)
- `year` - Filter by year

**Response:**
```json
[
  {
    "id": "uuid",
    "name": "Rally Name",
    "location": "Location",
    "start_date": "2025-01-01",
    "end_date": "2025-01-03",
    "status": "upcoming"
  }
]
```

### POST /rallies
Create a new rally. (Admin only)

**Request Body:**
```json
{
  "name": "Rally Name",
  "location": "Location",
  "start_date": "2025-01-01",
  "end_date": "2025-01-03",
  "status": "upcoming",
  "description": "Rally description"
}
```

### GET /rallies/:id
Get rally details by ID.

### PUT /rallies/:id
Update rally. (Admin only)

### DELETE /rallies/:id
Delete rally. (Admin only)

## Entries Management

### GET /entries
Get all entries.

**Query Parameters:**
- `rally_id` - Filter by rally ID
- `status` - Filter by entry status

### POST /entries
Create a new entry. (Admin only)

**Request Body:**
```json
{
  "rally_id": "uuid",
  "driver_id": "uuid",
  "codriver_id": "uuid",
  "team_id": "uuid",
  "car": "Car Model",
  "number": 1,
  "class": "C1, F2",
  "status": "entered"
}
```

### GET /entries/:id
Get entry details by ID.

### PUT /entries/:id
Update entry. (Admin only)

### DELETE /entries/:id
Delete entry. (Admin only)

## Results Management

### GET /stage-results
Get stage results.

**Query Parameters:**
- `rally_id` - Filter by rally ID
- `stage_id` - Filter by stage ID
- `entry_id` - Filter by entry ID

### POST /stage-results
Add stage result. (Admin only)

**Request Body:**
```json
{
  "rally_id": "uuid",
  "stage_id": "uuid",
  "entry_id": "uuid",
  "time": 123.456,
  "is_active": true,
  "nominal_time": false,
  "super_rally": false
}
```

## Championship System

### GET /championships
Get all championships.

### GET /championship-results/:championshipId
Get championship standings.

**Query Parameters:**
- `rally_id` - Filter by specific rally

### GET /championship-results/:championshipId/rallies/:rallyId
Get championship results for specific rally.

## Person Management

### GET /persons
Get all persons.

**Query Parameters:**
- `search` - Search by name
- `nationality` - Filter by nationality

### POST /persons
Create a new person. (Admin only)

**Request Body:**
```json
{
  "first_name": "John",
  "last_name": "Doe",
  "nationality": "GR",
  "date_of_birth": "1990-01-01",
  "photo_url": "https://example.com/photo.jpg",
  "bio": "Driver biography"
}
```

### GET /persons/:id
Get person details by ID.

### PUT /persons/:id
Update person. (Admin only)

### DELETE /persons/:id
Delete person. (Admin only)

## Driver & Codriver Management

### GET /drivers
Get all drivers.

### POST /drivers
Create driver (person + driver role). (Admin only)

### GET /codrivers
Get all codrivers.

### POST /codrivers
Create codriver (person + codriver role). (Admin only)

## Stage Management

### GET /stages
Get all stages.

**Query Parameters:**
- `rally_id` - Filter by rally ID

### POST /stages
Create a new stage. (Admin only)

**Request Body:**
```json
{
  "rally_id": "uuid",
  "name": "Stage Name",
  "number": 1,
  "length": 12.5,
  "surface": "Gravel",
  "start_time": "2025-01-01T09:00:00Z",
  "status": "upcoming",
  "is_power_stage": false,
  "is_super_special": false,
  "leg_number": 1,
  "day_number": 1
}
```

## Error Responses

All endpoints may return the following error responses:

### 400 Bad Request
```json
{
  "message": "Invalid request data"
}
```

### 401 Unauthorized
```json
{
  "message": "Authentication required"
}
```

### 403 Forbidden
```json
{
  "message": "Insufficient permissions"
}
```

### 404 Not Found
```json
{
  "message": "Resource not found"
}
```

### 500 Internal Server Error
```json
{
  "message": "Internal server error"
}
```

## Rate Limiting

API requests are rate-limited to prevent abuse. Current limits:
- 100 requests per minute for authenticated users
- 20 requests per minute for unauthenticated users

## Data Formats

### Dates
All dates are in ISO 8601 format: `YYYY-MM-DD`

### Times
All times are in ISO 8601 format with timezone: `YYYY-MM-DDTHH:mm:ssZ`

### Stage Times
Stage times are in seconds with millisecond precision (e.g., 123.456)

### Nationalities
Nationality codes follow ISO 3166-1 alpha-2 standard (e.g., "GR", "FR", "DE")
