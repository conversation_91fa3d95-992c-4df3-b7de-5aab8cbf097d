import React from 'react';
import { Link } from 'react-router-dom';
import { Trophy, Flag, Activity } from 'lucide-react';
import { Driver } from '../../types';
import { getCountryCode } from '../../utils/countryUtils';

interface DriverCardProps {
  driver: Driver;
  compact?: boolean;
}

const DriverCard: React.FC<DriverCardProps> = ({ driver, compact = false }) => {
  if (compact) {
    return (
      <Link to={`/drivers/${driver.id}`} className="block px-4 py-3 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
        <div className="flex items-center">
          <div className="flex-shrink-0 mr-3">
            <div className="relative">
              <img
                src={driver.image || driver.photo_url || `https://ui-avatars.com/api/?name=${driver.name}&background=random`}
                alt={driver.name}
                className="w-12 h-12 rounded-full object-cover"
              />
              <span
                className={`fi fi-${driver.flagCode || getCountryCode(driver.nationality)} absolute -bottom-1 -right-1 w-6 h-4 rounded-sm border border-white dark:border-gray-800`}
                style={{ display: 'inline-block' }}
              ></span>
            </div>
          </div>
          <div className="min-w-0 flex-1">
            <h4 className="text-sm font-medium text-gray-900 dark:text-white truncate">{driver.name}</h4>
            <div className="flex items-center mt-1">
              <Trophy className="w-3 h-3 text-yellow-500 mr-1" />
              <span className="text-xs text-gray-600 dark:text-gray-400">{driver.totalWins || 0} Wins</span>
              <span className="mx-1.5 text-gray-500">•</span>
              <span className="text-xs text-gray-600 dark:text-gray-400">{driver.totalRallies || 0} Rallies</span>
            </div>
          </div>
          <div className="flex-shrink-0">
            <div className="bg-gray-100 dark:bg-gray-700 rounded-full px-2.5 py-1 text-xs font-medium">
              {driver.careerPoints || 0} pts
            </div>
          </div>
        </div>
      </Link>
    );
  }

  return (
    <Link to={`/drivers/${driver.id}`} className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden transition-transform duration-300 hover:shadow-lg hover:-translate-y-1">
      <div className="relative h-48">
        <img
          src={driver.image || driver.photo_url || `https://ui-avatars.com/api/?name=${driver.name}&background=random&size=400`}
          alt={driver.name}
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-gray-900 to-transparent"></div>

        <div className="absolute bottom-0 left-0 right-0 p-4">
          <div className="flex items-center">
            <span
              className={`fi fi-${driver.flagCode || getCountryCode(driver.nationality)} w-6 h-4 rounded mr-2 border border-white/30`}
              style={{ display: 'inline-block' }}
            ></span>
            <h3 className="text-white font-bold text-lg">{driver.name || `${driver.first_name} ${driver.last_name}`}</h3>
          </div>
        </div>
      </div>

      <div className="p-4">
        <div className="grid grid-cols-3 gap-4 text-center mb-4">
          <div>
            <p className="text-gray-500 dark:text-gray-400 text-xs">Rallies</p>
            <p className="font-semibold text-gray-900 dark:text-white">{driver.totalRallies || 0}</p>
          </div>
          <div>
            <p className="text-gray-500 dark:text-gray-400 text-xs">Wins</p>
            <p className="font-semibold text-gray-900 dark:text-white">{driver.totalWins || 0}</p>
          </div>
          <div>
            <p className="text-gray-500 dark:text-gray-400 text-xs">Podiums</p>
            <p className="font-semibold text-gray-900 dark:text-white">{driver.totalPodiums || 0}</p>
          </div>
        </div>

        <div className="flex justify-between items-center">
          <div className="flex items-center">
            <Activity className="w-4 h-4 text-blue-500 mr-1" />
            <span className="text-sm text-gray-700 dark:text-gray-300">Career Points</span>
          </div>
          <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-100 px-2 py-1 rounded text-sm font-medium">
            {driver.careerPoints || 0}
          </span>
        </div>
      </div>
    </Link>
  );
};

export default DriverCard;