import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Clock, Edit, Plus, Trash } from 'lucide-react';
import { formatTime, formatPenalty, calculateTotalTime } from '../../utils/timeUtils.js';

interface StageResult {
  id: string;
  stage_id: string;
  entry_id: string;
  time: number;
  nominal_time?: boolean;
  super_rally?: boolean;
  is_active?: boolean;
  penalty_time?: number;
  penalty_reason?: string;
  status: string;
  stage_name?: string;
  entry_number?: number;
  driver_name?: string;
  codriver_name?: string;
}

interface Stage {
  id: string;
  name: string;
  number: number;
}

interface StageResultsManagerProps {
  rallyId: string;
}

const StageResultsManager: React.FC<StageResultsManagerProps> = ({ rallyId }) => {
  const [stageResults, setStageResults] = useState<StageResult[]>([]);
  const [stages, setStages] = useState<Stage[]>([]);
  const [selectedStage, setSelectedStage] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    fetchStages();
  }, [rallyId]);

  useEffect(() => {
    if (selectedStage) {
      fetchStageResults();
    } else {
      setStageResults([]);
    }
  }, [selectedStage]);

  const fetchStages = async () => {
    setLoading(true);
    try {
      const res = await fetch(`/api/stages?rally_id=${rallyId}`, { credentials: 'include' });
      if (!res.ok) throw new Error('Failed to fetch stages');
      const data = await res.json();
      const stagesData = Array.isArray(data) ? data : data.stages || [];
      setStages(stagesData);

      // Auto-select the first stage if available
      if (stagesData.length > 0 && !selectedStage) {
        setSelectedStage(stagesData[0].id);
      }
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const fetchStageResults = async () => {
    setLoading(true);
    try {
      // Include both stage_id and rally_id in the query to ensure we only get results for this rally
      const res = await fetch(`/api/stageResults?stage_id=${selectedStage}&rally_id=${rallyId}`, { credentials: 'include' });
      if (!res.ok) throw new Error('Failed to fetch stage results');
      const data = await res.json();
      setStageResults(Array.isArray(data) ? data : data.results || []);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: string) => {
    if (!window.confirm('Are you sure you want to delete this stage result?')) {
      return;
    }

    try {
      const res = await fetch(`/api/stageResults/${id}`, {
        method: 'DELETE',
        credentials: 'include',
      });
      if (!res.ok) throw new Error('Failed to delete stage result');
      setStageResults(stageResults.filter(result => result.id !== id));
    } catch (err: any) {
      setError(err.message);
    }
  };



  // Helper function to get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Completed':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'DNF':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'DNS':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold">Stage Results</h2>
        <Link
          to={`/admin/stageResults/add?rally_id=${rallyId}`}
          className="bg-red-600 text-white px-4 py-2 rounded-lg flex items-center hover:bg-red-700 transition-colors"
        >
          <Plus className="w-4 h-4 mr-2" />
          Add Stage Result
        </Link>
      </div>

      {error && (
        <div className="mb-4 p-3 bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-200 rounded">
          {error}
        </div>
      )}

      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          Select Stage
        </label>
        <select
          value={selectedStage}
          onChange={(e) => setSelectedStage(e.target.value)}
          className="w-full md:w-64 px-3 py-2 rounded border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
        >
          <option value="">Select a stage</option>
          {stages.map((stage) => (
            <option key={stage.id} value={stage.id}>
              SS{stage.number}: {stage.name}
            </option>
          ))}
        </select>
      </div>

      {loading ? (
        <div className="p-8 text-center">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-4 border-gray-300 border-t-red-600"></div>
          <p className="mt-2 text-gray-600 dark:text-gray-400">Loading...</p>
        </div>
      ) : !selectedStage ? (
        <div className="p-8 text-center text-gray-500 dark:text-gray-400">
          Select a stage to view results
        </div>
      ) : stageResults.length === 0 ? (
        <div className="p-8 text-center text-gray-500 dark:text-gray-400">
          No results found for this stage. Add a result to get started.
        </div>
      ) : (
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Car #
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Driver
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Codriver
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Time
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Active
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Status
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {stageResults.map((result) => (
                <tr key={result.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    {result.entry_number}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                    {result.driver_name}
                    {result.super_rally && (
                      <span className="ml-1 text-xs text-blue-600 dark:text-blue-400 font-medium">[SR]</span>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    {result.codriver_name}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    <div className="flex flex-col">
                      <span>
                        {(() => {
                          const penaltyTime = result.penalty_time ? parseFloat(result.penalty_time) : 0;
                          const stageTime = parseFloat(result.time);
                          const hasPenalty = penaltyTime > 0 && !isNaN(penaltyTime);
                          const totalTime = hasPenalty ? calculateTotalTime(stageTime, penaltyTime) : stageTime;
                          return formatTime(totalTime);
                        })()}
                        {result.nominal_time && (
                          <span className="ml-1 text-xs text-orange-600 dark:text-orange-400 font-medium">[N]</span>
                        )}
                      </span>
                      {(() => {
                        const penaltyTime = result.penalty_time ? parseFloat(result.penalty_time) : 0;
                        const stageTime = parseFloat(result.time);
                        const hasPenalty = penaltyTime > 0 && !isNaN(penaltyTime);

                        if (hasPenalty) {
                          return (
                            <div className="text-xs text-red-600 dark:text-red-400">
                              {formatTime(stageTime)} {formatPenalty(penaltyTime)}
                              {result.penalty_reason && (
                                <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                  {result.penalty_reason}
                                </div>
                              )}
                            </div>
                          );
                        }
                        return null;
                      })()}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    <span className={`px-2 py-1 rounded text-xs ${result.is_active ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'}`}>
                      {result.is_active ? 'Active' : 'Inactive'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    <span className={`px-2 py-1 rounded text-xs ${getStatusColor(result.status)}`}>
                      {result.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex justify-end space-x-3">
                      <Link
                        to={`/admin/stageResults/${result.id}`}
                        className="text-blue-600 hover:text-blue-900 dark:hover:text-blue-400"
                      >
                        <Edit className="w-4 h-4" />
                      </Link>
                      <button
                        onClick={() => handleDelete(result.id)}
                        className="text-red-600 hover:text-red-900 dark:hover:text-red-400"
                      >
                        <Trash className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default StageResultsManager;
