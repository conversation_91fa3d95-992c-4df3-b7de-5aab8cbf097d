import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useRallyContext } from '../context/RallyContext';
import { Trophy, ChevronRight, Users, Calendar, Flag, Target } from 'lucide-react';
import { Championship } from '../types';

interface ChampionshipWithStats extends Championship {
  rally_count: number;
  driver_count: number;
  latest_rally_date?: string;
  leader_name?: string;
  leader_points?: number;
}

const ChampionshipCard = ({ championship }: { championship: ChampionshipWithStats }) => {

  return (
    <Link
      to={`/championships/${championship.id}`}
      className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow"
    >
      <div className="p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            {championship.logo ? (
              <img
                src={championship.logo}
                alt={championship.name}
                className="h-10 w-auto mr-3"
              />
            ) : (
              <Trophy className="w-8 h-8 text-red-600 mr-3" />
            )}
            <div>
              <h3 className="text-xl font-bold text-gray-900 dark:text-white">
                {championship.name}
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {championship.year} Season
              </p>
            </div>
          </div>
          <ChevronRight className="w-5 h-5 text-gray-400" />
        </div>

        <div className="space-y-3">
          {championship.leader_name && (
            <div className="flex items-center text-sm">
              <Users className="w-4 h-4 text-blue-500 mr-2" />
              <span className="text-gray-700 dark:text-gray-300">
                Championship Leader:
                <span className="font-medium ml-1">{championship.leader_name}</span>
                {championship.leader_points && (
                  <span className="text-gray-500 dark:text-gray-400 ml-1">
                    ({championship.leader_points} pts)
                  </span>
                )}
              </span>
            </div>
          )}

          {championship.latest_rally_date && (
            <div className="flex items-center text-sm">
              <Calendar className="w-4 h-4 text-green-500 mr-2" />
              <span className="text-gray-700 dark:text-gray-300">
                Latest Event:
                <span className="font-medium ml-1">
                  {new Date(championship.latest_rally_date).toLocaleDateString()}
                </span>
              </span>
            </div>
          )}

          <div className="grid grid-cols-2 gap-2 text-sm">
            <div className="flex items-center text-gray-700 dark:text-gray-300">
              <Target className="w-4 h-4 text-red-500 mr-2" />
              <span>{championship.rally_count} Events</span>
            </div>
            <div className="flex items-center text-gray-700 dark:text-gray-300">
              <Users className="w-4 h-4 text-blue-500 mr-2" />
              <span>{championship.driver_count} Drivers</span>
            </div>
          </div>
        </div>
      </div>
    </Link>
  );
};

const ChampionshipsPage = () => {
  const [championships, setChampionships] = useState<ChampionshipWithStats[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchChampionships = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/championships');
        if (!response.ok) throw new Error('Failed to fetch championships');

        const data = await response.json();

        // Fetch stats for each championship
        const championshipsWithStats = await Promise.all(
          data.map(async (championship: Championship) => {
            try {
              // Get calendar and standings
              const [calendarResponse, standingsResponse] = await Promise.all([
                fetch(`/api/championship-results/championships/${championship.id}/calendar`),
                fetch(`/api/championship-results/championships/${championship.id}/standings`)
              ]);

              const calendarData = calendarResponse.ok ? await calendarResponse.json() : [];
              const standingsData = standingsResponse.ok ? await standingsResponse.json() : [];

              const latestRally = calendarData.length > 0
                ? calendarData.sort((a: any, b: any) => new Date(b.start_date).getTime() - new Date(a.start_date).getTime())[0]
                : null;

              const leader = standingsData.length > 0 ? standingsData[0] : null;

              return {
                ...championship,
                rally_count: calendarData.length,
                driver_count: standingsData.length,
                latest_rally_date: latestRally?.start_date,
                leader_name: leader?.driver,
                leader_points: leader?.grand_total_points
              };
            } catch (err) {
              console.error(`Error fetching stats for championship ${championship.id}:`, err);
              return {
                ...championship,
                rally_count: 0,
                driver_count: 0
              };
            }
          })
        );

        setChampionships(championshipsWithStats);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch championships');
        console.error('Error fetching championships:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchChampionships();
  }, []);

  // Group championships by year and type
  const groupedChampionships = championships.reduce((acc, championship) => {
    const year = championship.year.toString();
    if (!acc[year]) acc[year] = [];
    acc[year].push(championship);
    return acc;
  }, {} as Record<string, ChampionshipWithStats[]>);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600 dark:text-gray-400">Loading championships...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-600 dark:text-red-400 text-xl mb-4">
            {error}
          </div>
          <button
            onClick={() => window.location.reload()}
            className="text-blue-600 hover:text-blue-800"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  if (championships.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <h1 className="text-3xl font-bold mb-8 text-gray-900 dark:text-white">Championships</h1>
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-12 text-center">
            <Trophy className="w-16 h-16 mx-auto mb-4 text-gray-400" />
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
              No Championships Found
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              No championships are available at the moment.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">Championships</h1>
          <p className="text-gray-600 dark:text-gray-400">
            Browse rally championships and view standings, calendars, and results.
          </p>
        </div>

        <div className="space-y-8">
          {Object.entries(groupedChampionships)
            .sort(([a], [b]) => parseInt(b) - parseInt(a))
            .map(([year, yearChampionships]) => (
              <div key={year}>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                  {year} Season
                </h2>
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                  {yearChampionships.map(championship => (
                    <ChampionshipCard
                      key={championship.id}
                      championship={championship}
                    />
                  ))}
                </div>
              </div>
            ))}
        </div>
      </div>
    </div>
  );
};

export default ChampionshipsPage;