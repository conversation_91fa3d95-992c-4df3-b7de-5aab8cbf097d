# 🚀 Deployment Guide for StageTime Rally App

## 📋 Pre-Deployment Checklist

### 1. Database Setup
- [ ] PostgreSQL database created
- [ ] Database tables created (run your SQL schema files)
- [ ] Database views updated (`npm run update-views`)
- [ ] Database connection string ready

### 2. Environment Variables
Set these in your hosting platform:

```bash
NODE_ENV=production
PORT=3000
DATABASE_URL=postgresql://username:password@hostname:port/database_name
JWT_SECRET=your_super_secret_jwt_key_here
CLIENT_URL=https://yourdomain.com
```

### 3. Hosting Platform Configuration

**Type:** Node.js
**Command:** `npm start`
**Working Directory:** `/home/<USER>/` (or your app directory)
**Node.js Version:** Default version (18+ recommended)

## 🔧 Platform-Specific Instructions

### For cPanel/Shared Hosting:
1. Upload all files to your hosting directory
2. Set environment variables in the hosting control panel
3. Ensure Node.js is enabled
4. Set the startup file to `server/index.js`

### For VPS/Cloud Hosting:
1. Clone your repository
2. Install dependencies: `npm install`
3. Build the app: `npm run build`
4. Set environment variables
5. Start with PM2: `pm2 start server/index.js --name stagetime`

### For Heroku:
1. Create Heroku app
2. Add PostgreSQL addon
3. Set environment variables in Heroku dashboard
4. Deploy via Git

## 🗄️ Database Setup

1. Create PostgreSQL database
2. Run your schema files to create tables
3. Import initial data if needed
4. Update database views: `npm run update-views`

## 🔒 Security Checklist

- [ ] Strong JWT_SECRET set
- [ ] Database credentials secure
- [ ] CORS configured for your domain
- [ ] SSL/HTTPS enabled
- [ ] Environment variables not in code

## 🧪 Testing Deployment

1. Visit `/diagnostics` to check API endpoints
2. Test login functionality
3. Verify database connections
4. Check all pages load correctly

## 🚨 Troubleshooting

### Common Issues:
- **White page:** Check `/diagnostics` for API issues
- **Database errors:** Verify DATABASE_URL and connection
- **Build fails:** Check Node.js version compatibility
- **API not working:** Verify environment variables

### Logs:
- Check hosting platform logs
- Use `console.log` statements for debugging
- Monitor database connection status
