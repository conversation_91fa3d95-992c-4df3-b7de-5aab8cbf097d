/**
 * Utility functions for handling images
 */

/**
 * Get a valid image URL, using a default if the provided URL is empty or invalid
 *
 * @param url The image URL to check
 * @param defaultImage The default image to use if url is empty
 * @returns A valid image URL
 */
export const getImageUrl = (url: string | null | undefined, defaultImage: string): string => {
  // If URL is empty, null, or undefined, return the default
  if (!url || url.trim() === '') {
    console.log('Using default image because URL is empty:', defaultImage);
    return defaultImage;
  }

  // If URL is from wrc.com, return the default (to avoid external dependencies)
  if (url.includes('wrc.com')) {
    console.log('Using default image because URL is from wrc.com:', defaultImage);
    return defaultImage;
  }

  // If URL doesn't start with http or /, add / to make it relative to the root
  if (!url.startsWith('http') && !url.startsWith('/')) {
    console.log('Adding / prefix to URL:', url);
    return '/' + url;
  }

  // Otherwise return the provided URL
  console.log('Using provided URL:', url);
  return url;
};

// Default images - these should be placed in your public/images folder
export const DEFAULT_RALLY_BANNER = 'https://via.placeholder.com/1400x400/f0f0f0/333333?text=Default+Rally+Banner';
export const DEFAULT_RALLY_LOGO = 'https://via.placeholder.com/200x200/f0f0f0/333333?text=Rally+Logo';
export const DEFAULT_NEWS_IMAGE = 'https://via.placeholder.com/800x400/f0f0f0/333333?text=News+Image';
export const DEFAULT_PERSON_PHOTO = 'https://via.placeholder.com/300x300/f0f0f0/333333?text=Person+Photo';
export const DEFAULT_TEAM_LOGO = 'https://via.placeholder.com/200x200/f0f0f0/333333?text=Team+Logo';
