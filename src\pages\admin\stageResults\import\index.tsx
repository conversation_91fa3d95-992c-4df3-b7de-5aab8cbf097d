import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Upload, Clock, RefreshCw } from 'lucide-react';
import { useAuth } from '../../../../context/AuthContext';

const ImportStageResultsPage: React.FC = () => {
  const [file, setFile] = useState<File | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [result, setResult] = useState<any>(null);
  const [checkNumbers, setCheckNumbers] = useState({ rallyId: '', stageNumber: '', entryNumber: '' });
  const [checkResult, setCheckResult] = useState<any>(null);
  const [checkingNumbers, setCheckingNumbers] = useState(false);
  const [rallies, setRallies] = useState<any[]>([]);
  const [stages, setStages] = useState<any[]>([]);
  const [selectedRally, setSelectedRally] = useState('');
  const [selectedStage, setSelectedStage] = useState('');
  const [overrideStatus, setOverrideStatus] = useState(false);
  const navigate = useNavigate();
  const { user, isAdmin } = useAuth();

  useEffect(() => {
    fetchRallies();
  }, []);

  useEffect(() => {
    if (selectedRally) {
      fetchStages(selectedRally);
      setSelectedStage('');
    } else {
      setStages([]);
    }
  }, [selectedRally]);

  const fetchRallies = async () => {
    try {
      const response = await fetch('/api/rallies', {
        credentials: 'include',
      });
      const data = await response.json();
      setRallies(data);
    } catch (err) {
      console.error('Failed to fetch rallies:', err);
    }
  };

  const fetchStages = async (rallyId: string) => {
    try {
      const response = await fetch(`/api/stages?rally_id=${rallyId}`, {
        credentials: 'include',
      });
      const data = await response.json();
      // Sort stages by number
      const sortedStages = data.sort((a: any, b: any) => a.number - b.number);
      setStages(sortedStages);
    } catch (err) {
      console.error('Failed to fetch stages:', err);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setFile(e.target.files[0]);
    }
  };

  const handleCheckNumbers = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!checkNumbers.rallyId || !checkNumbers.stageNumber || !checkNumbers.entryNumber) {
      setError('Please enter Rally ID, Stage Number, and Entry Number');
      return;
    }

    setCheckingNumbers(true);
    setError('');
    setCheckResult(null);

    try {
      // Check if rally exists
      const rallyResponse = await fetch(`/api/rallies/${checkNumbers.rallyId}`, {
        credentials: 'include',
      });

      const rallyData = await rallyResponse.json();

      if (!rallyResponse.ok) {
        setCheckResult({
          exists: false,
          message: `Rally with ID "${checkNumbers.rallyId}" not found`,
          rallyData: null,
          stageData: null,
          entryData: null
        });
        setCheckingNumbers(false);
        return;
      }

      // Check if stage exists
      const stageResponse = await fetch(`/api/stages?rally_id=${checkNumbers.rallyId}`, {
        credentials: 'include',
      });

      const stagesData = await stageResponse.json();

      if (!stageResponse.ok) {
        setCheckResult({
          exists: false,
          message: `Failed to fetch stages for rally "${rallyData.name}"`,
          rallyData,
          stageData: null,
          entryData: null
        });
        setCheckingNumbers(false);
        return;
      }

      const stageData = stagesData.find((stage: any) =>
        stage.number === parseInt(checkNumbers.stageNumber)
      );

      if (!stageData) {
        setCheckResult({
          exists: false,
          message: `Stage number ${checkNumbers.stageNumber} not found in rally "${rallyData.name}"`,
          rallyData,
          stageData: null,
          entryData: null
        });
        setCheckingNumbers(false);
        return;
      }

      // Check if entry exists
      const entriesResponse = await fetch(`/api/entries?rally_id=${checkNumbers.rallyId}`, {
        credentials: 'include',
      });

      const entriesData = await entriesResponse.json();

      if (!entriesResponse.ok) {
        setCheckResult({
          exists: false,
          message: `Failed to fetch entries for rally "${rallyData.name}"`,
          rallyData,
          stageData,
          entryData: null
        });
        setCheckingNumbers(false);
        return;
      }

      const entryData = entriesData.find((entry: any) =>
        entry.number === parseInt(checkNumbers.entryNumber)
      );

      if (!entryData) {
        setCheckResult({
          exists: false,
          message: `Entry number ${checkNumbers.entryNumber} not found in rally "${rallyData.name}"`,
          rallyData,
          stageData,
          entryData: null
        });
        setCheckingNumbers(false);
        return;
      }

      // Check if result already exists
      const resultResponse = await fetch(`/api/stageResults?stage_id=${stageData.id}&entry_id=${entryData.id}`, {
        credentials: 'include',
      });

      const resultData = await resultResponse.json();

      if (resultResponse.ok && resultData.length > 0) {
        setCheckResult({
          exists: true,
          message: `Stage result already exists for entry #${entryData.number} in stage "${stageData.name}" (will be updated on import)`,
          rallyData,
          stageData,
          entryData,
          resultData: resultData[0]
        });
      } else {
        setCheckResult({
          exists: true,
          message: `Stage and entry are valid. No existing result found (will be created on import)`,
          rallyData,
          stageData,
          entryData,
          resultData: null
        });
      }
    } catch (err: any) {
      setError(err.message || 'Failed to check numbers');
    } finally {
      setCheckingNumbers(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!file) {
      setError('Please select a CSV file');
      return;
    }

    if (!selectedRally) {
      setError('Please select a rally');
      return;
    }

    if (!selectedStage) {
      setError('Please select a stage');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('rally_id', selectedRally);
      formData.append('stage_id', selectedStage);
      formData.append('override_status', overrideStatus.toString());

      const response = await fetch('/api/stageResults/import', {
        method: 'POST',
        credentials: 'include',
        body: formData,
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to import stage results');
      }

      setResult(data);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center mb-6">
        <Clock className="w-6 h-6 text-red-600 mr-2" />
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Import Stage Results</h1>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
          {error}
        </div>
      )}

      {result ? (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">Import Results</h2>
          <div className="grid grid-cols-4 gap-4 mb-4">
            <div className="bg-green-100 dark:bg-green-900 p-4 rounded-lg">
              <p className="text-green-800 dark:text-green-200 text-2xl font-bold">{result.imported}</p>
              <p className="text-green-600 dark:text-green-400">Total imported</p>
            </div>
            <div className="bg-blue-100 dark:bg-blue-900 p-4 rounded-lg">
              <p className="text-blue-800 dark:text-blue-200 text-2xl font-bold">{result.inserted}</p>
              <p className="text-blue-600 dark:text-blue-400">Newly inserted</p>
            </div>
            <div className="bg-purple-100 dark:bg-purple-900 p-4 rounded-lg">
              <p className="text-purple-800 dark:text-purple-200 text-2xl font-bold">{result.updated}</p>
              <p className="text-purple-600 dark:text-purple-400">Updated</p>
            </div>
            <div className="bg-red-100 dark:bg-red-900 p-4 rounded-lg">
              <p className="text-red-800 dark:text-red-200 text-2xl font-bold">{result.errors + result.skipped}</p>
              <p className="text-red-600 dark:text-red-400">Errors/Skipped</p>
            </div>
          </div>

          {result.skipped > 0 && result.skipReasons && (
            <div className="mt-4 mb-6">
              <h3 className="text-lg font-semibold mb-2">Reasons for skipped entries:</h3>
              <ul className="list-disc pl-5 space-y-1">
                {Object.entries(result.skipReasons).map(([reason, count]) => (
                  <li key={reason} className="text-gray-700 dark:text-gray-300">
                    {reason}: <span className="font-semibold">{count as number}</span> entries
                  </li>
                ))}
              </ul>
            </div>
          )}

          {result.sampleSkipped && result.sampleSkipped.length > 0 && (
            <div className="mt-4 mb-6">
              <h3 className="text-lg font-semibold mb-2">Sample skipped entries:</h3>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <thead className="bg-gray-50 dark:bg-gray-700">
                    <tr>
                      <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">Reason</th>
                      <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">Data</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    {result.sampleSkipped.map((item: any, index: number) => (
                      <tr key={index}>
                        <td className="px-3 py-2 text-sm text-gray-700 dark:text-gray-300">{item.reason}</td>
                        <td className="px-3 py-2 text-sm text-gray-700 dark:text-gray-300">
                          <pre className="text-xs overflow-x-auto">{JSON.stringify(item.data, null, 2)}</pre>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          <div className="flex justify-end">
            <button
              onClick={() => navigate('/admin/stageResults')}
              className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
            >
              Go to Stage Results
            </button>
          </div>
        </div>
      ) : (
        <div className="space-y-6">
          {/* Number Checker Tool */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
            <h2 className="text-xl font-semibold mb-4">Stage Result Checker</h2>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
              Use this tool to check if a stage and entry exist and are compatible before importing results.
            </p>

            <form onSubmit={handleCheckNumbers} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Rally
                  </label>
                  <select
                    value={checkNumbers.rallyId}
                    onChange={(e) => setCheckNumbers({...checkNumbers, rallyId: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:text-white"
                  >
                    <option value="">Select a rally</option>
                    {rallies.map((rally) => (
                      <option key={rally.id} value={rally.id}>
                        {rally.name}
                      </option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Stage Number
                  </label>
                  <input
                    type="number"
                    value={checkNumbers.stageNumber}
                    onChange={(e) => setCheckNumbers({...checkNumbers, stageNumber: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:text-white"
                    placeholder="Enter stage number"
                    min="1"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Entry Number
                  </label>
                  <input
                    type="number"
                    value={checkNumbers.entryNumber}
                    onChange={(e) => setCheckNumbers({...checkNumbers, entryNumber: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:text-white"
                    placeholder="Enter car number"
                    min="1"
                  />
                </div>
              </div>

              <div className="flex justify-end">
                <button
                  type="submit"
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center"
                  disabled={checkingNumbers}
                >
                  {checkingNumbers ? (
                    <>
                      <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                      Checking...
                    </>
                  ) : (
                    'Check Stage Result'
                  )}
                </button>
              </div>
            </form>

            {checkResult && (
              <div className={`mt-4 p-4 rounded-lg ${checkResult.exists ? 'bg-green-100 dark:bg-green-900' : 'bg-red-100 dark:bg-red-900'}`}>
                <p className={`font-medium ${checkResult.exists ? 'text-green-800 dark:text-green-200' : 'text-red-800 dark:text-red-200'}`}>
                  {checkResult.message}
                </p>
                {checkResult.rallyData && (
                  <div className="mt-2 text-sm">
                    <h4 className="font-semibold">Rally Info:</h4>
                    <p><strong>Name:</strong> {checkResult.rallyData.name}</p>
                    <p><strong>ID:</strong> {checkResult.rallyData.id}</p>
                  </div>
                )}
                {checkResult.stageData && (
                  <div className="mt-2 text-sm">
                    <h4 className="font-semibold">Stage Info:</h4>
                    <p><strong>Name:</strong> SS{checkResult.stageData.number} - {checkResult.stageData.name}</p>
                    <p><strong>ID:</strong> {checkResult.stageData.id}</p>
                  </div>
                )}
                {checkResult.entryData && (
                  <div className="mt-2 text-sm">
                    <h4 className="font-semibold">Entry Info:</h4>
                    <p><strong>Number:</strong> {checkResult.entryData.number}</p>
                    <p><strong>ID:</strong> {checkResult.entryData.id}</p>
                    <p><strong>Driver:</strong> {checkResult.entryData.driver_first_name} {checkResult.entryData.driver_last_name}</p>
                    <p><strong>Codriver:</strong> {checkResult.entryData.codriver_first_name} {checkResult.entryData.codriver_last_name}</p>
                  </div>
                )}
                {checkResult.resultData && (
                  <div className="mt-2 text-sm">
                    <h4 className="font-semibold">Existing Result:</h4>
                    <p><strong>Time:</strong> {checkResult.resultData.time} seconds</p>
                    <p><strong>ID:</strong> {checkResult.resultData.id}</p>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* CSV Import Form */}
          <form onSubmit={handleSubmit} className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">Import Stage Results from CSV</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Rally
                </label>
                <select
                  value={selectedRally}
                  onChange={(e) => setSelectedRally(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:text-white"
                  required
                >
                  <option value="">Select a rally</option>
                  {rallies.map((rally) => (
                    <option key={rally.id} value={rally.id}>
                      {rally.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Stage
                </label>
                <select
                  value={selectedStage}
                  onChange={(e) => setSelectedStage(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:text-white"
                  disabled={!selectedRally}
                  required
                >
                  <option value="">Select a stage</option>
                  {stages.map((stage) => (
                    <option key={stage.id} value={stage.id}>
                      SS{stage.number} - {stage.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div className="mb-4">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="overrideStatus"
                  checked={overrideStatus}
                  onChange={(e) => setOverrideStatus(e.target.checked)}
                  className="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded"
                />
                <label htmlFor="overrideStatus" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                  Override entry status (allow adding results for retired/DNS/DNF/DSQ entries)
                </label>
              </div>
              <p className="mt-1 text-xs text-gray-500 dark:text-gray-400 ml-6">
                Use this option if you need to import results for entries that have been marked as retired, DNS, DNF, or DSQ.
              </p>
            </div>

            <div className="mb-6">
              <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6">
                <label className="flex flex-col items-center cursor-pointer">
                  <div className="flex flex-col items-center">
                    <Upload className="w-12 h-12 text-gray-400 mb-2" />
                    <span className="text-gray-700 dark:text-gray-300 font-medium mb-1">
                      Click to select a CSV file
                    </span>
                    <div className="text-xs text-gray-500 dark:text-gray-400 text-center">
                      <p className="mb-1">
                        CSV file with columns: entry_number, time
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        <strong>Note:</strong> Time can be in seconds (123.456) or MM:SS.sss format (2:03.456)
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        <strong>Example:</strong> 5,2:03.456
                      </p>
                    </div>
                    <input
                      type="file"
                      className="hidden"
                      accept=".csv"
                      onChange={handleFileChange}
                    />
                  </div>
                </label>
              </div>
              {file && (
                <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                  Selected file: {file.name}
                </p>
              )}
            </div>

            <div className="flex justify-end space-x-4">
              <button
                type="button"
                onClick={() => navigate('/admin/stageResults')}
                className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
                disabled={loading}
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 flex items-center"
                disabled={loading || !selectedRally || !selectedStage || !file}
              >
                {loading ? (
                  <>
                    <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                    Importing...
                  </>
                ) : (
                  <>
                    <Upload className="w-4 h-4 mr-2" />
                    Import Results
                  </>
                )}
              </button>
            </div>
          </form>
        </div>
      )}
    </div>
  );
};

export default ImportStageResultsPage;
