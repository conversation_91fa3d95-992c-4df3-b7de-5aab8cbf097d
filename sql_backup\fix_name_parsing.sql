-- Fix name parsing issues in the persons table
-- This script helps correct persons that were created with incorrect name parsing
-- IMPORTANT: Review the results of check_name_parsing_issues.sql first!

-- ============================================================================
-- STEP 1: Preview what would be changed (DRY RUN)
-- ============================================================================
-- Uncomment this section to see what changes would be made before applying them


SELECT 'PREVIEW: PERSONS THAT WOULD BE UPDATED' as section;
SELECT 
  id,
  first_name as current_first_name,
  last_name as current_last_name,
  SPLIT_PART(first_name, ' ', 1) as new_first_name,
  TRIM(SUBSTRING(first_name FROM POSITION(' ' IN first_name) + 1) || ' ' || last_name) as new_last_name,
  nationality,
  created_at
FROM persons 
WHERE first_name LIKE '% %'  -- Only persons with spaces in first name
  AND SPLIT_PART(first_name, ' ', 1) != first_name  -- Make sure there's actually a change
ORDER BY created_at DESC;

-- ============================================================================
-- STEP 2: Backup the current data (RECOMMENDED)
-- ============================================================================
-- Create a backup table before making changes
CREATE TABLE IF NOT EXISTS persons_backup_before_name_fix AS 
SELECT * FROM persons WHERE first_name LIKE '% %';

-- ============================================================================
-- STEP 3: Fix the name parsing (ACTUAL UPDATE)
-- ============================================================================
-- UNCOMMENT THE FOLLOWING SECTION ONLY AFTER REVIEWING THE PREVIEW ABOVE!

/*
-- Update persons with multi-word first names
UPDATE persons 
SET 
  first_name = SPLIT_PART(first_name, ' ', 1),
  last_name = TRIM(SUBSTRING(first_name FROM POSITION(' ' IN first_name) + 1) || ' ' || last_name),
  updated_at = NOW()
WHERE first_name LIKE '% %'  -- Only persons with spaces in first name
  AND SPLIT_PART(first_name, ' ', 1) != first_name;  -- Make sure there's actually a change

-- Show how many records were updated
SELECT 'RECORDS UPDATED' as section, ROW_COUNT() as updated_count;
*/

-- ============================================================================
-- STEP 4: Verification after update
-- ============================================================================
-- Run this after the update to verify the changes

/*
SELECT 'VERIFICATION: REMAINING MULTI-WORD FIRST NAMES' as section;
SELECT 
  COUNT(*) as remaining_multi_word_first_names
FROM persons 
WHERE first_name LIKE '% %';

SELECT 'VERIFICATION: SAMPLE OF UPDATED RECORDS' as section;
SELECT 
  p.id,
  p.first_name as current_first_name,
  p.last_name as current_last_name,
  pb.first_name as original_first_name,
  pb.last_name as original_last_name,
  p.nationality,
  p.updated_at
FROM persons p
JOIN persons_backup_before_name_fix pb ON p.id = pb.id
WHERE p.first_name != pb.first_name OR p.last_name != pb.last_name
ORDER BY p.updated_at DESC
LIMIT 10;
*/

-- ============================================================================
-- STEP 5: Clean up duplicates (OPTIONAL - BE VERY CAREFUL!)
-- ============================================================================
-- This section helps identify and potentially merge duplicate persons
-- ONLY run this if you're confident about the duplicates!

/*
-- Find potential duplicates after name fixing
SELECT 'POTENTIAL DUPLICATES AFTER NAME FIX' as section;
WITH duplicate_candidates AS (
  SELECT 
    first_name,
    last_name,
    nationality,
    COUNT(*) as count,
    STRING_AGG(id::text, ', ') as ids,
    MIN(created_at) as earliest_created
  FROM persons
  GROUP BY first_name, last_name, nationality
  HAVING COUNT(*) > 1
)
SELECT * FROM duplicate_candidates
ORDER BY count DESC, earliest_created;
*/

-- ============================================================================
-- INSTRUCTIONS FOR USE:
-- ============================================================================
-- 1. First run check_name_parsing_issues.sql to see what needs to be fixed
-- 2. Uncomment the PREVIEW section above to see what would change
-- 3. If the preview looks good, uncomment the ACTUAL UPDATE section
-- 4. Run the verification queries to confirm the changes
-- 5. Optionally, check for and handle duplicates
-- 
-- SAFETY NOTES:
-- - A backup table is automatically created before any changes
-- - All changes include updated_at timestamp updates
-- - You can always restore from the backup if needed
