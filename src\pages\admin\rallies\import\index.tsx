import React, { useState } from 'react';
import { Upload, FileText, CheckCircle, AlertCircle, Loader, ArrowLeft } from 'lucide-react';
import { Link } from 'react-router-dom';

const ImportEWRCPage: React.FC = () => {
  const [file, setFile] = useState<File | null>(null);
  const [importing, setImporting] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0];
    if (selectedFile) {
      if (selectedFile.type === 'application/json' || selectedFile.name.endsWith('.json')) {
        setFile(selectedFile);
        setError(null);
        setResult(null);
      } else {
        setError('Please select a JSON file');
        setFile(null);
      }
    }
  };

  const handleImport = async () => {
    if (!file) {
      setError('Please select a file first');
      return;
    }

    setImporting(true);
    setError(null);
    setResult(null);

    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch('/api/rallies/import-ewrc', {
        method: 'POST',
        credentials: 'include',
        body: formData,
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Import failed');
      }

      setResult(data);
      setFile(null);
      // Reset file input
      const fileInput = document.getElementById('file-input') as HTMLInputElement;
      if (fileInput) fileInput.value = '';
    } catch (err: any) {
      setError(err.message || 'An error occurred during import');
    } finally {
      setImporting(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      {/* Header */}
      <div className="mb-6">
        <Link
          to="/admin/rallies"
          className="inline-flex items-center gap-2 text-blue-600 hover:text-blue-800 mb-4"
        >
          <ArrowLeft className="w-4 h-4" />
          Back to Rallies
        </Link>
        <div className="flex items-center gap-3">
          <FileText className="w-8 h-8 text-blue-600" />
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Import EWRC Data</h1>
            <p className="text-gray-600">Upload JSON files from EWRC scraping script</p>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-md p-6">
        {/* File Upload Section */}
        <div className="mb-6">
          <label htmlFor="file-input" className="block text-sm font-medium text-gray-700 mb-2">
            Select EWRC JSON File
          </label>
          <div className="flex items-center gap-4">
            <div className="flex-1">
              <input
                id="file-input"
                type="file"
                accept=".json"
                onChange={handleFileChange}
                className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
              />
            </div>
            <button
              onClick={handleImport}
              disabled={!file || importing}
              className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center gap-2"
            >
              {importing ? (
                <>
                  <Loader className="w-4 h-4 animate-spin" />
                  Importing...
                </>
              ) : (
                <>
                  <Upload className="w-4 h-4" />
                  Import
                </>
              )}
            </button>
          </div>
          {file && (
            <p className="mt-2 text-sm text-gray-600">
              Selected: {file.name} ({(file.size / 1024).toFixed(1)} KB)
            </p>
          )}
        </div>

        {/* Error Display */}
        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-md">
            <div className="flex items-center gap-2">
              <AlertCircle className="w-5 h-5 text-red-600" />
              <h3 className="text-sm font-medium text-red-800">Import Error</h3>
            </div>
            <p className="mt-1 text-sm text-red-700">{error}</p>
          </div>
        )}

        {/* Success Display */}
        {result && (
          <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-md">
            <div className="flex items-center gap-2 mb-3">
              <CheckCircle className="w-5 h-5 text-green-600" />
              <h3 className="text-sm font-medium text-green-800">Import Successful</h3>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <span className="font-medium text-gray-700">Rally ID:</span>
                <p className="text-gray-900 font-mono text-xs">{result.rally_id}</p>
              </div>
              <div>
                <span className="font-medium text-gray-700">Stages:</span>
                <p className="text-gray-900">{result.stages_imported}</p>
              </div>
              <div>
                <span className="font-medium text-gray-700">Entries:</span>
                <p className="text-gray-900">{result.entries_imported}</p>
              </div>
              <div>
                <span className="font-medium text-gray-700">Results:</span>
                <p className="text-gray-900">{result.results_imported}</p>
              </div>
            </div>
            <p className="mt-2 text-sm text-green-700">{result.message}</p>
            <div className="mt-3">
              <Link
                to={`/admin/rallies/${result.rally_id}`}
                className="inline-flex items-center gap-2 px-3 py-1 bg-green-600 text-white text-sm rounded-md hover:bg-green-700"
              >
                View Rally
              </Link>
            </div>
          </div>
        )}

        {/* Instructions */}
        <div className="bg-gray-50 rounded-md p-4 mb-6">
          <h3 className="text-sm font-medium text-gray-900 mb-2">Import Instructions</h3>
          <ul className="text-sm text-gray-700 space-y-1">
            <li>• Upload JSON files generated by your EWRC scraping script</li>
            <li>• The file should contain rally_info and results arrays</li>
            <li>• Driver and codriver names will be automatically parsed and created</li>
            <li>• Existing rallies with the same name and date will be updated</li>
            <li>• Stage times with nominal_time: true will be skipped</li>
            <li>• Penalties will be imported as separate penalty records</li>
            <li>• Stage distance (km), date, and start time will be imported if available</li>
            <li>• Championships will be linked if they exist in the database (won't create new ones)</li>
            <li>• Entry status: 'finished' if active=true, 'dnf' if active=false</li>
          </ul>
        </div>

        {/* Expected JSON Format */}
        <div className="bg-gray-50 rounded-md p-4">
          <h3 className="text-sm font-medium text-gray-900 mb-2">Expected JSON Format</h3>
          <pre className="text-xs text-gray-700 overflow-x-auto bg-white p-3 rounded border">
{`{
  "rally_info": {
    "name": "Rally Stereas Elladas 2025",
    "championship": ["Greece", "Historic (GR)", "Rally3 (GR)"],
    "start_date": "24.5.2025",
    "end_date": "25.5.2025",
    "distance_meters": 89900,
    "total_entries": 69,
    "finished": 0
  },
  "results": [
    {
      "stage_number": 1,
      "stage_name": "Thiva 1",
      "stage_distance_km": 22.79,
      "stage_date": "25.5.",
      "stage_start_time": "09:23",
      "country": "greece",
      "driver_codriver": "Papadimitriou Ioannis - Kouzionis Christos",
      "entry_number": "#1",
      "group": ["C1"],
      "car": "Škoda Fabia RS Rally2",
      "stage_time_ms": 993240,
      "overall_time_ms": 993240,
      "penalties_ms": 0,
      "super_rally": false,
      "active": true,
      "nominal_time": false,
      "comments": null
    }
  ]
}`}
          </pre>
        </div>
      </div>
    </div>
  );
};

export default ImportEWRCPage;
