import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { formatTime } from '../../utils/timeUtils.js';

interface Rally {
  id: string;
  name: string;
}

interface Stage {
  id: string;
  rally_id: string;
  name: string;
  number: number;
}

interface Entry {
  id: string;
  rally_id: string;
  number: number;
  driver_first_name?: string;
  driver_last_name?: string;
  codriver_first_name?: string;
  codriver_last_name?: string;
}

interface StageResultFormProps {
  initialData?: any;
  onSubmit?: (formData: any) => Promise<void>;
  onDelete?: () => Promise<void>;
  preselectedRallyId?: string;
  preselectedStageId?: string;
}

const StageResultForm: React.FC<StageResultFormProps> = ({
  initialData,
  onSubmit,
  onDelete,
  preselectedRallyId,
  preselectedStageId
}) => {
  const [form, setForm] = useState({
    rally_id: '',
    stage_id: '',
    entry_id: '',
    time: '',
    nominal_time: false,
    super_rally: false,
    is_active: true,
    override_status: false
  });
  const [rallies, setRallies] = useState<Rally[]>([]);
  const [stages, setStages] = useState<Stage[]>([]);
  const [entries, setEntries] = useState<Entry[]>([]);
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const navigate = useNavigate();

  useEffect(() => {
    fetchRallies();
    fetchStages();
    fetchEntries();
  }, []);

  useEffect(() => {
    if (initialData) {
      setForm({
        rally_id: initialData.rally_id || '',
        stage_id: initialData.stage_id || '',
        entry_id: initialData.entry_id || '',
        time: initialData.time ? formatTime(initialData.time) : '',
        nominal_time: initialData.nominal_time || false,
        super_rally: initialData.super_rally || false,
        is_active: initialData.is_active !== undefined ? initialData.is_active : true,
        override_status: false
      });
    } else {
      if (preselectedRallyId) {
        setForm(prev => ({ ...prev, rally_id: preselectedRallyId }));
      }
      if (preselectedStageId) {
        setForm(prev => ({ ...prev, stage_id: preselectedStageId }));
      }
    }
  }, [initialData, preselectedRallyId, preselectedStageId]);



  // Parse time from MM:SS.sss to seconds
  const parseTimeToSeconds = (timeString: string): number => {
    if (!timeString.includes(':')) {
      return parseFloat(timeString);
    }

    const [minutes, seconds] = timeString.split(':');
    return parseFloat(minutes) * 60 + parseFloat(seconds);
  };

  const fetchRallies = async () => {
    try {
      const res = await fetch('/api/rallies', { credentials: 'include' });
      if (!res.ok) throw new Error('Failed to fetch rallies');
      const data = await res.json();
      setRallies(Array.isArray(data) ? data : data.rallies || []);
    } catch (err: any) {
      setError(err.message);
    }
  };

  const fetchStages = async () => {
    try {
      const res = await fetch('/api/stages', { credentials: 'include' });
      if (!res.ok) throw new Error('Failed to fetch stages');
      const data = await res.json();
      setStages(Array.isArray(data) ? data : data.stages || []);
    } catch (err: any) {
      setError(err.message);
    }
  };

  const fetchEntries = async () => {
    try {
      const res = await fetch('/api/entries', { credentials: 'include' });
      if (!res.ok) throw new Error('Failed to fetch entries');
      const data = await res.json();
      setEntries(Array.isArray(data) ? data : data.entries || []);
    } catch (err: any) {
      setError(err.message);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;

    // Handle checkbox inputs
    if (type === 'checkbox') {
      const { checked } = e.target as HTMLInputElement;
      setForm({ ...form, [name]: checked });
      return;
    }

    // If changing the rally, reset the stage and entry if they don't belong to this rally
    if (name === 'rally_id') {
      const newRallyId = value;

      // Check if current stage belongs to the new rally
      const currentStage = stages.find(s => s.id === form.stage_id);
      const stageMatchesRally = currentStage && currentStage.rally_id === newRallyId;

      // Check if current entry belongs to the new rally
      const currentEntry = entries.find(e => e.id === form.entry_id);
      const entryMatchesRally = currentEntry && currentEntry.rally_id === newRallyId;

      setForm({
        ...form,
        rally_id: newRallyId,
        stage_id: stageMatchesRally ? form.stage_id : '',
        entry_id: entryMatchesRally ? form.entry_id : ''
      });
    } else {
      // For stage selection, if the stage has a different rally_id, update the rally_id
      if (name === 'stage_id' && value) {
        const selectedStage = stages.find(s => s.id === value);
        if (selectedStage && selectedStage.rally_id !== form.rally_id) {
          setForm({
            ...form,
            [name]: value,
            rally_id: selectedStage.rally_id,
            // Reset entry if it doesn't match the new rally
            entry_id: entries.find(e => e.id === form.entry_id && e.rally_id === selectedStage.rally_id)
              ? form.entry_id
              : ''
          });
        } else {
          setForm({ ...form, [name]: value });
        }
      } else {
        setForm({ ...form, [name]: value });
      }
    }
  };

  const validateForm = () => {
    const errors: Record<string, string> = {};

    if (!form.rally_id) errors.rally_id = 'Rally is required';
    if (!form.stage_id) errors.stage_id = 'Stage is required';
    if (!form.entry_id) errors.entry_id = 'Entry is required';
    if (!form.time.trim()) errors.time = 'Time is required';

    // Validate time format (either a number or MM:SS.sss)
    const timeRegex = /^\d+(\.\d+)?$|^\d+:\d+(\.\d+)?$/;
    if (form.time && !timeRegex.test(form.time)) {
      errors.time = 'Time must be in seconds or MM:SS.sss format';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    setLoading(true);
    setError('');

    try {
      // Convert time to seconds if in MM:SS.sss format
      const timeInSeconds = parseTimeToSeconds(form.time);

      // Get the rally_id from the selected stage if not provided
      let rally_id = form.rally_id;
      if (!rally_id && form.stage_id) {
        const selectedStage = stages.find(s => s.id === form.stage_id);
        if (selectedStage) {
          rally_id = selectedStage.rally_id;
        }
      }

      // Format the form data for submission
      const formattedData = {
        rally_id,
        stage_id: form.stage_id,
        entry_id: form.entry_id,
        time: timeInSeconds,
        nominal_time: form.nominal_time,
        super_rally: form.super_rally,
        is_active: form.is_active,
        override_status: form.override_status
      };

      if (onSubmit) {
        await onSubmit(formattedData);
      } else {
        // Default submission logic if no onSubmit provided
        const url = initialData ? `/api/stageResults/${initialData.id}` : '/api/stageResults';
        const method = initialData ? 'PUT' : 'POST';

        // Log the form data being sent
        console.log('Submitting stage result form data:', formattedData);

        const response = await fetch(url, {
          method,
          headers: { 'Content-Type': 'application/json' },
          credentials: 'include',
          body: JSON.stringify(formattedData)
        });

        // Log the response
        console.log('Response status:', response.status);
        const responseData = await response.json();
        console.log('Response data:', responseData);

        if (!response.ok) {
          throw new Error(responseData.message || responseData.error || `Failed to ${initialData ? 'update' : 'add'} stage result`);
        }

        navigate('/admin/stageResults');
      }
    } catch (err: any) {
      setError(err.message);
      window.scrollTo(0, 0); // Scroll to top to show error
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!initialData || !onDelete) return;

    if (!window.confirm('Are you sure you want to delete this stage result?')) {
      return;
    }

    try {
      await onDelete();
    } catch (err: any) {
      setError(err.message);
    }
  };

  const handleCancel = () => {
    navigate('/admin/stageResults');
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      {error && (
        <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Rally
          </label>
          <select
            name="rally_id"
            value={form.rally_id}
            onChange={handleChange}
            required
            className={`w-full px-3 py-2 rounded border ${
              validationErrors.rally_id ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
            } dark:bg-gray-700 dark:text-white`}
          >
            <option value="">Select Rally</option>
            {rallies.map(rally => (
              <option key={rally.id} value={rally.id}>{rally.name}</option>
            ))}
          </select>
          {validationErrors.rally_id && (
            <p className="mt-1 text-sm text-red-500">{validationErrors.rally_id}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Stage
          </label>
          <select
            name="stage_id"
            value={form.stage_id}
            onChange={handleChange}
            required
            className={`w-full px-3 py-2 rounded border ${
              validationErrors.stage_id ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
            } dark:bg-gray-700 dark:text-white`}
          >
            <option value="">Select Stage</option>
            {stages
              .filter(stage => !form.rally_id || stage.rally_id === form.rally_id)
              .map(stage => (
                <option key={stage.id} value={stage.id}>
                  SS{stage.number} - {stage.name}
                </option>
              ))
            }
          </select>
          {validationErrors.stage_id && (
            <p className="mt-1 text-sm text-red-500">{validationErrors.stage_id}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Entry
          </label>
          <select
            name="entry_id"
            value={form.entry_id}
            onChange={handleChange}
            required
            className={`w-full px-3 py-2 rounded border ${
              validationErrors.entry_id ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
            } dark:bg-gray-700 dark:text-white`}
          >
            <option value="">Select Entry</option>
            {entries
              .filter(entry => !form.rally_id || entry.rally_id === form.rally_id)
              .map(entry => (
                <option key={entry.id} value={entry.id}>
                  #{entry.number} - {entry.driver_first_name} {entry.driver_last_name} / {entry.codriver_first_name} {entry.codriver_last_name}
                </option>
              ))
            }
          </select>
          {validationErrors.entry_id && (
            <p className="mt-1 text-sm text-red-500">{validationErrors.entry_id}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Time (MM:SS.sss or seconds)
          </label>
          <input
            name="time"
            value={form.time}
            onChange={handleChange}
            required
            placeholder="e.g. 3:45.123 or 225.123"
            className={`w-full px-3 py-2 rounded border ${
              validationErrors.time ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
            } dark:bg-gray-700 dark:text-white`}
          />
          {validationErrors.time && (
            <p className="mt-1 text-sm text-red-500">{validationErrors.time}</p>
          )}
          <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
            Enter time in minutes:seconds.milliseconds format (e.g. 3:45.123) or total seconds (e.g. 225.123)
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="flex items-center">
            <input
              type="checkbox"
              id="nominal_time"
              name="nominal_time"
              checked={form.nominal_time}
              onChange={handleChange}
              className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded"
            />
            <label htmlFor="nominal_time" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
              Nominal Time [N]
            </label>
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              id="super_rally"
              name="super_rally"
              checked={form.super_rally}
              onChange={handleChange}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="super_rally" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
              Super Rally [SR]
            </label>
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              id="is_active"
              name="is_active"
              checked={form.is_active}
              onChange={handleChange}
              className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
            />
            <label htmlFor="is_active" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
              Entry Active
            </label>
          </div>
        </div>

        <div className="text-xs text-gray-500 dark:text-gray-400 space-y-1">
          <p><strong>Nominal Time:</strong> Check if this is a theoretical/nominal time (e.g., when driver didn't complete the stage)</p>
          <p><strong>Super Rally:</strong> Check if the entry was competing under Super Rally rules for this stage</p>
          <p><strong>Entry Active:</strong> Uncheck if the entry didn't start this stage (DNS) or retired before starting</p>
        </div>

        <div className="flex items-center">
          <input
            type="checkbox"
            id="override_status"
            name="override_status"
            checked={form.override_status}
            onChange={handleChange}
            className="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded"
          />
          <label htmlFor="override_status" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
            Override entry status (allow adding results for retired/DNS/DNF/DSQ entries)
          </label>
        </div>
        <p className="ml-6 text-xs text-gray-500 dark:text-gray-400">
          Use this option if you need to add results for entries that have been marked as retired, DNS, DNF, or DSQ.
        </p>

        <div className="flex justify-between pt-4">
          <div>
            {initialData && onDelete && (
              <button
                type="button"
                onClick={handleDelete}
                className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
              >
                Delete
              </button>
            )}
          </div>
          <div className="flex space-x-3">
            <button
              type="button"
              onClick={handleCancel}
              className="px-4 py-2 border border-gray-300 rounded text-gray-700 bg-white hover:bg-gray-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:hover:bg-gray-600"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors disabled:opacity-50"
            >
              {loading ? (initialData ? 'Saving...' : 'Adding...') : (initialData ? 'Save Changes' : 'Add Stage Result')}
            </button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default StageResultForm;
