import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, Link } from 'react-router-dom';
import { Trophy, Calendar, Users, Award, Target, BarChart3 } from 'lucide-react';
import { Championship, ChampionshipStandings, ChampionshipClassStandings, ChampionshipClass, ChampionshipCalendar } from '../types';
import ChampionshipStandingsTable from '../components/championships/ChampionshipStandingsTable';

const ChampionshipStandingsPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [championship, setChampionship] = useState<Championship | null>(null);
  const [standings, setStandings] = useState<ChampionshipStandings[]>([]);
  const [classStandings, setClassStandings] = useState<ChampionshipClassStandings[]>([]);
  const [classes, setClasses] = useState<ChampionshipClass[]>([]);
  const [calendar, setCalendar] = useState<ChampionshipCalendar[]>([]);
  const [selectedClass, setSelectedClass] = useState<string>('');
  const [activeTab, setActiveTab] = useState<'overall' | 'classes' | 'calendar'>('overall');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchChampionshipData = async () => {
      if (!id) return;

      try {
        setLoading(true);
        
        // Fetch championship details
        const championshipResponse = await fetch(`/api/championships/${id}`);
        if (!championshipResponse.ok) throw new Error('Failed to fetch championship');
        const championshipData = await championshipResponse.json();
        setChampionship(championshipData);

        // Fetch overall standings
        const standingsResponse = await fetch(`/api/championship-results/championships/${id}/standings`);
        if (!standingsResponse.ok) throw new Error('Failed to fetch standings');
        const standingsData = await standingsResponse.json();
        setStandings(standingsData);

        // Fetch classes
        const classesResponse = await fetch(`/api/championship-results/championships/${id}/classes`);
        if (!classesResponse.ok) throw new Error('Failed to fetch classes');
        const classesData = await classesResponse.json();
        setClasses(classesData);

        // Fetch calendar
        const calendarResponse = await fetch(`/api/championship-results/championships/${id}/calendar`);
        if (!calendarResponse.ok) throw new Error('Failed to fetch calendar');
        const calendarData = await calendarResponse.json();
        setCalendar(calendarData);

      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch championship data');
        console.error('Error fetching championship data:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchChampionshipData();
  }, [id]);

  // Fetch class-specific standings when class is selected
  useEffect(() => {
    const fetchClassStandings = async () => {
      if (!id || !selectedClass) return;

      try {
        const response = await fetch(`/api/championship-results/championships/${id}/standings?class=${selectedClass}`);
        if (!response.ok) throw new Error('Failed to fetch class standings');
        const data = await response.json();
        setClassStandings(data);
      } catch (err) {
        console.error('Error fetching class standings:', err);
        setClassStandings([]);
      }
    };

    fetchClassStandings();
  }, [id, selectedClass]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600 dark:text-gray-400">Loading championship data...</p>
        </div>
      </div>
    );
  }

  if (error || !championship) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-600 dark:text-red-400 text-xl mb-4">
            {error || 'Championship not found'}
          </div>
          <Link to="/championships" className="text-blue-600 hover:text-blue-800">
            ← Back to Championships
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Championship Header */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 mb-8">
          <div className="px-6 py-8">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                  {championship.name}
                </h1>
                <div className="flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400">
                  <div className="flex items-center">
                    <Calendar className="w-4 h-4 mr-1" />
                    {championship.year}
                  </div>
                  <div className="flex items-center">
                    <Trophy className="w-4 h-4 mr-1" />
                    {championship.type}
                  </div>
                  <div className="flex items-center">
                    <Target className="w-4 h-4 mr-1" />
                    {calendar.length} rallies
                  </div>
                  <div className="flex items-center">
                    <Users className="w-4 h-4 mr-1" />
                    {standings.length} drivers
                  </div>
                </div>
                {championship.description && (
                  <p className="mt-3 text-gray-700 dark:text-gray-300">
                    {championship.description}
                  </p>
                )}
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                  {championship.year}
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  Season
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 mb-6">
          <div className="border-b border-gray-200 dark:border-gray-700">
            <nav className="flex space-x-8 px-6">
              <button
                onClick={() => setActiveTab('overall')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'overall'
                    ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
                }`}
              >
                <Trophy className="w-4 h-4 inline mr-2" />
                Overall Standings
              </button>
              <button
                onClick={() => setActiveTab('classes')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'classes'
                    ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
                }`}
              >
                <Award className="w-4 h-4 inline mr-2" />
                Class Standings
              </button>
              <button
                onClick={() => setActiveTab('calendar')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'calendar'
                    ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
                }`}
              >
                <Calendar className="w-4 h-4 inline mr-2" />
                Calendar
              </button>
            </nav>
          </div>
        </div>

        {/* Tab Content */}
        {activeTab === 'overall' && (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-600">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                Overall Championship Standings
              </h2>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                Points include rally results and power stage bonuses
              </p>
            </div>
            <div className="p-6">
              <ChampionshipStandingsTable
                standings={standings}
                isClassStandings={false}
              />
            </div>
          </div>
        )}

        {activeTab === 'classes' && (
          <div className="space-y-6">
            {/* Class Selector */}
            {classes.length > 0 && (
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
                <label htmlFor="class-select" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Select Class
                </label>
                <select
                  id="class-select"
                  value={selectedClass}
                  onChange={(e) => setSelectedClass(e.target.value)}
                  className="block w-full max-w-xs px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                >
                  <option value="">Select a class...</option>
                  {classes.map((cls) => (
                    <option key={cls.class_name} value={cls.class_name}>
                      {cls.class_name} ({cls.driver_count} drivers)
                    </option>
                  ))}
                </select>
              </div>
            )}

            {/* Class Standings */}
            {selectedClass && classStandings.length > 0 ? (
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-600">
                  <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                    {selectedClass} Class Standings
                  </h2>
                </div>
                <div className="p-6">
                  <ChampionshipStandingsTable
                    standings={classStandings}
                    isClassStandings={true}
                  />
                </div>
              </div>
            ) : selectedClass ? (
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-8 text-center">
                <BarChart3 className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600 dark:text-gray-400">
                  No standings available for {selectedClass} class
                </p>
              </div>
            ) : (
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-8 text-center">
                <Award className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600 dark:text-gray-400">
                  Select a class to view standings
                </p>
              </div>
            )}
          </div>
        )}

        {activeTab === 'calendar' && (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-600">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                Championship Calendar
              </h2>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                All rallies in this championship
              </p>
            </div>
            <div className="divide-y divide-gray-200 dark:divide-gray-700">
              {calendar.map((rally, index) => (
                <div key={rally.id} className="p-6 hover:bg-gray-50 dark:hover:bg-gray-700">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3">
                        <div className="text-lg font-semibold text-gray-900 dark:text-white">
                          Round {index + 1}
                        </div>
                        <Link
                          to={`/rallies/${rally.id}`}
                          className="text-lg font-medium text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                        >
                          {rally.name}
                        </Link>
                        {rally.coefficient !== 1.0 && (
                          <span className="bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200 px-2 py-1 rounded-full text-xs">
                            {rally.coefficient}x points
                          </span>
                        )}
                      </div>
                      <div className="flex items-center space-x-4 mt-2 text-sm text-gray-600 dark:text-gray-400">
                        <div className="flex items-center">
                          <Calendar className="w-4 h-4 mr-1" />
                          {new Date(rally.start_date).toLocaleDateString()} - {new Date(rally.end_date).toLocaleDateString()}
                        </div>
                        <div className="flex items-center">
                          <Users className="w-4 h-4 mr-1" />
                          {rally.entry_count} entries
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                        rally.status === 'finished'
                          ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                          : rally.status === 'running'
                          ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                          : 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                      }`}>
                        {rally.status.charAt(0).toUpperCase() + rally.status.slice(1)}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ChampionshipStandingsPage;
