-- Exact championship setup for Rally Stereas Elladas 2025
-- Based on your specific championship IDs and class structure

-- We'll use the existing class column in entries table
-- No need for separate championship_classes table since you have comma-separated classes

-- Update the championship_overall_classification view to handle your class structure
DROP VIEW IF EXISTS championship_overall_classification;

CREATE VIEW championship_overall_classification AS
SELECT DISTINCT
    ce.championship_id,
    c.name as championship_name,
    r.id as rally_id,
    r.name as rally_name,
    r.start_date as rally_date,
    e.id as entry_id,
    e.number,
    CONCAT(pd.first_name, ' ', pd.last_name) as driver,
    pd.nationality as driver_nationality,
    CONCAT(pc.first_name, ' ', pc.last_name) as codriver,
    pc.nationality as codriver_nationality,
    e.car,
    e.class,
    oc.position,
    oc.total_time,
    oc.time_diff_leader,
    -- Calculate championship points with coefficient
    CASE
        WHEN oc.position = 1 THEN 25 * ce.coefficient
        WHEN oc.position = 2 THEN 18 * ce.coefficient
        WHEN oc.position = 3 THEN 15 * ce.coefficient
        WHEN oc.position = 4 THEN 12 * ce.coefficient
        WHEN oc.position = 5 THEN 10 * ce.coefficient
        WHEN oc.position = 6 THEN 8 * ce.coefficient
        WHEN oc.position = 7 THEN 6 * ce.coefficient
        WHEN oc.position = 8 THEN 4 * ce.coefficient
        WHEN oc.position = 9 THEN 2 * ce.coefficient
        WHEN oc.position = 10 THEN 1 * ce.coefficient
        ELSE 0
    END as championship_points
FROM championship_events ce
JOIN championships c ON ce.championship_id = c.id
JOIN rallies r ON ce.rally_id = r.id
JOIN entries e ON r.id = e.rally_id
JOIN persons pd ON e.driver_id = pd.id
JOIN persons pc ON e.codriver_id = pc.id
JOIN overall_classification oc ON e.id = oc.entry_id
WHERE e.status IN ('finished', 'retired', 'dnf')
AND (
    -- Greece Championship: All C-classes
    (ce.championship_id = '55a003a9-66ff-4a37-b11d-2e14df10bae3' AND (
        e.class LIKE '%C1%' OR e.class LIKE '%C2%' OR e.class LIKE '%C3%'
        OR e.class LIKE '%C4%' OR e.class LIKE '%C5%' OR e.class LIKE '%C6%'
    ))
    OR
    -- Historic Championship: Numeric classes 2, 3, 4 (but not C2, C3, C4)
    (ce.championship_id = 'cc1e9147-fdb3-4483-aad3-9e27795eff17' AND (
        (e.class = '2' OR e.class LIKE '2,%' OR e.class LIKE '%,2,%' OR e.class LIKE '%,2' OR e.class LIKE '%, 2')
        OR (e.class = '3' OR e.class LIKE '3,%' OR e.class LIKE '%,3,%' OR e.class LIKE '%,3' OR e.class LIKE '%, 3')
        OR (e.class = '4' OR e.class LIKE '4,%' OR e.class LIKE '%,4,%' OR e.class LIKE '%,4' OR e.class LIKE '%, 4')
    ) AND e.class NOT LIKE '%C2%' AND e.class NOT LIKE '%C3%' AND e.class NOT LIKE '%C4%')
    OR
    -- Historic Gravel Cup: Same as Historic
    (ce.championship_id = 'aa934657-d4f5-49bb-80d5-b0be90e72b4c' AND (
        (e.class = '2' OR e.class LIKE '2,%' OR e.class LIKE '%,2,%' OR e.class LIKE '%,2' OR e.class LIKE '%, 2')
        OR (e.class = '3' OR e.class LIKE '3,%' OR e.class LIKE '%,3,%' OR e.class LIKE '%,3' OR e.class LIKE '%, 3')
        OR (e.class = '4' OR e.class LIKE '4,%' OR e.class LIKE '%,4,%' OR e.class LIKE '%,4' OR e.class LIKE '%, 4')
    ) AND e.class NOT LIKE '%C2%' AND e.class NOT LIKE '%C3%' AND e.class NOT LIKE '%C4%')
    OR
    -- Rally3 Championship: Only entries with Rally3 in their class
    (ce.championship_id = '895f159e-f147-439b-b5ab-04972033a7bb' AND e.class LIKE '%Rally3%')
)
ORDER BY ce.championship_id, r.start_date, oc.position;

-- Show current setup verification
SELECT 'Championship filtering logic:' as info;
SELECT 'Greece: All C-classes (C1, C2, C3, C4, C5, C6)' as championship_info
UNION ALL SELECT 'Historic: Numeric classes (2, 3, 4) excluding C-classes'
UNION ALL SELECT 'Historic Gravel Cup: Same as Historic'
UNION ALL SELECT 'Rally3: Only entries with Rally3 in class field';

-- Test the filtering results
SELECT 'Entries per championship after filtering:' as info;
SELECT
    championship_name,
    COUNT(*) as entry_count,
    STRING_AGG(DISTINCT class, ', ' ORDER BY class) as classes_included
FROM championship_overall_classification coc
WHERE rally_name LIKE '%Stereas Elladas%'
GROUP BY championship_id, championship_name
ORDER BY entry_count DESC;
