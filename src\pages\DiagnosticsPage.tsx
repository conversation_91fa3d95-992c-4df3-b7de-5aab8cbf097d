import React, { useState, useEffect } from 'react';
import { CheckCircle, XCircle, AlertCircle, RefreshCw } from 'lucide-react';

interface DiagnosticResult {
  name: string;
  status: 'success' | 'error' | 'warning' | 'loading';
  message: string;
  details?: string;
}

const DiagnosticsPage: React.FC = () => {
  const [diagnostics, setDiagnostics] = useState<DiagnosticResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  const runDiagnostics = async () => {
    setIsRunning(true);
    const results: DiagnosticResult[] = [];

    // Test API endpoints
    const endpoints = [
      { name: 'Rallies API', url: '/api/rallies' },
      { name: 'Drivers API', url: '/api/drivers' },
      { name: 'Driver Stats API', url: '/api/driverStats' },
      { name: 'Championships API', url: '/api/championships' },
      { name: 'Stages API', url: '/api/stages' },
      { name: 'Entries API', url: '/api/entries' },
    ];

    for (const endpoint of endpoints) {
      try {
        const response = await fetch(endpoint.url);
        if (response.ok) {
          const data = await response.json();
          results.push({
            name: endpoint.name,
            status: 'success',
            message: `✓ Accessible (${response.status})`,
            details: `Returned ${Array.isArray(data) ? data.length : 'unknown'} items`
          });
        } else {
          results.push({
            name: endpoint.name,
            status: 'error',
            message: `✗ HTTP ${response.status}`,
            details: response.statusText
          });
        }
      } catch (error) {
        results.push({
          name: endpoint.name,
          status: 'error',
          message: '✗ Connection failed',
          details: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    // Test browser environment
    results.push({
      name: 'Browser Environment',
      status: 'success',
      message: '✓ React app loaded',
      details: `User Agent: ${navigator.userAgent.substring(0, 50)}...`
    });

    // Test CSS loading
    const flagIconsLoaded = document.querySelector('link[href*="flag-icons"]');
    results.push({
      name: 'Flag Icons CSS',
      status: flagIconsLoaded ? 'success' : 'warning',
      message: flagIconsLoaded ? '✓ Loaded' : '⚠ Not found',
      details: flagIconsLoaded ? 'Flag icons should display correctly' : 'Flags may not display'
    });

    setDiagnostics(results);
    setIsRunning(false);
  };

  useEffect(() => {
    runDiagnostics();
  }, []);

  const getStatusIcon = (status: DiagnosticResult['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'error':
        return <XCircle className="w-5 h-5 text-red-500" />;
      case 'warning':
        return <AlertCircle className="w-5 h-5 text-yellow-500" />;
      case 'loading':
        return <RefreshCw className="w-5 h-5 text-blue-500 animate-spin" />;
    }
  };

  const getStatusColor = (status: DiagnosticResult['status']) => {
    switch (status) {
      case 'success':
        return 'border-green-200 bg-green-50 dark:bg-green-900/20';
      case 'error':
        return 'border-red-200 bg-red-50 dark:bg-red-900/20';
      case 'warning':
        return 'border-yellow-200 bg-yellow-50 dark:bg-yellow-900/20';
      case 'loading':
        return 'border-blue-200 bg-blue-50 dark:bg-blue-900/20';
    }
  };

  return (
    <div className="pt-16 pb-12 min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <div className="flex items-center justify-between mb-6">
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                System Diagnostics
              </h1>
              <button
                onClick={runDiagnostics}
                disabled={isRunning}
                className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-4 py-2 rounded-lg transition-colors"
              >
                <RefreshCw className={`w-4 h-4 ${isRunning ? 'animate-spin' : ''}`} />
                <span>{isRunning ? 'Running...' : 'Run Diagnostics'}</span>
              </button>
            </div>

            <div className="space-y-4">
              {diagnostics.map((diagnostic, index) => (
                <div
                  key={index}
                  className={`border rounded-lg p-4 ${getStatusColor(diagnostic.status)}`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      {getStatusIcon(diagnostic.status)}
                      <div>
                        <h3 className="font-semibold text-gray-900 dark:text-white">
                          {diagnostic.name}
                        </h3>
                        <p className="text-sm text-gray-600 dark:text-gray-300">
                          {diagnostic.message}
                        </p>
                      </div>
                    </div>
                  </div>
                  {diagnostic.details && (
                    <div className="mt-2 text-xs text-gray-500 dark:text-gray-400 font-mono">
                      {diagnostic.details}
                    </div>
                  )}
                </div>
              ))}
            </div>

            {diagnostics.length === 0 && !isRunning && (
              <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                Click "Run Diagnostics" to check system status
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DiagnosticsPage;
