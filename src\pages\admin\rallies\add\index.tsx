import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Flag } from 'lucide-react';
import RallyForm from '../../../../components/admin/rally-form';

interface Championship {
  id: string;
  name: string;
  year: number;
}

const AddRallyPage: React.FC = () => {
  const [championships, setChampionships] = useState<Championship[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const navigate = useNavigate();

  useEffect(() => {
    fetchChampionships();
  }, []);

  const fetchChampionships = async () => {
    setLoading(true);
    try {
      const res = await fetch('/api/championships', { credentials: 'include' });
      if (!res.ok) throw new Error('Failed to fetch championships');
      const data = await res.json();
      setChampionships(Array.isArray(data) ? data : data.championships || []);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (formData: any) => {
    console.log('Submitting rally with data:', formData);

    const response = await fetch('/api/rallies', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      credentials: 'include',
      body: JSON.stringify(formData)
    });

    // Log the response for debugging
    console.log('Response status:', response.status);

    if (!response.ok) {
      const data = await response.json();
      console.error('Error response:', data);
      throw new Error(data.message || data.error || 'Failed to add rally');
    }

    navigate('/admin/rallies');
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center mb-6">
        <Flag className="w-6 h-6 text-red-600 mr-2" />
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Add Rally</h1>
      </div>

      {error && (
        <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          {error}
        </div>
      )}

      {loading ? (
        <div className="p-8 text-center">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-4 border-gray-300 border-t-red-600"></div>
          <p className="mt-2 text-gray-600 dark:text-gray-400">Loading...</p>
        </div>
      ) : (
        <RallyForm
          championships={championships}
          onSubmit={handleSubmit}
        />
      )}
    </div>
  );
};

export default AddRallyPage;
