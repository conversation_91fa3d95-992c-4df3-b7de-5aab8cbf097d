import React, { useState, useEffect } from 'react';
import { X, Plus } from 'lucide-react';
import { Championship, RallyChampionship } from '../../types';

interface ChampionshipSelectorProps {
  availableChampionships: Championship[];
  selectedChampionships: RallyChampionship[];
  onChange: (championships: RallyChampionship[]) => void;
  disabled?: boolean;
}

const ChampionshipSelector: React.FC<ChampionshipSelectorProps> = ({
  availableChampionships,
  selectedChampionships,
  onChange,
  disabled = false
}) => {
  const [showAddForm, setShowAddForm] = useState(false);
  const [selectedChampId, setSelectedChampId] = useState('');
  const [coefficient, setCoefficient] = useState('1.0');

  // Get available championships that aren't already selected
  const unselectedChampionships = availableChampionships.filter(
    champ => !selectedChampionships.some(selected => selected.id === champ.id)
  );

  const handleAddChampionship = () => {
    if (!selectedChampId) return;

    const championship = availableChampionships.find(c => c.id === selectedChampId);
    if (!championship) return;

    const newChampionship: RallyChampionship = {
      id: championship.id,
      name: championship.name,
      year: championship.year,
      coefficient: parseFloat(coefficient) || 1.0
    };

    onChange([...selectedChampionships, newChampionship]);
    setSelectedChampId('');
    setCoefficient('1.0');
    setShowAddForm(false);
  };

  const handleRemoveChampionship = (championshipId: string) => {
    onChange(selectedChampionships.filter(c => c.id !== championshipId));
  };

  const handleCoefficientChange = (championshipId: string, newCoefficient: string) => {
    const updatedChampionships = selectedChampionships.map(c =>
      c.id === championshipId
        ? { ...c, coefficient: parseFloat(newCoefficient) || 1.0 }
        : c
    );
    onChange(updatedChampionships);
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
          Championships
        </label>
        {!disabled && unselectedChampionships.length > 0 && (
          <button
            type="button"
            onClick={() => setShowAddForm(!showAddForm)}
            className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 flex items-center"
          >
            <Plus className="w-4 h-4 mr-1" />
            Add Championship
          </button>
        )}
      </div>

      {/* Selected Championships */}
      <div className="space-y-2">
        {selectedChampionships.length === 0 ? (
          <p className="text-sm text-gray-500 dark:text-gray-400 italic">
            No championships selected
          </p>
        ) : (
          selectedChampionships.map((championship) => (
            <div
              key={championship.id}
              className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
            >
              <div className="flex items-center space-x-3">
                <span className="font-medium text-gray-900 dark:text-white">
                  {championship.name} {championship.year}
                </span>
                <div className="flex items-center space-x-2">
                  <label className="text-xs text-gray-500 dark:text-gray-400">
                    Coefficient:
                  </label>
                  <input
                    type="number"
                    min="0.1"
                    max="10"
                    step="0.1"
                    value={championship.coefficient}
                    onChange={(e) => handleCoefficientChange(championship.id, e.target.value)}
                    disabled={disabled}
                    className="w-16 px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded dark:bg-gray-600 dark:text-white"
                  />
                </div>
              </div>
              {!disabled && (
                <button
                  type="button"
                  onClick={() => handleRemoveChampionship(championship.id)}
                  className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                >
                  <X className="w-4 h-4" />
                </button>
              )}
            </div>
          ))
        )}
      </div>

      {/* Add Championship Form */}
      {showAddForm && !disabled && (
        <div className="p-4 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
            <div className="md:col-span-2">
              <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                Championship
              </label>
              <select
                value={selectedChampId}
                onChange={(e) => setSelectedChampId(e.target.value)}
                className="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded dark:bg-gray-600 dark:text-white"
              >
                <option value="">Select Championship</option>
                {unselectedChampionships.map(championship => (
                  <option key={championship.id} value={championship.id}>
                    {championship.name} {championship.year}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                Coefficient
              </label>
              <input
                type="number"
                min="0.1"
                max="10"
                step="0.1"
                value={coefficient}
                onChange={(e) => setCoefficient(e.target.value)}
                className="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded dark:bg-gray-600 dark:text-white"
              />
            </div>
          </div>
          <div className="flex justify-end space-x-2 mt-3">
            <button
              type="button"
              onClick={() => setShowAddForm(false)}
              className="px-3 py-1 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200"
            >
              Cancel
            </button>
            <button
              type="button"
              onClick={handleAddChampionship}
              disabled={!selectedChampId}
              className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Add
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ChampionshipSelector;
