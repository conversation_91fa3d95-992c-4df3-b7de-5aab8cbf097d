import React, { useState, useEffect } from 'react';
import { Clock, Plus, Trash, Edit, RefreshCw, Calendar, MapPin, ChevronDown, ChevronUp } from 'lucide-react';
import ItineraryItemForm from './itinerary-item-form';

interface ItineraryItem {
  id: string;
  rally_id: string;
  type: string;
  name: string;
  location: string | null;
  start_time: string;
  duration: number | null;
  leg_number: number;
  day_number: number;
  order_in_day: number;
  related_id: string | null;
  additional_info: any;
}

interface ItineraryManagerProps {
  rallyId: string;
}

const ItineraryManager: React.FC<ItineraryManagerProps> = ({ rallyId }) => {
  const [itineraryItems, setItineraryItems] = useState<ItineraryItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [showForm, setShowForm] = useState(false);
  const [editingItem, setEditingItem] = useState<ItineraryItem | null>(null);
  const [expandedDay, setExpandedDay] = useState<number | null>(null);

  useEffect(() => {
    fetchItineraryItems();
  }, [rallyId]);

  const fetchItineraryItems = async () => {
    setLoading(true);
    try {
      const res = await fetch(`/api/itinerary/rally/${rallyId}`, { credentials: 'include' });
      if (!res.ok) throw new Error('Failed to fetch itinerary items');
      const data = await res.json();
      setItineraryItems(Array.isArray(data) ? data : []);

      // Expand the first day by default if there are items
      if (data.length > 0) {
        setExpandedDay(data[0].day_number);
      }
    } catch (err: any) {
      console.error('Error fetching itinerary items:', err);
      setError(err.message || 'Failed to load itinerary items');
    } finally {
      setLoading(false);
    }
  };

  const handleAddItem = async (formData: any) => {
    try {
      const res = await fetch('/api/itinerary', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify(formData)
      });

      if (!res.ok) {
        const errorData = await res.json();
        console.error('Server error response:', errorData);
        throw new Error(errorData.message || errorData.error || 'Failed to add itinerary item');
      }

      setShowForm(false);
      fetchItineraryItems();
    } catch (err: any) {
      console.error('Error adding itinerary item:', err);
      throw err;
    }
  };

  const handleUpdateItem = async (formData: any) => {
    if (!editingItem) return;

    try {
      const res = await fetch(`/api/itinerary/${editingItem.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify(formData)
      });

      if (!res.ok) {
        const errorData = await res.json();
        throw new Error(errorData.message || 'Failed to update itinerary item');
      }

      setEditingItem(null);
      fetchItineraryItems();
    } catch (err: any) {
      console.error('Error updating itinerary item:', err);
      throw err;
    }
  };

  const handleDeleteItem = async (id: string) => {
    if (!window.confirm('Are you sure you want to delete this itinerary item?')) {
      return;
    }

    try {
      const res = await fetch(`/api/itinerary/${id}`, {
        method: 'DELETE',
        credentials: 'include'
      });

      if (!res.ok) {
        const errorData = await res.json();
        throw new Error(errorData.message || 'Failed to delete itinerary item');
      }

      fetchItineraryItems();
    } catch (err: any) {
      console.error('Error deleting itinerary item:', err);
      setError(err.message || 'Failed to delete itinerary item');
    }
  };

  const handleGenerateFromStages = async () => {
    if (!window.confirm('This will generate itinerary items from stages. Any existing stage-type items will be replaced. Continue?')) {
      return;
    }

    setLoading(true);
    try {
      const res = await fetch(`/api/itinerary/generate-from-stages/${rallyId}`, {
        method: 'POST',
        credentials: 'include'
      });

      if (!res.ok) {
        const errorData = await res.json();
        throw new Error(errorData.message || 'Failed to generate itinerary items');
      }

      fetchItineraryItems();
    } catch (err: any) {
      console.error('Error generating itinerary items:', err);
      setError(err.message || 'Failed to generate itinerary items');
    } finally {
      setLoading(false);
    }
  };

  const formatTime = (timeString: string) => {
    return new Date(timeString).toLocaleTimeString('en-GB', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatDate = (timeString: string) => {
    return new Date(timeString).toLocaleDateString('en-GB', {
      weekday: 'long',
      day: 'numeric',
      month: 'long'
    });
  };

  const getItemTypeLabel = (type: string) => {
    switch (type) {
      case 'stage':
        return 'Stage';
      case 'service':
        return 'Service';
      case 'regroup':
        return 'Regroup';
      case 'refuel':
        return 'Refuel';
      case 'start':
        return 'Start';
      case 'finish':
        return 'Finish';
      case 'podium':
        return 'Podium';
      case 'parc_ferme':
        return 'Parc Fermé';
      case 'shakedown':
        return 'Shakedown';
      case 'tyre_fitting':
        return 'Tyre Fitting';
      case 'remote_service':
        return 'Remote Service';
      default:
        return type.charAt(0).toUpperCase() + type.slice(1).replace('_', ' ');
    }
  };

  const getUniqueDays = () => {
    const days = new Set<number>();
    itineraryItems.forEach(item => days.add(item.day_number));
    return Array.from(days).sort((a, b) => a - b);
  };

  const getItemsForDay = (dayNumber: number) => {
    return itineraryItems
      .filter(item => item.day_number === dayNumber)
      .sort((a, b) => a.order_in_day - b.order_in_day);
  };

  const getDayDate = (dayNumber: number) => {
    const dayItem = itineraryItems.find(item => item.day_number === dayNumber);
    return dayItem ? formatDate(dayItem.start_time) : `Day ${dayNumber}`;
  };

  const toggleDay = (dayNumber: number) => {
    setExpandedDay(expandedDay === dayNumber ? null : dayNumber);
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white flex items-center">
          <Clock className="mr-2 h-5 w-5 text-blue-600" />
          Rally Itinerary
        </h2>

        <div className="flex space-x-2">
          <button
            onClick={fetchItineraryItems}
            className="p-2 text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400"
            title="Refresh"
          >
            <RefreshCw size={20} />
          </button>

          <button
            onClick={handleGenerateFromStages}
            className="bg-green-600 text-white px-3 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center"
          >
            Generate from Stages
          </button>

          <button
            onClick={() => {
              setEditingItem(null);
              setShowForm(!showForm);
            }}
            className="bg-blue-600 text-white px-3 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center"
          >
            <Plus size={18} className="mr-1" />
            Add Item
          </button>
        </div>
      </div>

      {error && (
        <div className="mb-4 p-3 bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-200 rounded-lg">
          {error}
        </div>
      )}

      {(showForm || editingItem) && (
        <div className="mb-6 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            {editingItem ? 'Edit Itinerary Item' : 'Add Itinerary Item'}
          </h3>
          <ItineraryItemForm
            initialData={editingItem}
            rallyId={rallyId}
            onSubmit={editingItem ? handleUpdateItem : handleAddItem}
            onCancel={() => {
              setShowForm(false);
              setEditingItem(null);
            }}
          />
        </div>
      )}

      {loading && !itineraryItems.length ? (
        <div className="flex justify-center items-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      ) : itineraryItems.length === 0 ? (
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-8 text-center">
          <p className="text-gray-600 dark:text-gray-400">No itinerary items found for this rally.</p>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            Click "Add Item" to create a new itinerary item or "Generate from Stages" to automatically create items from stages.
          </p>
        </div>
      ) : (
        <div className="space-y-6">
          {getUniqueDays().map(dayNumber => (
            <div key={dayNumber} className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
              <div
                className="bg-gray-50 dark:bg-gray-700 px-4 py-3 flex justify-between items-center cursor-pointer"
                onClick={() => toggleDay(dayNumber)}
              >
                <div className="flex items-center">
                  <Calendar className="w-5 h-5 mr-2 text-blue-600" />
                  <h3 className="font-medium text-gray-900 dark:text-white">
                    Day {dayNumber} - {getDayDate(dayNumber)}
                  </h3>
                </div>
                <div>
                  {expandedDay === dayNumber ? (
                    <ChevronUp className="w-5 h-5 text-gray-500 dark:text-gray-400" />
                  ) : (
                    <ChevronDown className="w-5 h-5 text-gray-500 dark:text-gray-400" />
                  )}
                </div>
              </div>

              {expandedDay === dayNumber && (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead className="bg-gray-50 dark:bg-gray-700">
                      <tr>
                        <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Time
                        </th>
                        <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Type
                        </th>
                        <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Name
                        </th>
                        <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Location
                        </th>
                        <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Details
                        </th>
                        <th scope="col" className="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                      {getItemsForDay(dayNumber).map(item => (
                        <tr key={item.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                          <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                            {formatTime(item.start_time)}
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400">
                            {getItemTypeLabel(item.type)}
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                            {item.name}
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400">
                            {item.location && (
                              <div className="flex items-center">
                                <MapPin className="w-4 h-4 mr-1 text-gray-400" />
                                {item.location}
                              </div>
                            )}
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400">
                            {item.type === 'stage' && item.additional_info && (
                              <span>
                                {(() => {
                                  try {
                                    if (typeof item.additional_info === 'string') {
                                      return JSON.parse(item.additional_info).length;
                                    } else {
                                      return item.additional_info.length;
                                    }
                                  } catch (e) {
                                    return '?';
                                  }
                                })()} km
                              </span>
                            )}
                            {item.type === 'service' && item.duration && (
                              <span>{item.duration} min</span>
                            )}
                            {item.type === 'regroup' && item.duration && (
                              <span>{item.duration} min</span>
                            )}
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-right text-sm font-medium">
                            <button
                              onClick={() => setEditingItem(item)}
                              className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3"
                            >
                              <Edit size={18} />
                            </button>
                            <button
                              onClick={() => handleDeleteItem(item.id)}
                              className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                            >
                              <Trash size={18} />
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default ItineraryManager;
