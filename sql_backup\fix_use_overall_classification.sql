-- Fix Championship View to Use overall_classification
-- Instead of recalculating times, use the existing correct times

-- 1. Drop the current championship view
DROP VIEW IF EXISTS championship_overall_classification CASCADE;

-- 2. Create new championship view using overall_classification
CREATE VIEW championship_overall_classification AS
SELECT
  ce.championship_id,
  c.name AS championship_name,
  oc.rally_id,
  r.name AS rally_name,
  oc.entry_id,
  oc.number,
  oc.driver,
  oc.codriver,
  e.class,
  e.car,
  oc.total_time,
  -- Calculate championship position within eligible drivers
  RANK() OVER (
    PARTITION BY ce.championship_id, oc.rally_id
    ORDER BY oc.position ASC
  ) AS championship_position,
  oc.time_diff,
  ce.coefficient
FROM championship_events ce
JOIN championships c ON c.id = ce.championship_id
JOIN rallies r ON r.id = ce.rally_id
JOIN overall_classification oc ON oc.rally_id = ce.rally_id
JOIN entries e ON e.id = oc.entry_id
WHERE e.status NOT IN ('retired', 'dnf', 'dsq')
-- CHAMPIONSHIP ELIGIBILITY FILTER
AND EXISTS (
    SELECT 1 FROM championship_eligibility elig 
    WHERE elig.championship_id = ce.championship_id 
    AND (
        -- For Historic championships (numeric classes): EXACT match only
        (c.name LIKE '%Historic%' AND e.class = elig.class_pattern) OR
        -- For other championships: CONTAINS match
        (c.name NOT LIKE '%Historic%' AND (
            e.class = elig.class_pattern OR 
            e.class LIKE '%' || elig.class_pattern || '%'
        ))
    )
);

-- 3. Recreate championship_points view
CREATE VIEW championship_points AS
SELECT
  ce.championship_id,
  c.name AS championship_name,
  e.rally_id,
  r.name AS rally_name,
  e.driver_id,
  pd.first_name || ' ' || pd.last_name AS driver,
  e.id AS entry_id,
  e.number,
  e.class,
  coc.championship_position,
  coc.total_time,
  CASE
    WHEN coc.championship_position = 1 THEN 25 * ce.coefficient
    WHEN coc.championship_position = 2 THEN 18 * ce.coefficient
    WHEN coc.championship_position = 3 THEN 15 * ce.coefficient
    WHEN coc.championship_position = 4 THEN 12 * ce.coefficient
    WHEN coc.championship_position = 5 THEN 10 * ce.coefficient
    WHEN coc.championship_position = 6 THEN 8 * ce.coefficient
    WHEN coc.championship_position = 7 THEN 6 * ce.coefficient
    WHEN coc.championship_position = 8 THEN 4 * ce.coefficient
    WHEN coc.championship_position = 9 THEN 2 * ce.coefficient
    WHEN coc.championship_position = 10 THEN 1 * ce.coefficient
    ELSE 0
  END AS rally_points,
  ce.coefficient
FROM championship_events ce
JOIN championships c ON c.id = ce.championship_id
JOIN rallies r ON r.id = ce.rally_id
JOIN championship_overall_classification coc ON coc.championship_id = ce.championship_id
  AND coc.rally_id = ce.rally_id
JOIN entries e ON e.id = coc.entry_id
JOIN persons pd ON pd.id = e.driver_id;

-- 4. Recreate championship_standings view
CREATE VIEW championship_standings AS
SELECT
  cp.championship_id,
  cp.championship_name,
  cp.driver_id,
  cp.driver,
  COUNT(DISTINCT cp.rally_id) AS rallies_completed,
  SUM(cp.rally_points) AS total_points,
  RANK() OVER (PARTITION BY cp.championship_id ORDER BY SUM(cp.rally_points) DESC) AS position,
  COALESCE(SUM(psp_total.power_stage_points), 0) AS total_power_stage_points,
  SUM(cp.rally_points) + COALESCE(SUM(psp_total.power_stage_points), 0) AS grand_total_points
FROM championship_points cp
LEFT JOIN (
  SELECT
    ce.championship_id,
    e.driver_id,
    SUM(psp.points) AS power_stage_points
  FROM championship_events ce
  JOIN power_stage_points psp ON psp.rally_id = ce.rally_id
  JOIN entries e ON e.id = psp.entry_id
  GROUP BY ce.championship_id, e.driver_id
) psp_total ON psp_total.championship_id = cp.championship_id AND psp_total.driver_id = cp.driver_id
GROUP BY cp.championship_id, cp.championship_name, cp.driver_id, cp.driver;

-- 5. Test the fix
SELECT 'FIXED - Using overall_classification times:' as test;

-- Compare overall vs championship positions
SELECT 'Position comparison:' as comparison;
SELECT 
    oc.position as overall_position,
    oc.driver,
    e.class,
    oc.total_time,
    coc.championship_position,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM championship_eligibility elig 
            WHERE elig.championship_id = '55a003a9-66ff-4a37-b11d-2e14df10bae3'::uuid
            AND (
                e.class = elig.class_pattern OR 
                e.class LIKE '%' || elig.class_pattern || '%'
            )
        ) THEN 'ELIGIBLE'
        ELSE 'NOT ELIGIBLE'
    END as greece_eligibility
FROM overall_classification oc
JOIN entries e ON oc.entry_id = e.id
JOIN rallies r ON oc.rally_id = r.id
LEFT JOIN championship_overall_classification coc ON coc.entry_id = oc.entry_id 
    AND coc.championship_name = 'Greece'
WHERE r.name = 'Rally Stereas Elladas 2025'
ORDER BY oc.position
LIMIT 10;

SELECT 'Greece Championship Results (should now be correct):' as test;
SELECT 
    championship_position,
    driver,
    class,
    total_time
FROM championship_overall_classification
WHERE championship_name = 'Greece' 
AND rally_name = 'Rally Stereas Elladas 2025'
ORDER BY championship_position
LIMIT 10;

SELECT 'Championship positions fixed using overall_classification!' as result;
