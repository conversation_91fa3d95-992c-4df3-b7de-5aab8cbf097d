import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Trophy } from 'lucide-react';
import ChampionshipForm from '../../../../components/admin/championship-form';

const AddChampionshipPage: React.FC = () => {
  const [error, setError] = useState('');
  const navigate = useNavigate();

  const handleSubmit = async (formData: any) => {
    console.log('Submitting championship with data:', formData);
    
    const response = await fetch('/api/championships', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      credentials: 'include',
      body: JSON.stringify(formData)
    });

    // Log the response for debugging
    console.log('Response status:', response.status);
    
    if (!response.ok) {
      const data = await response.json();
      console.error('Error response:', data);
      throw new Error(data.message || data.error || 'Failed to add championship');
    }

    navigate('/admin/championships');
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center mb-6">
        <Trophy className="w-6 h-6 text-red-600 mr-2" />
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Add Championship</h1>
      </div>

      {error && (
        <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          {error}
        </div>
      )}

      <ChampionshipForm onSubmit={handleSubmit} />
    </div>
  );
};

export default AddChampionshipPage;
