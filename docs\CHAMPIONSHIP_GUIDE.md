# Championship Implementation Guide

This guide explains how to use the new championship-aware system that leverages your existing `championship_events` table.

## Overview

The implementation allows multiple championships to share rally events and stages while maintaining separate:
- Results and standings per championship
- Points systems with coefficients
- Class-specific standings
- Championship calendars

## Database Structure

### Core Tables
- `championships` - Championship definitions
- `championship_events` - Links rallies to championships (many-to-many)
- `rallies` - Rally events (unchanged)
- `entries` - Rally entries (unchanged)
- `results` - Stage results (unchanged)

### New Views
- `championship_overall_classification` - Championship-specific rally results
- `championship_points` - Points calculation per rally/championship
- `championship_standings` - Overall championship standings
- `championship_class_standings` - Class-specific championship standings

## API Endpoints

### Get Championships for a Rally
```
GET /api/championship-results/rallies/:rallyId/championships
```
Returns all championships that include this rally.

### Get Championship-Specific Results
```
GET /api/championship-results/rallies/:rallyId/results?championshipId=:id
```
Returns rally results filtered by championship context.

### Get Championship Standings
```
GET /api/championship-results/championships/:championshipId/standings
GET /api/championship-results/championships/:championshipId/standings?class=WRC
```
Returns championship standings (overall or class-specific).

### Get Driver Points Breakdown
```
GET /api/championship-results/championships/:championshipId/drivers/:driverId/points
```
Returns detailed points breakdown for a driver.

### Get Championship Calendar
```
GET /api/championship-results/championships/:championshipId/calendar
```
Returns all rallies in a championship.

## Frontend Components

### ChampionshipSelector
Dropdown to select championship view for rally results:
```tsx
<ChampionshipSelector
  rallyId={rallyId}
  selectedChampionshipId={selectedChampionship}
  onChampionshipChange={setSelectedChampionship}
/>
```

### ChampionshipResultsTable
Display championship-specific rally results:
```tsx
<ChampionshipResultsTable
  results={championshipResults}
  championshipName={championshipName}
  showChampionshipInfo={true}
/>
```

### ChampionshipStandingsTable
Display championship standings:
```tsx
<ChampionshipStandingsTable
  standings={standings}
  isClassStandings={false}
  championshipName={championshipName}
/>
```

## Usage Examples

### 1. Adding a Rally to Multiple Championships
```sql
-- Add Acropolis Rally to multiple championships
INSERT INTO championship_events (championship_id, rally_id, coefficient) VALUES
('wrc-2025-id', 'acropolis-rally-id', 1.0),      -- WRC Championship
('greece-2025-id', 'acropolis-rally-id', 1.5),   -- Greek National (higher points)
('historic-2025-id', 'acropolis-rally-id', 1.0); -- Historic Championship
```

### 2. Getting Championship-Specific Results
```javascript
// Fetch championships for a rally
const championships = await fetch(`/api/championship-results/rallies/${rallyId}/championships`);

// Fetch results for specific championship
const results = await fetch(`/api/championship-results/rallies/${rallyId}/results?championshipId=${championshipId}`);
```

### 3. Points System
The system uses WRC-style points (25, 18, 15, 12, 10, 8, 6, 4, 2, 1) multiplied by the championship coefficient:
- Position 1: 25 × coefficient points
- Position 2: 18 × coefficient points
- etc.

### 4. Class-Specific Championships
```javascript
// Get all classes in a championship
const classes = await fetch(`/api/championship-results/championships/${championshipId}/classes`);

// Get class-specific standings
const classStandings = await fetch(`/api/championship-results/championships/${championshipId}/standings?class=WRC`);
```

## Migration

Run the database migration to create the new views:
```bash
node server/db/run_championship_migration.js
```

## Key Benefits

1. **No Data Duplication**: Same rally data serves multiple championships
2. **Flexible Points**: Different coefficients per championship
3. **Separate Standings**: Independent championship standings
4. **Class Support**: Class-specific championship standings
5. **Backward Compatible**: Existing functionality unchanged

## Real-World Example

A rally like "Acropolis Rally 2025" can be part of:
- WRC Championship (coefficient 1.0)
- Greek National Championship (coefficient 1.5 for higher national points)
- Historic Championship (coefficient 1.0, different eligible classes)

Each championship will have:
- Separate standings
- Different point values (due to coefficients)
- Different eligible classes/entries
- Independent championship calendars

## Next Steps

1. Run the database migration
2. Update your rally details pages to include championship selector
3. Add championship standings pages
4. Update EWRC import to properly link championships
5. Add championship management in admin panel

The system is now ready to handle multiple championships sharing rally events while maintaining separate results and standings for each championship context.
