import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Timer } from 'lucide-react';
import StageForm from '../../../../components/admin/stage-form';

const AddStagePage: React.FC = () => {
  const [error, setError] = useState('');
  const navigate = useNavigate();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const rallyId = queryParams.get('rally_id');

  const handleSubmit = async (formData: any) => {
    console.log('Submitting stage with data:', formData);
    
    const response = await fetch('/api/stages', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      credentials: 'include',
      body: JSON.stringify(formData)
    });

    // Log the response for debugging
    console.log('Response status:', response.status);
    
    if (!response.ok) {
      const data = await response.json();
      console.error('Error response:', data);
      throw new Error(data.message || data.error || 'Failed to add stage');
    }

    // If we came from a rally page, go back there
    if (rallyId) {
      navigate(`/admin/rallies/${rallyId}`);
    } else {
      navigate('/admin/stages');
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center mb-6">
        <Timer className="w-6 h-6 text-red-600 mr-2" />
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Add Stage</h1>
      </div>

      {error && (
        <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          {error}
        </div>
      )}

      <StageForm 
        onSubmit={handleSubmit} 
        preselectedRallyId={rallyId || undefined}
      />
    </div>
  );
};

export default AddStagePage;
