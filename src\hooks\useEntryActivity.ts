import { useState, useEffect } from 'react';

export interface EntryStatusHistory {
  id: string;
  entry_id: string;
  rally_id: string;
  stage_id?: string;
  old_status?: string;
  new_status: string;
  changed_at: string;
  changed_by?: string;
  changed_by_username?: string;
  reason?: string;
  stage_name?: string;
  stage_number?: number;
  rally_name: string;
  entry_number?: number;
  driver_name?: string;
  codriver_name?: string;
}

export interface EntryActivityStats {
  total_entries: number;
  active_entries: number;
  retired_entries: number;
  dns_entries: number;
  dnf_entries: number;
  dsq_entries: number;
}

export const useEntryActivity = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Get entry status history for a specific entry
  const getEntryHistory = async (entryId: string): Promise<EntryStatusHistory[]> => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch(`/api/entry-status-history/entry/${entryId}`);
      if (!response.ok) {
        throw new Error('Failed to fetch entry history');
      }
      const data = await response.json();
      return data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Get entry status history for a rally
  const getRallyHistory = async (rallyId: string): Promise<EntryStatusHistory[]> => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch(`/api/entry-status-history/rally/${rallyId}`);
      if (!response.ok) {
        throw new Error('Failed to fetch rally history');
      }
      const data = await response.json();
      return data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Get active entries for a rally
  const getActiveEntries = async (rallyId: string) => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch(`/api/entry-status-history/active/${rallyId}`);
      if (!response.ok) {
        throw new Error('Failed to fetch active entries');
      }
      const data = await response.json();
      return data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Get retired entries for a rally
  const getRetiredEntries = async (rallyId: string) => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch(`/api/entry-status-history/retired/${rallyId}`);
      if (!response.ok) {
        throw new Error('Failed to fetch retired entries');
      }
      const data = await response.json();
      return data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Get entry activity statistics for a rally
  const getActivityStats = async (rallyId: string): Promise<EntryActivityStats> => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch(`/api/entry-status-history/stats/${rallyId}`);
      if (!response.ok) {
        throw new Error('Failed to fetch activity stats');
      }
      const data = await response.json();
      return data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Add manual status change (admin only)
  const addStatusChange = async (statusChange: {
    entry_id: string;
    rally_id: string;
    stage_id?: string;
    old_status?: string;
    new_status: string;
    reason?: string;
  }) => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch('/api/entry-status-history', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(statusChange),
      });
      
      if (!response.ok) {
        throw new Error('Failed to add status change');
      }
      
      const data = await response.json();
      return data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return {
    loading,
    error,
    getEntryHistory,
    getRallyHistory,
    getActiveEntries,
    getRetiredEntries,
    getActivityStats,
    addStatusChange,
  };
};

// Hook for real-time activity stats
export const useRallyActivityStats = (rallyId: string | null) => {
  const [stats, setStats] = useState<EntryActivityStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { getActivityStats } = useEntryActivity();

  useEffect(() => {
    if (!rallyId) return;

    const fetchStats = async () => {
      setLoading(true);
      try {
        const data = await getActivityStats(rallyId);
        setStats(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, [rallyId]);

  return { stats, loading, error, refetch: () => rallyId && getActivityStats(rallyId).then(setStats) };
};
